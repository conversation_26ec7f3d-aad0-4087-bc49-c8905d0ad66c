﻿#include "stdafx.h"
#include "../iSharePp/mbr.h"
#include "JsonSystem.h"
#include "stringsystem.h"
#include "filesystem.h"
#include "systemfunction.hpp"
#if (defined(_WIN32) || defined(_WIN64))
#include "WindowsFunc.h"
#endif
#include "LogSystem.h"
#include "VHDBootMng.h"
#include "OptionFile.h"
#include "FormatDisk.h"

#define FORMAT_PARA_DISKNO "DiskNo"	//要格式化的磁盘编号0，是第一个符合要求的磁盘
#define FORMAT_PARA_GPT "GPT"
#define FORMAT_PARA_FROMATGB "FormatGB"
#define FORMAT_PARA_PERCENT "Percent"
#define FORMAT_PARA_CLIENTLETTER "ClientLetter"
#define FORMAT_PARA_HIDEDISKMODE "HideDiskMode"
#define FORMAT_PARA_VHDBOOT "VHDBoot"
#define FORMAT_PARA_USERDATA "UserData"
#define FORMAT_PARA_WRITEBACK "Writeback"
#define FORMAT_WRITEBACK_DISK _T("iCacheXDisk")

std::string FormatParaToString(const PFORMATDISKPARAALL pFormatPara)
{
	std::string sResponse;
	if (pFormatPara) {
		boost::json::array lRows;
		for (auto i = 0; i < pFormatPara->nPartitionCount; i++) {
			boost::json::object oRow;
			oRow[FORMAT_PARA_PERCENT] = pFormatPara->aDiskPartitions[i].nPercent;
			oRow[FORMAT_PARA_CLIENTLETTER] = pFormatPara->aDiskPartitions[i].cClientLetter;
			oRow[FORMAT_PARA_HIDEDISKMODE] = pFormatPara->aDiskPartitions[i].nHideDiskMode;
			oRow[FORMAT_PARA_VHDBOOT] = pFormatPara->aDiskPartitions[i].bVHDBootPartition;
			oRow[FORMAT_PARA_USERDATA] = pFormatPara->aDiskPartitions[i].bUserDataPartition;
			oRow[FORMAT_PARA_WRITEBACK] = pFormatPara->aDiskPartitions[i].bWritebackPartition;
			lRows.push_back(oRow);
		}
		boost::json::object dDocRsp;
		SetJsonRows(dDocRsp, lRows);
		dDocRsp[FORMAT_PARA_DISKNO] = pFormatPara->nDiskNo;
		dDocRsp[FORMAT_PARA_GPT] = pFormatPara->bGPT;
		dDocRsp[FORMAT_PARA_FROMATGB] = pFormatPara->bFormatGB;
		sResponse = boost::json::serialize(dDocRsp);
	}
	return sResponse;
}

bool FormatParaParse(const std::string& sFormatCommand, PFORMATDISKPARAALL pFormatPara) {
	if (sFormatCommand.empty() || !pFormatPara) {
		return false;
	}

#if BOOST_VERSION >= 108700
	boost::system::error_code ec;
#else
	boost::json::error_code ec;
#endif
	auto dDocReq = boost::json::parse(sFormatCommand, ec);
	if (ec || !dDocReq.is_object() || dDocReq.as_object().empty()) {
		return false;
	}

	boost::json::object& oFormatPara = dDocReq.get_object();
	parseJsonField(oFormatPara, FORMAT_PARA_DISKNO, pFormatPara->nDiskNo);
	parseJsonField(oFormatPara, FORMAT_PARA_GPT, pFormatPara->bGPT);
	parseJsonField(oFormatPara, FORMAT_PARA_FROMATGB, pFormatPara->bFormatGB);

	int64_t nPartitions = 0;
	if (!parseJsonField(oFormatPara, WEBSOCKET_JSON_COMMAND_TOTAL, nPartitions)
		|| nPartitions > FORMAT_DISK_PARTITIONS_MAX) {
		return false;
	}

	auto iterCommand = caseInsensitiveFind(oFormatPara, WEBSOCKET_JSON_COMMAND_ROWS);
	if (iterCommand == oFormatPara.end() || !iterCommand->value().is_array()) {
		return false;
	}

	int i = 0;
	for (const auto& partition : iterCommand->value().get_array()) {
		if (!partition.is_object() || i >= nPartitions) {
			continue;
		}

		auto& partitionObj = partition.get_object();
		auto& diskPartition = pFormatPara->aDiskPartitions[i];

		parseJsonField(partitionObj, FORMAT_PARA_PERCENT, diskPartition.nPercent);
		parseJsonField(partitionObj, FORMAT_PARA_CLIENTLETTER, diskPartition.cClientLetter);
		parseJsonField(partitionObj, FORMAT_PARA_HIDEDISKMODE, diskPartition.nHideDiskMode);
		parseJsonField(partitionObj, FORMAT_PARA_VHDBOOT, diskPartition.bVHDBootPartition);
		parseJsonField(partitionObj, FORMAT_PARA_USERDATA, diskPartition.bUserDataPartition);
		parseJsonField(partitionObj, FORMAT_PARA_WRITEBACK, diskPartition.bWritebackPartition);

		i++;
	}

	pFormatPara->nPartitionCount = i;
	return i > 0;
}

std::string FormatDiskScript(int nDiskID, DWORD dwCapacityMB, FORMATDISKPARAALL* pFormatPara)
{
	std::string sFormatScript;
	sFormatScript = string_format("select disk %d\r\n", nDiskID);
	sFormatScript += "clean\r\n";
	//https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/automount
	sFormatScript += "automount scrub noerr\r\n";
	sFormatScript += "automount enable noerr\r\n";
	bool bActive(false);
	char letter = 'P';
	if (dwCapacityMB > FORMAT_DISK_FAT32_MB && FORMAT_PARA_LEGACY_ONLY != pFormatPara->bGPT) {
		if (FORMAT_PARA_UEFI_ONLY == pFormatPara->bGPT || FORMAT_PARA_EXT4_ONLY == pFormatPara->bGPT) {
			//https://docs.microsoft.com/en-us/windows-hardware/manufacture/desktop/configure-uefigpt-based-hard-drive-partitions
			sFormatScript += "convert gpt\r\n";
			sFormatScript += string_format("create partition efi size=%d noerr\r\n", FORMAT_DISK_FAT32_MB);
		}
		else {
			sFormatScript += "convert mbr\r\n";
			sFormatScript += string_format("create partition primary size=%d noerr\r\n", FORMAT_DISK_FAT32_MB);
			sFormatScript += "active\r\n"; //激活该分区
			bActive = true;
			dwCapacityMB = (std::min)(dwCapacityMB, (DWORD)FORMAT_DISK_MBR_MAXCAPACITY_MB); // mbr分区最大容量
		}
		sFormatScript += "format quick fs=FAT32 label=\"bcd_boot\"\r\n";
		sFormatScript += string_format("assign letter=%c noerr\r\n", letter);
		pFormatPara->cFat32Letter = letter;
		letter++;
		dwCapacityMB -= FORMAT_DISK_FAT32_MB;
	}
	else {
		//MBR格式化时，容量小于2GB，创建一个FAT32分区
		sFormatScript += "convert mbr\r\n";
	}
	DWORD dwAllPercent = pFormatPara->bFormatGB ? dwCapacityMB : 100;
	for (auto i = 0; i < pFormatPara->nPartitionCount; i++) {
		if (pFormatPara->aDiskPartitions[i].nPercent > 0 && dwAllPercent > 0) {
			auto dwLeftPercent = (std::min)(pFormatPara->aDiskPartitions[i].nPercent * (pFormatPara->bFormatGB ? 1024 : 1), dwAllPercent);
			if (i == pFormatPara->nPartitionCount - 1) {
				//最后一个分区
				sFormatScript += "create partition primary noerr\r\n";
			}
			else {
				if (pFormatPara->bFormatGB) {
					//按照容量格式化
					sFormatScript += string_format("create partition primary size=%d noerr\r\n", dwLeftPercent);
				}
				else {
					sFormatScript += string_format("create partition primary size=%d noerr\r\n", dwCapacityMB / 100 * dwLeftPercent);
				}
			}
			if (FORMAT_PARA_UEFI_ONLY != pFormatPara->bGPT
				&& !bActive && pFormatPara->aDiskPartitions[i].bVHDBootPartition) {
				sFormatScript += "active\r\n"; //激活该分区
				bActive = true;
			}
			sFormatScript += string_format("format quick fs=ntfs label=\"%s\"\r\n",
				(pFormatPara->aDiskPartitions[i].bVHDBootPartition ? "vhd_boot" : (pFormatPara->aDiskPartitions[i].bUserDataPartition ? "user_data" : (pFormatPara->aDiskPartitions[i].bWritebackPartition ? "write_back" : ""))));
			sFormatScript += string_format("assign letter=%c noerr\r\n", letter);
			pFormatPara->aDiskPartitions[i].cRealLetter = letter;
			if (bActive && !pFormatPara->cFat32Letter) {
				pFormatPara->cFat32Letter = letter;
			}
			letter++;
			dwAllPercent -= dwLeftPercent;
		}
		else {
			break;
		}
	}
	sFormatScript += "exit\r\n";
	return sFormatScript;
}
//假设磁盘设备为/dev/sda, /dev/sdb, /dev/nvme0n1等
std::string FormatDiskScript(const std::string& nDiskID, DWORD dwCapacityMB, FORMATDISKPARAALL* pFormatPara)
{
#if !(defined(__x86_64__) || defined(_M_X64))
	//非x64平台仅支持UEFI
	pFormatPara->bGPT = FORMAT_PARA_EXT4_ONLY;
#endif

	std::string sFormatScript;
	sFormatScript = "#!/bin/bash\n";
	sFormatScript += "PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin\n";
	sFormatScript += "export PATH\n";
	//增加语言环境,避免乱码
	sFormatScript += "export LC_ALL=C.UTF-8\n";
	sFormatScript += "export LANG=C.UTF-8\n";
	sFormatScript += string_format("disk=%s\n", nDiskID.c_str());
	sFormatScript += "max_attempts=3\n";
	sFormatScript += "sleep_duration=3\n";
	//使用 sfdisk 保存磁盘第一分区的 uuid
	//sFormatScript += "partuuid=$(sfdisk --part-uuid $disk 1)\n";
	//# 检查设备名是否以数字结尾
	sFormatScript += "case $disk in\n";
	sFormatScript += "*[0-9]) part_suffix=\"p\" ;;\n";
	sFormatScript += "*) part_suffix=\"\" ;;\n";
	sFormatScript += "esac\n";
	sFormatScript += "attempts=0\n";
	//# 使用umount命令卸载该磁盘的所有分区
	sFormatScript += "while mount | grep -q \"${disk}\" && [ $attempts -lt $max_attempts ]; do\n";
	sFormatScript += "    mountpoints=$(mount | grep \"^${disk}\" | awk '{print $3}')\n";
	sFormatScript += "    echo \"Found mountpoints for ${disk}: ${mountpoints}\"\n";
	sFormatScript += "    for mountpoint in $mountpoints; do\n";
	sFormatScript += "        echo \"Unmounting ${mountpoint}\"\n";
	sFormatScript += "        umount $mountpoint\n";
	sFormatScript += "        if [ $? -eq 0 ]; then\n";
	sFormatScript += "            echo \"Successfully unmounted ${mountpoint}\"\n";
	sFormatScript += "        else\n";
	sFormatScript += "            echo \"Failed to unmount ${mountpoint}\"\n";
	sFormatScript += "        fi\n";
	sFormatScript += "    done\n";
	sFormatScript += "    if mount | grep -q \"${disk}\"; then\n";
	sFormatScript += "        echo \"Failed to unmount ${disk} after ${attempts} attempts\"\n";
	sFormatScript += "        echo \"Killing processes that are using ${disk}\"\n";
	sFormatScript += "        fuser -km $disk\n";
	sFormatScript += "        if [ $? -eq 0 ]; then\n";
	sFormatScript += "            echo \"Successfully killed processes using ${disk}\"\n";
	sFormatScript += "        else\n";
	sFormatScript += "            echo \"Failed to kill processes using ${disk}\"\n";
	sFormatScript += "        fi\n";
	sFormatScript += "        sleep $sleep_duration\n";
	sFormatScript += "    fi\n";
	sFormatScript += "    attempts=$((attempts+1))\n";
	sFormatScript += "done\n";
	sFormatScript += "if mount | grep -q \"${disk}\"; then\n";
	sFormatScript += "    echo \"Failed to unmount ${disk}\"\n";
	sFormatScript += "    echo \"Exiting\"\n";
	sFormatScript += "    exit 1\n";
	sFormatScript += "fi\n";
	//如果 mountpoints 不为空，则 partprobe
	sFormatScript += "if [ -n \"$mountpoints\" ]; then\n";
	sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
	sFormatScript += "    partprobe $disk\n";
	sFormatScript += "    sleep $sleep_duration\n";
	sFormatScript += "fi\n";

	//使用parted命令创建分区表
	if (FORMAT_PARA_UEFI_ONLY == pFormatPara->bGPT || FORMAT_PARA_EXT4_ONLY == pFormatPara->bGPT) {
		sFormatScript += "parted -s $disk mklabel gpt\n"; //使用parted命令创建GPT分区表
	}
	else {
		sFormatScript += "parted -s $disk mklabel msdos\n"; //使用parted命令创建MBR分区表
		dwCapacityMB = (std::min)(dwCapacityMB, (DWORD)FORMAT_DISK_MBR_MAXCAPACITY_MB); // mbr分区最大容量
	}
	//检查错误
	sFormatScript += "if [ $? -eq 0 ]; then\n";
	sFormatScript += "    echo \"Successfully created partition table on ${disk}\"\n";
	sFormatScript += "else\n";
	sFormatScript += "    echo \"Failed to create partition table on ${disk}\"\n";
	sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
	sFormatScript += "    partprobe $disk\n";
	sFormatScript += "    sleep $sleep_duration\n";
	sFormatScript += "fi\n";

	//设置错误标志
	sFormatScript += "error_flag=0\n";

	bool bActive(false);
	char letter = 'P';
	//剩余的分区开始起点
	DWORD dwStartPartMB = 1;
	//剩余的容量(MB)或者百分比
	DWORD dwLeftMB = dwCapacityMB - 1;
	//创建一个启动分区
	if (dwCapacityMB > FORMAT_DISK_FAT32_MB && FORMAT_PARA_LEGACY_ONLY != pFormatPara->bGPT) {
		sFormatScript += string_format("parted -s $disk mkpart primary fat32 %dMiB %dMiB\n", dwStartPartMB, dwStartPartMB + FORMAT_DISK_FAT32_MB);
		//检查错误
		sFormatScript += "if [ $? -eq 0 ]; then\n";
		sFormatScript += "    echo \"Successfully created FAT32 partition on ${disk}\"\n";
		sFormatScript += "else\n";
		sFormatScript += "    echo \"Failed to create FAT32 partition on ${disk}\"\n";
		sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
		sFormatScript += "    partprobe $disk\n";
		sFormatScript += "    sleep $sleep_duration\n";
		sFormatScript += "fi\n";

		sFormatScript += "parted -s $disk set 1 boot on\n";
		//检查错误
		sFormatScript += "if [ $? -eq 0 ]; then\n";
		sFormatScript += "    echo \"Successfully set boot flag on ${disk}\"\n";
		sFormatScript += "else\n";
		sFormatScript += "    echo \"Failed to set boot flag on ${disk}\"\n";
		sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
		sFormatScript += "    partprobe $disk\n";
		sFormatScript += "    sleep $sleep_duration\n";
		sFormatScript += "fi\n";

		if (FORMAT_PARA_UEFI_ONLY == pFormatPara->bGPT
			|| FORMAT_PARA_EXT4_ONLY == pFormatPara->bGPT) {
			//分区类型为EFI系统分区
			sFormatScript += "parted -s $disk set 1 esp on\n";
			//检查错误
			sFormatScript += "if [ $? -eq 0 ]; then\n";
			sFormatScript += "    echo \"Successfully set esp flag on ${disk}\"\n";
			sFormatScript += "else\n";
			sFormatScript += "    echo \"Failed to set esp flag on ${disk}\"\n";
			sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
			sFormatScript += "    partprobe $disk\n";
			sFormatScript += "    sleep $sleep_duration\n";
			sFormatScript += "fi\n";
		}
		//如果 partuuid 不为空，则恢复第一个分区的 uuid
		//sFormatScript += "if [ -n \"$partuuid\" ]; then\n";
		//sFormatScript += "sfdisk --part-uuid $disk 1 $partuuid\n";
		//sFormatScript += "fi\n";
		sFormatScript += "partprobe $disk\n";
		sFormatScript += "sleep $sleep_duration\n";
		//格式化该分区, -F32 表示创建的是 FAT32 类型的文件系统，
		// -I：这个参数通常用于格式化没有分区表的设备，或者当您想要在整个设备上而不是某个分区上创建文件系统时使用。它会忽略设备上的分区表，并在整个设备上创建文件系统。
		// -I 选项已经允许您在整个设备上而不是某个分区上创建文件系统，这本身就是一种强制行为。然而，如果您想要确保即使在分区已经被挂载的情况下也进行格式化，您可以使用 -I 选项来实现这一点。
		sFormatScript += "attempts=0\n";
		sFormatScript += "while [ $attempts -lt $max_attempts ]; do\n";
		sFormatScript += "    mkfs.fat -F32 -n \"EFI\" -I ${disk}${part_suffix}1\n";
		//检查错误
		sFormatScript += "    if [ $? -eq 0 ]; then\n";
		sFormatScript += "        echo \"Successfully formatted FAT32 partition on ${disk}${part_suffix}1\"\n";
		sFormatScript += "        break\n";
		sFormatScript += "    else\n";
		sFormatScript += "        echo \"Failed to format FAT32 partition on ${disk}${part_suffix}1\"\n";
		sFormatScript += "        attempts=$((attempts+1))\n";
		sFormatScript += "        sleep $sleep_duration\n";
		sFormatScript += "    fi\n";
		sFormatScript += "done\n";
		sFormatScript += "if [ $attempts -eq $max_attempts ]; then\n";
		sFormatScript += "    error_flag=1\n";
		sFormatScript += "fi\n";
		sFormatScript += string_format("mkdir -p /mnt/%c\n", letter); //创建一个挂载点目录
		sFormatScript += "attempts=0\n";
		sFormatScript += "while [ $attempts -lt $max_attempts ]; do\n";
		sFormatScript += string_format("    mount ${disk}${part_suffix}1 /mnt/%c\n", letter); //挂载该分区到目录
		//检查错误
		sFormatScript += "    if [ $? -eq 0 ]; then\n";
		sFormatScript += string_format("        echo \"Successfully mounted FAT32 partition /mnt/%c on ${disk}${part_suffix}1\"\n", letter);
		sFormatScript += "        break\n";
		sFormatScript += "    else\n";
		sFormatScript += string_format("        echo \"Failed to mount FAT32 partition /mnt/%c on ${disk}${part_suffix}1\"\n", letter);
		sFormatScript += "        attempts=$((attempts+1))\n";
		sFormatScript += "        sleep $sleep_duration\n";
		sFormatScript += "    fi\n";
		sFormatScript += "done\n";
		sFormatScript += "if [ $attempts -eq $max_attempts ]; then\n";
		sFormatScript += "    error_flag=1\n";
		sFormatScript += "fi\n";
		pFormatPara->cFat32Letter = letter;
		letter++;
		dwLeftMB -= FORMAT_DISK_FAT32_MB;
		dwStartPartMB += FORMAT_DISK_FAT32_MB;
		bActive = true;
	}

#define FORMAT_DISK_PARTED_CREATE_NTFS "parted -s $disk mkpart primary ntfs %dMiB %dMiB\n"
	//--fast：这个选项用于执行快速格式化 --force 选项将强制格式化 --label 选项用于指定卷标 --no-indexing 选项用于禁用索引 --cluster-size 选项用于指定簇大小
#define FORMAT_DISK_PARTED_FORMAT_NTFS "mkntfs --fast --force --label %s --cluster-size 65536 ${disk}${part_suffix}%d\n"
#define FORMAT_DISK_PARTED_CREATE_EXT4 "parted -s $disk mkpart primary ext4 %dMiB %dMiB\n"
#define FORMAT_DISK_PARTED_FORMAT_EXT4 "mkfs.ext4 -F -L %s ${disk}${part_suffix}%d\n"
#define FORMAT_DISK_PARTED_FORMAT_MKE2FS "mke2fs -F -L %s ${disk}${part_suffix}%d\n"

	for (auto i = 0; i < pFormatPara->nPartitionCount; i++) {
		if (pFormatPara->aDiskPartitions[i].nPercent > 0 && dwLeftMB > 0) {
			//需要格式化的容量MB
			auto dwPartMB = (std::min)(pFormatPara->aDiskPartitions[i].nPercent * (pFormatPara->bFormatGB ? 1024 : (dwCapacityMB / 100)), dwLeftMB);
			if (i == pFormatPara->nPartitionCount - 1) {
				//最后一个分区
				sFormatScript += string_format((FORMAT_PARA_EXT4_ONLY != pFormatPara->bGPT) ? FORMAT_DISK_PARTED_CREATE_NTFS : FORMAT_DISK_PARTED_CREATE_EXT4, dwStartPartMB, dwCapacityMB - 1);
			}
			else {
				sFormatScript += string_format((FORMAT_PARA_EXT4_ONLY != pFormatPara->bGPT) ? FORMAT_DISK_PARTED_CREATE_NTFS : FORMAT_DISK_PARTED_CREATE_EXT4, dwStartPartMB, dwStartPartMB + dwPartMB);
			}
			//检查错误
			sFormatScript += "if [ $? -eq 0 ]; then\n";
			sFormatScript += "    echo \"Successfully created partition on ${disk}\"\n";
			sFormatScript += "else\n";
			sFormatScript += "    echo \"Failed to create partition on ${disk}\"\n";
			sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
			sFormatScript += "    partprobe $disk\n";
			sFormatScript += "    sleep $sleep_duration\n";
			sFormatScript += "fi\n";
			int nPartIndex = (letter - 'P') + 1;
			if (FORMAT_PARA_UEFI_ONLY != pFormatPara->bGPT
				&& !bActive && pFormatPara->aDiskPartitions[i].bVHDBootPartition) {
				sFormatScript += string_format("parted -s $disk set %d boot on\n", nPartIndex); //设置该分区为启动分区
				//检查错误
				sFormatScript += "if [ $? -eq 0 ]; then\n";
				sFormatScript += string_format("    echo \"Successfully set boot flag on ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "else\n";
				sFormatScript += string_format("    echo \"Failed to set boot flag on ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "    echo \"Attempting to notify kernel of partition changes\"\n";
				sFormatScript += "    partprobe $disk\n";
				sFormatScript += "    sleep $sleep_duration\n";
				sFormatScript += "fi\n";
				bActive = true;
			}
			sFormatScript += "partprobe $disk\n";
			sFormatScript += "sleep $sleep_duration\n";
			const char* sLabel = (pFormatPara->aDiskPartitions[i].bVHDBootPartition ? "vhd_boot" : (pFormatPara->aDiskPartitions[i].bUserDataPartition ? "user_data" : (pFormatPara->aDiskPartitions[i].bWritebackPartition ? "write_back" : "none")));
			if (FORMAT_PARA_EXT4_ONLY != pFormatPara->bGPT) {
				//格式化该分区为NTFS文件系统
				sFormatScript += "attempts=0\n";
				sFormatScript += "while [ $attempts -lt $max_attempts ]; do\n";
				sFormatScript += string_format("    " FORMAT_DISK_PARTED_FORMAT_NTFS, sLabel, nPartIndex);
				//检查错误
				sFormatScript += "    if [ $? -eq 0 ]; then\n";
				sFormatScript += string_format("        echo \"Successfully formatted partition on ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "        break\n";
				sFormatScript += "    else\n";
				sFormatScript += string_format("        echo \"Failed to format partition on ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "        attempts=$((attempts+1))\n";
				sFormatScript += "        sleep $sleep_duration\n";
				sFormatScript += "    fi\n";
				sFormatScript += "done\n";
				sFormatScript += "if [ $attempts -eq $max_attempts ]; then\n";
				sFormatScript += "    error_flag=1\n";
				sFormatScript += "fi\n";
			}
			else {
				//格式化该分区为ext4文件系统
				sFormatScript += "attempts=0\n";
				sFormatScript += "while [ $attempts -lt $max_attempts ]; do\n";
				sFormatScript += string_format("    " FORMAT_DISK_PARTED_FORMAT_EXT4, sLabel, nPartIndex);
				sFormatScript += "    if [ $? -ne 0 ]; then\n";
				sFormatScript += "        echo \"mkfs.ext4 failed, trying mke2fs...\"\n";
				sFormatScript += string_format("        " FORMAT_DISK_PARTED_FORMAT_MKE2FS, sLabel, nPartIndex);
				sFormatScript += "        if [ $? -eq 0 ]; then\n";
				sFormatScript += string_format("            echo \"Successfully formatted partition ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "            break\n";
				sFormatScript += "        else\n";
				sFormatScript += string_format("            echo \"Failed to format partition ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "            attempts=$((attempts+1))\n";
				sFormatScript += "            sleep $sleep_duration\n";
				sFormatScript += "        fi\n";
				sFormatScript += "    else\n";
				sFormatScript += string_format("        echo \"Successfully formatted partition ${disk}${part_suffix}%d\"\n", nPartIndex);
				sFormatScript += "        break\n";
				sFormatScript += "    fi\n";
				sFormatScript += "done\n";
				sFormatScript += "if [ $attempts -eq $max_attempts ]; then\n";
				sFormatScript += "    error_flag=1\n";
				sFormatScript += "fi\n";
			}

			sFormatScript += string_format("mkdir -p /mnt/%c\n", letter); //创建一个挂载点目录
			sFormatScript += "attempts=0\n";
			sFormatScript += "while [ $attempts -lt $max_attempts ]; do\n";
			if (FORMAT_PARA_EXT4_ONLY != pFormatPara->bGPT) {
				sFormatScript += string_format("    ntfs-3g ${disk}${part_suffix}%d /mnt/%c\n", nPartIndex, letter); //挂载该分区到目录
			}
			else {
				sFormatScript += string_format("    mount ${disk}${part_suffix}%d /mnt/%c\n", nPartIndex, letter); //挂载该分区到目录
			}
			//检查错误
			sFormatScript += "    if [ $? -eq 0 ]; then\n";
			sFormatScript += string_format("        echo \"Successfully mounted partition /mnt/%c on ${disk}${part_suffix}%d\"\n", letter, nPartIndex);
			sFormatScript += "        break\n";
			sFormatScript += "    else\n";
			sFormatScript += string_format("        echo \"Failed to mount partition /mnt/%c on ${disk}${part_suffix}%d\"\n", letter, nPartIndex);
			sFormatScript += "        attempts=$((attempts+1))\n";
			sFormatScript += "        sleep $sleep_duration\n";
			sFormatScript += "    fi\n";
			sFormatScript += "done\n";
			sFormatScript += "if [ $attempts -eq $max_attempts ]; then\n";
			sFormatScript += "    error_flag=1\n";
			sFormatScript += "fi\n";
			pFormatPara->aDiskPartitions[i].cRealLetter = letter;
			if (bActive && !pFormatPara->cFat32Letter) {
				pFormatPara->cFat32Letter = letter;
			}
			letter++;
			dwLeftMB -= dwPartMB;
			dwStartPartMB += dwPartMB;
		}
		else {
			break;
		}
	}
	sFormatScript += "if [ $error_flag -eq 1 ]; then\n";
	sFormatScript += "    echo \"Failed to format disk ${disk}\"\n";
	sFormatScript += "    exit 1\n";
	sFormatScript += "else\n";
	sFormatScript += "    echo \"Successfully formatted disk ${disk}\"\n";
	sFormatScript += "fi\n";
	sFormatScript += "exit 0\n";
	return sFormatScript;
}

//隐藏磁盘, 返回vhd启动盘符
#if (defined(_WIN32) || defined(_WIN64))
std::wstring HideDiskAfterFormat(std::wstring sNewDiskLetter, FORMATDISKPARAALL* pFormatPara, std::wstring* sBcdVolume)
{
	std::wstring sNewVolume;
	//使用新的方法标记各个分区的用途
	if (sNewDiskLetter.size() >= pFormatPara->nPartitionCount
		&& pFormatPara->nPartitionCount) {
		//隐藏引导分区
		if (pFormatPara->cFat32Letter) {
			auto sVolumeName(wstring_format(_T("%C:\\"), pFormatPara->cFat32Letter));
			HideDisk(sVolumeName, FORMATDISK_HIDEDISK, false, NULL);
			if (sBcdVolume) *sBcdVolume = sVolumeName;
		}
		for (auto i = 0; i < pFormatPara->nPartitionCount; i++) {
			if (sNewDiskLetter.find(pFormatPara->aDiskPartitions[i].cRealLetter) != std::wstring::npos) {
				auto sVolumeName(wstring_format(_T("%C:\\"), pFormatPara->aDiskPartitions[i].cRealLetter));
				if (pFormatPara->aDiskPartitions[i].bVHDBootPartition) {
					//启动分区
					sNewVolume = sVolumeName;
				}
				if (pFormatPara->aDiskPartitions[i].bUserDataPartition) {
					//用户数据分区
					auto sUserData = boost::filesystem::absolute(_T(FORMAT_PARA_USERDATA), sVolumeName);
					MakeDirectory(sUserData);
					HideDirectory(sUserData);
				}
				if (pFormatPara->aDiskPartitions[i].bWritebackPartition) {
					//回写分区
					auto siCacheXDisk = boost::filesystem::absolute(FORMAT_WRITEBACK_DISK, sVolumeName);
					MakeFile(siCacheXDisk);
					HideFile(siCacheXDisk);
				}
				HideDisk(sVolumeName, pFormatPara->aDiskPartitions[i].nHideDiskMode,
					false, &pFormatPara->aDiskPartitions[i].cClientLetter);
			}
		}
	}
	else if (sNewDiskLetter.size()) {
		//未格式化，直接引用
		for (auto i = 0; i < sNewDiskLetter.size(); i++) {
			auto tempVolume = wstring_format(_T("%C:\\"), sNewDiskLetter[i]);
			if (CVHDBootMng::IsVHDBootVolume(tempVolume)) {
				sNewVolume = tempVolume;
				break;
			}
		}
		if (sNewVolume.empty()) {
			auto j = sNewDiskLetter.size() > 1 ? 1 : 0;
			sNewVolume = wstring_format(_T("%C:\\"), sNewDiskLetter[j]);
		}
	}
	return sNewVolume;
}

bool FormatDisk_Auto(int nDiskID, DWORD dwCapacityMB, FORMATDISKPARAALL* pFormatPara)
{
	auto sFormatScript = FormatDiskScript(nDiskID, dwCapacityMB, pFormatPara);
#ifdef _DEBUG
	return RunDiskpartScript(sFormatScript, TRUE);
#else
	return RunDiskpartScript(sFormatScript, FALSE);
#endif // DEBUG
}

std::wstring DiskIDString(int nDiskID)
{
	return wstring_format(_T("\\\\.\\PhysicalDrive%d"), nDiskID);
}

std::wstring DiskLetters(const std::wstring& sNewDiskLetter)
{
	std::wstring sLetters;
	for (auto j = 0; j < sNewDiskLetter.size(); j++) {
		sLetters += wstring_format(_T("%C: "), sNewDiskLetter[j]);
	}
	return sLetters;
}

#else

std::string HideDiskAfterFormat(std::vector<std::string> sNewDiskLetter, FORMATDISKPARAALL* pFormatPara, std::string* pstrBCDVolume)
{
	std::string sNewVolume, sVolumeName;
	//使用新的方法标记各个分区的用途
	if (sNewDiskLetter.size() >= pFormatPara->nPartitionCount
		&& pFormatPara->nPartitionCount) {
		//隐藏引导分区
		if (pFormatPara->cFat32Letter) {
			sVolumeName = string_format("/mnt/%c", pFormatPara->cFat32Letter);
			HideDisk(sVolumeName, FORMATDISK_HIDEDISK, false, NULL);
			if (pstrBCDVolume) {
				*pstrBCDVolume = sVolumeName;
			}
		}
		for (auto i = 0; i < pFormatPara->nPartitionCount; i++) {
			sVolumeName = string_format("/mnt/%c", pFormatPara->aDiskPartitions[i].cRealLetter);
			if (std::find(sNewDiskLetter.begin(), sNewDiskLetter.end(), sVolumeName) != sNewDiskLetter.end()) {
				if (pFormatPara->aDiskPartitions[i].bVHDBootPartition) {
					//启动分区
					sNewVolume = sVolumeName;
				}
				if (pFormatPara->aDiskPartitions[i].bUserDataPartition) {
					//用户数据分区
					auto sUserData = boost::filesystem::absolute(_T(FORMAT_PARA_USERDATA), sVolumeName);
					MakeDirectory(sUserData);
					HideDirectory(sUserData);
				}
				if (pFormatPara->aDiskPartitions[i].bWritebackPartition) {
					//回写分区
					auto siCacheXDisk = boost::filesystem::absolute(FORMAT_WRITEBACK_DISK, sVolumeName);
					MakeFile(siCacheXDisk);
					HideFile(siCacheXDisk);
				}
				HideDisk(sVolumeName, pFormatPara->aDiskPartitions[i].nHideDiskMode,
					false, &pFormatPara->aDiskPartitions[i].cClientLetter);
			}
		}
	}
	else if (sNewDiskLetter.size()) {
		//未格式化，直接引用
		for (auto i = 0; i < sNewDiskLetter.size(); i++) {
			if (CVHDBootMng::IsVHDBootVolume(sNewDiskLetter[i])) {
				sNewVolume = sNewDiskLetter[i];
				break;
			}
		}
		if (sNewVolume.empty()) {
			sNewVolume = sNewDiskLetter.size() > 1 ? sNewDiskLetter[1] : sNewDiskLetter[0];
		}
	}
	return sNewVolume;
}

bool FormatDisk_Auto(const std::string& nDiskID, DWORD dwCapacityMB, FORMATDISKPARAALL* pFormatPara)
{
	auto sFormatScript = FormatDiskScript(nDiskID, dwCapacityMB, pFormatPara);
	WRITE_ISCSIFILELOG(_T("FormatDisk_Auto FormatScript: \n") << sFormatScript.c_str());
	auto sLogFile = boost::filesystem::absolute(_T("FormatDiskLog.txt"), GetModuleFolder());
	auto ret = RunScript(sFormatScript, true, sLogFile.string());
	std::string sLogFileContent;
	ReadTxtFile_ByCMarkup(sLogFile, sLogFileContent);
	WRITE_ISCSIFILELOG(_T("FormatDisk_Auto RunScript ret: ") << ret << _T(", LogFile: \n") << sLogFileContent.c_str());
	return ret;
}

std::wstring DiskIDString(const std::string& nDiskID)
{
	return s2ws(nDiskID);
}

std::wstring DiskLetters(const std::vector<std::string>& sNewDiskLetter)
{
	std::wstring sLetters;
	for (auto j = 0; j < sNewDiskLetter.size(); j++) {
		sLetters += s2ws(sNewDiskLetter[j]) + _T(" ");
	}
	return sLetters;
}

#endif

bool IsHidedDiskMark_(BYTE bMark) {
	return (bMark == HIDN_DISK_LETTER
		|| bMark == HIDN_DISK_DISKLESS_LETTER
		|| bMark == HIDN_DISK_VHDBOOT_LETTER) ? true : false;
}

//标记磁盘为隐藏,自动，或者指定盘符
bool HideDisk_(const boost::filesystem::path& cVolumeLetter, int nHideDiskMode, bool bKeepOldLetter, TCHAR* cNewLetter)
{
	bool ret(false);
	auto hDisk = OpenBinaryFile(cVolumeLetter);
	if (hDisk != boost::interprocess::ipcdetail::invalid_file()) {
		std::vector<BYTE> buffer(DBR_SIZE, 0);
		if (ReadBinaryFile(hDisk, buffer.data(), buffer.size(), 0)) {
			BYTE bNewLetter(0);
			if (nHideDiskMode) {
				//隐藏分区
				switch (nHideDiskMode) {
				case FORMATDISK_HIDEDISK_DISKLESS:
					bNewLetter = HIDN_DISK_DISKLESS_LETTER;
					break;
				case FORMATDISK_HIDEDISK_VHDBOOT:
					bNewLetter = HIDN_DISK_VHDBOOT_LETTER;
					break;
				case FORMATDISK_HIDEDISK:
				default:
					bNewLetter = HIDN_DISK_LETTER;
					break;
				}
			}
			else {
				//是否恢复原来的隐藏
				bool bOldHide = IsHidedDiskMark_(buffer[CLIENTLETTER_OFFSET]);
				if (bKeepOldLetter && !bOldHide) {
					//保留原来的盘符设置
					bNewLetter = buffer[CLIENTLETTER_OFFSET];
				}
				else if (cNewLetter) {
					bNewLetter = *cNewLetter;
				}
			}
			buffer[CLIENTLETTER_OFFSET] = bNewLetter;
			ret = WriteBinaryFile(hDisk, buffer.data(), buffer.size(), 0);
		}
		CloseBinaryFile(hDisk);
	}
	return ret;
}

bool DiskHided_(const boost::filesystem::path& sVolumeRoot)
{
	bool ret(false);
	auto hDisk = OpenBinaryFile(sVolumeRoot, true);
	if (hDisk != boost::interprocess::ipcdetail::invalid_file()) {
		std::vector<BYTE> buffer(DBR_SIZE, 0);
		if (ReadBinaryFile(hDisk, buffer.data(), buffer.size(), 0)) {
			ret = IsHidedDiskMark_(buffer[CLIENTLETTER_OFFSET]);//隐藏该分区
		}
		CloseBinaryFile(hDisk);
	}
	return ret;
}

bool Diskletter_(const boost::filesystem::path& sVolumeDevicePath, TCHAR& cClientLetter, bool bRead)
{
	bool ret(false);
	if (!bRead && !IsHidedDiskletter(cClientLetter)) {
		string_makeupper(cClientLetter);
	}
	if (bRead || IsGoodDiskletterSet(cClientLetter)) {
		auto hDisk = OpenBinaryFile(sVolumeDevicePath);
		if (hDisk != boost::interprocess::ipcdetail::invalid_file()) {
			std::vector<BYTE> buffer(DBR_SIZE, 0);
			ret = ReadBinaryFile(hDisk, buffer.data(), buffer.size(), 0);
			if (ret) {
				if (bRead) {
					cClientLetter = buffer[CLIENTLETTER_OFFSET];
				}
				else {
					buffer[CLIENTLETTER_OFFSET] = cClientLetter;//隐藏该分区
					ret = WriteBinaryFile(hDisk, buffer.data(), buffer.size(), 0);
				}
			}
			CloseBinaryFile(hDisk);
		}
	}
	return ret;
}

#if (defined(_WIN32) || defined(_WIN64))
bool Diskletter(std::wstring cVolumeLetter, TCHAR& cClientLetter, bool bRead)
{
	bool ret(false);
	std::wstring sMountPath;
	GetPartitionDevicePath(cVolumeLetter, sMountPath);
	if (!sMountPath.empty()) {
		ret = Diskletter_(sMountPath, cClientLetter, bRead);
	}
	return ret;
}

bool DiskHided(TCHAR cVolumeLetter)
{
	return DiskHided_(wstring_format(VOLUME_LETTER_FORMAT_DEVICE, cVolumeLetter));
}

bool DiskHided(const std::wstring& sVolumeRoot)
{
	return DiskHided_(FormatVolumeRealName(sVolumeRoot));
}

bool HideDisk(TCHAR cVolumeLetter, int nHideDiskMode, bool bKeepOldLetter, TCHAR* cNewLetter)
{
	return HideDisk_(wstring_format(VOLUME_LETTER_FORMAT_DEVICE, cVolumeLetter), nHideDiskMode, bKeepOldLetter, cNewLetter);
}

bool HideDisk(std::wstring sVolumeRoot, int nHideDiskMode, bool bKeepOldLetter /*= true*/, TCHAR* cNewLetter /*= NULL*/)
{
	return HideDisk_(FormatVolumeRealName(sVolumeRoot), nHideDiskMode, bKeepOldLetter, cNewLetter);
}

#else

bool Diskletter(const std::string& sVolumeMountpoint, TCHAR& cClientLetter, bool bRead)
{
	std::string sDevicePath;
	if (GetDeviceMountPath(sVolumeMountpoint, sDevicePath)) {
		return Diskletter_(sDevicePath, cClientLetter, bRead);
	}
	return false;
}

bool DiskHided(const std::string& sVolumeMountpoint)
{
	std::string sDevicePath;
	if (GetDeviceMountPath(sVolumeMountpoint, sDevicePath)) {
		return DiskHided_(sDevicePath);
	}
	return false;
}

bool HideDisk(std::string sVolumeMountPoint, int nHideDiskMode, bool bKeepOldLetter, TCHAR* cNewLetter)
{
	std::string sDevicePath;
	if (GetDeviceMountPath(sVolumeMountPoint, sDevicePath)) {
		return HideDisk_(sDevicePath, nHideDiskMode, bKeepOldLetter, cNewLetter);
	}
	return false;
}
#endif

void Format_Para_1::PrintLog()
{
	WRITE_ISCSIFILELOG(_T("Format Para : ") << _T("GPT= ") << bGPT << _T(", bFormatGB= ") << bFormatGB << _T(", cFat32Letter= ") << cFat32Letter << _T(", nPartitionCount= ") << nPartitionCount);
	for (auto i = 0; i < nPartitionCount; i++) {
		WRITE_ISCSIFILELOG(_T("Partition ") << i << _T(" : Percent= ") << aDiskPartitions[i].nPercent << _T(", ClientLetter= ") << aDiskPartitions[i].cClientLetter
			<< _T(", cRealLetter= ") << aDiskPartitions[i].cRealLetter << _T(", HideDiskMode= ") << aDiskPartitions[i].nHideDiskMode << _T(", bVHDBootPartition= ") << aDiskPartitions[i].bVHDBootPartition
			<< _T(", bUserDataPartition= ") << aDiskPartitions[i].bUserDataPartition << _T(", bWritebackPartition= ") << aDiskPartitions[i].bWritebackPartition);
	}
}