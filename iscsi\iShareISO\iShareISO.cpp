﻿// iShareISO.cpp : 定义应用程序的入口点。
//

#include "stdafx.h"
#include "../iShareDll/includeappoptions.h"
#include "../iShareDll/OEMFunc.h"
#include "../iShareDll/stringsystem.h"
#include "../iShareDll/LanguageFunction.hpp"
//#include "resource.h"
#include "iShareISO.h"
#include "Wizards.h"

// 全局变量:
HINSTANCE g_hInstance = NULL;							// 当前实例
//CLanguageIni* g_languages = NULL;
//BOOL g_Runinwinpe = FALSE;

int APIENTRY _tWinMain(_In_ HINSTANCE hInstance,
	_In_opt_ HINSTANCE hPrevInstance,
	_In_ LPTSTR    lpCmdLine,
	_In_ int       nCmdShow)
{
	g_hInstance = hInstance; // 将实例句柄存储在全局变量中
	UNREFERENCED_PARAMETER(hPrevInstance);
	ParseCmdLine(lpCmdLine);
	//UNREFERENCED_PARAMETER(lpCmdLine);
	UNREFERENCED_PARAMETER(nCmdShow);
	auto hLanguages = boost::make_shared<CLanguageIni>();
	g_languages = hLanguages.get();
	if (g_languages) {
		LoadLanguagesFromFolder(*g_languages);
		if (IsOEMXima() || IsOEMLangCao() || IsOEMICloud()) {
			g_languages->ChangeLanguage(SIMPLIFIED_CHINESE_NAME);
			SetThreadLanguageByName(ENGLISH_NAME);
		}
	}
	CLogSystem m_pLog;									//log
	m_pLog.StartLog(GetModuleFolder(), GetPeSystemLogFileName());
	WRITE_ISCSIFILELOG(_T("iShareISO log start!"));
	return ShowIPSetWizards();
}

// std::wstring LoadStringFromID(int nResourceID)
// {
// 	std::wstring sResourceString;
// 	LoadString(g_hInstance, nResourceID, tmpstr(sResourceString, MAX_LOADSTRING), MAX_LOADSTRING);
// 	return sResourceString;
// }

void TranlateCWnd(HWND pWnd)
{
	auto nStringLen = ::GetWindowTextLength(pWnd);
	if (nStringLen > 0 && nStringLen < 256) {//当长度超过256时,GetWindowText将出错
		std::wstring strText;
		nStringLen++;
		::GetWindowText(pWnd, tmpstr(strText, nStringLen), nStringLen);
		if (!strText.empty())
			::SetWindowText(pWnd, TRANSLATE(strText).c_str());
	}
}

BOOL CALLBACK EnumChildProc(HWND hwndChild, LPARAM lParam)
{
	HWND dlg = (HWND)lParam;
	if (dlg && hwndChild && ::IsChild(dlg, hwndChild)) {
		TranlateCWnd(hwndChild);
	}
	return TRUE;
}

void ParseCmdLine(LPWSTR    lpCmdLine)
{
	try {
		boost::program_options::options_description opts("iShareAdmin command help");
		opts.add_options()\
			("help,h", "Show help message.")\
			("winpe,w", "run in winpe mode");
		std::vector<std::wstring> args = boost::program_options::split_winmain(lpCmdLine);
		using namespace boost::program_options::command_line_style;
		auto pr = boost::program_options::wcommand_line_parser(args).options(opts).style(unix_style | allow_slash_for_short | allow_long_disguise | case_insensitive).allow_unregistered().run();
		boost::program_options::variables_map vm;
		boost::program_options::store(pr, vm);
		//if (vm.count("winpe"))
		//	g_Runinwinpe = TRUE;
		if (vm.count("help")) {
			std::stringstream helpstream;
			opts.print(helpstream);
			MessageBoxA(NULL, helpstream.str().c_str(), "helper", MB_OK);
		}
	}
	catch (const std::exception& e) {
		MessageBoxA(NULL, e.what(), "helper", MB_OK);
	}
	catch (...) {
	}
}