﻿#include "stdafx.h"
#include <Usbioctl.h>
#include "../iShareDll/UsbBootDef.h"
#include "mbr.h"
#include "../iShareDriverLib/time.hpp"
#include "iSharePnp.h"

static const PWCHAR sanbootconf_device_symlink[] = {
	L"\\Device\\iSCSIBoot",
	L"\\DosDevices\\iSCSIBoot",
	L"\\DosDevices\\iSharePnp"
};
static const PWCHAR iSharePnp_DeviceName = L"\\Device\\iSharePnp";
static const PWCHAR NetClass_GUID = NET_CLASS_GUID;
static const PWCHAR DiskClass_GUID = DISKCLASS_GUID_VAL;
static const PWCHAR VolumeClass_GUID = L"{71a27cdd-812a-11d0-bec7-08002be2092f}";
static const PWCHAR KeyboardClass_GUID = L"{4d36e96b-e325-11ce-bfc1-08002be10318}";
// static const PWCHAR DisplayClass_GUID = L"{4d36e968-e325-11ce-bfc1-08002be10318}";
static const PWCHAR UsbClass_GUID = L"{36fc9e60-c465-11cf-8056-************}";
static const PWCHAR SystemClass_GUID = L"{4d36e97d-e325-11ce-bfc1-08002be10318}";
//static const PWCHAR SCSIAdapterClass_GUID = L"{4d36e97b-e325-11ce-bfc1-08002be10318}";

CiSharePnp::CiSharePnp(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
	: CWDMDriver(DriverObject, RegistryPath)
	// 	, m_MountPoint()
	, m_NicPara(RegistryPath)
	, m_Ibft(m_NicPara)
#ifdef USE_OLD_PNP_FUNCTION
	, m_NdisLanMan(m_NicPara)
#else
	, m_Pnp(m_NicPara, m_bDisableHostnameFromDHCP)
#endif
{
	CRegKey ParaRegKey;
	ReloadConfig_(ParaRegKey);
	//禁止页面文件执行代码
	DisablePageFile();
	//开始pnp
#ifdef USE_OLD_PNP_FUNCTION
	m_NdisLanMan.StartPnpNic();
#else
	m_Pnp.StartPnp(m_bDisablePnpNic, m_bDisablePnpDisplay);
#endif
	//保存参数
	if (ParaRegKey.IsOpen()) {
		m_NicPara.SaveConfig_(ParaRegKey);
		ParaRegKey.Close();
	}
}

CiSharePnp::~CiSharePnp(void)
{
}

NTSTATUS CiSharePnp::DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(DriverObject);
	UNREFERENCED_PARAMETER(RegistryPath);
	NTSTATUS status(STATUS_UNSUCCESSFUL);
	CWDMDevice_ptr pNewDevice(new iSharePnpDevice(this, iSharePnp_DeviceName));
	if (pNewDevice.get()) {
		//这个设备是NEITHER_IO，如果设置为DO_BUFFERED_IO会导致数据被重复释放
		status = CreateDevice(pNewDevice, 0);
		if (NT_SUCCESS(status)) {
			for (ULONG i = 0; i < _countof(sanbootconf_device_symlink); i++)
				pNewDevice->CreateSymbolicLink(sanbootconf_device_symlink[i]);
		}
		if (BOOT_IBFT != m_NicPara.m_nBootMode) {
			//放弃 grub4dos 引导，改为 grub2, 所以都要监控文件系统, 读取 ip.ipxe 的IP设置
		//if (BOOT_NORMAL == m_NicPara.m_nBootMode && IsWindows10UpVersion()
		//	|| IsXp2003Version()) {
			//在win10下Grub4Dos无效，继续监控文件系统
			//xp下，新磁盘过滤adddevice无法收到消息，也要监控文件系统
			RegisterFileSystem();
		}
		// 		if ( NT_SUCCESS(m_MountPoint.Open()) ){
		// 			m_MountPoint.RegisterPnpNotification(DriverObject);//监视硬盘
		// // 			SetRegValue(NULL, CStringW(RegistryPath) + ISHAREPNP_PARAMETERS, ISHAREPNP_BOOTINITTIMES, (DWORD)0, TRUE);
		// 		}
		// 		else{
		// 			//https://msdn.microsoft.com/en-us/library/windows/hardware/ff549494
		// 			//A driver should call IoRegisterBootDriverReinitialization only if its DriverEntry routine will return STATUS_SUCCESS.
		// 			if (NT_SUCCESS(status))
		// 				ReinitializationBoot();
		// 		}
	}
	return status;
}

// VOID CiSharePnp::BootinitWait( ULONG count )
// {
// 	if ( count < SANBOOTCONF_MAX_WAIT && !m_MountPoint.IsOpen() ){
// 		if ( NT_SUCCESS(m_MountPoint.Open()) ){
// 			m_MountPoint.RegisterPnpNotification( m_DriverObject );
// // 			SetRegValue(NULL, m_RegistryPath + ISHAREPNP_PARAMETERS, ISHAREPNP_BOOTINITTIMES, count, TRUE);
// 		}
// 		else{
// // 			CThread::WaitSecond(); //为了加快启动速度,取消等待1秒
// 			ReinitializationBoot();
// 		}
// 	}
// }

CWDMDevice_ptr CiSharePnp::OnAddDevice(PDEVICE_OBJECT PhysicalDeviceObject)
{
	// 	NTSTATUS status(STATUS_UNSUCCESSFUL);
	WCHAR sClass_GUID[] = NET_CLASS_GUID;
	RtlZeroMemory(sClass_GUID, sizeof(sClass_GUID));
	ULONG nResultLength(0);
	enum {
		GUID_CLASS_UNKNOW, GUID_CLASS_NET, GUID_CLASS_DISK, GUID_CLASS_VOLUME, GUID_CLASS_KEYBOARD, GUID_CLASS_USB, GUID_CLASS_SYSTEM, GUID_CLASS_SCSIADAPTER
	};
	if (NT_SUCCESS(IoGetDeviceProperty(PhysicalDeviceObject, DevicePropertyClassGuid, sizeof(sClass_GUID), sClass_GUID, &nResultLength))) {
		if (_wcsnicmp(sClass_GUID, SystemClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_SYSTEM;
		}
		else if (_wcsnicmp(sClass_GUID, NetClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_NET;
		}
		else if (_wcsnicmp(sClass_GUID, DiskClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_DISK;
		}
		else if (_wcsnicmp(sClass_GUID, VolumeClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_VOLUME;
		}
		else if (_wcsnicmp(sClass_GUID, KeyboardClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_KEYBOARD;
		}
		// 		else if (_wcsnicmp(sClass_GUID, DisplayClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
		// 			nResultLength = GUID_CLASS_DISPLAY;
		// 		}
		else if (_wcsnicmp(sClass_GUID, UsbClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
			nResultLength = GUID_CLASS_USB;
		}
		//else if (_wcsnicmp(sClass_GUID, SCSIAdapterClass_GUID, sizeof(sClass_GUID) / sizeof(WCHAR)) == 0) {
		//	nResultLength = GUID_CLASS_SCSIADAPTER;
		//}
		else {
			nResultLength = GUID_CLASS_UNKNOW;
		}
	}
	switch (nResultLength) {
	case GUID_CLASS_NET:
		if (BOOT_IBFT == m_NicPara.m_nBootMode)
		{
			HANDLE reg_key(NULL);
			if (NT_SUCCESS(IoOpenDeviceRegistryKey(PhysicalDeviceObject, PLUGPLAY_REGKEY_DRIVER, KEY_READ | KEY_WRITE, &reg_key))) {
				CRegKey InstanceKey;
				InstanceKey.Attach(reg_key);
				if (m_Pnp.FindPnpedDevice(InstanceKey.GetKeyName())) {
					InstanceKey.SetStringValue(TCPIP_PARA_MAC_KEYNAME, L"");
					return CWDMDevice_ptr(new NetDevice(this));
				}
			}
		}
		//  	if (BOOT_IBFT == m_NicPara.m_nBootMode && !m_bFilterNic && m_NicPara.m_sBootMac.GetLength() > 0){
		// 			//过滤网卡
		// 			HANDLE reg_key;
		// 			/* Open driver registry key */
		// 			status = IoOpenDeviceRegistryKey(PhysicalDeviceObject, PLUGPLAY_REGKEY_DRIVER, KEY_READ, &reg_key);
		// 			if (NT_SUCCESS(status)) {
		// 				CRegKey InstanceKey;
		// 				InstanceKey.Attach(reg_key);
		// 				CStringW sMac;
		// 				if (NT_SUCCESS(InstanceKey.QueryStringValue(TCPIP_PARA_MAC_KEYNAME, sMac))) {
		// 					if (m_NicPara.m_sBootMac == sMac) {
		// 						//找到了匹配的网卡//清除以前保存的值,避免技嘉主板重启后,显示超级用户的MAC地址.
		// 						InstanceKey.SetStringValue(TCPIP_PARA_MAC_KEYNAME, L"");
		// 						m_bFilterNic = TRUE;
		// 						return CWDMDevice_ptr(new NetDevice(this));
		// 					}
		// 				}
		// 			}
		// 		}
		break;
	case GUID_CLASS_DISK:
		//http://www.it610.com/article/4783259.htm
//全称为 USB Mass Storage Class Driver，是用于管理 USB 大容量存储设备的类驱动。USB 大容量存储设备包括 U 盘、移动硬盘、闪存卡等。Usbstor.sys
#define USB_DRIVERNAME L"\\Driver\\USBSTOR"
//全称为 Microsoft Uasp Driver，是用于支持 UASP (USB Attached SCSI Protocol) 的类驱动。UASP 是一种传输协议，可以提高 USB 3.0 存储设备的性能和效率。Uaspstor.sys
#define USB3_DRIVERNAME L"\\Driver\\UASPStor"
#define VHDDMP_DRIVERNAME L"\\Driver\\vhdmp"
#define DISKLESS_DRIVERNAME L"\\Driver\\iScsiPrt"
		if (PhysicalDeviceObject->DeviceType == FILE_DEVICE_MASS_STORAGE
			&& PhysicalDeviceObject->DriverObject && PhysicalDeviceObject->DriverObject->DriverName.Length > 0
			&& PhysicalDeviceObject->DriverObject->DriverName.Buffer)
		{
			BOOLEAN bUsbStorage = _wcsnicmp(PhysicalDeviceObject->DriverObject->DriverName.Buffer, USB_DRIVERNAME, sizeof(USB_DRIVERNAME) / sizeof(WCHAR) - 1) == 0 \
				|| _wcsnicmp(PhysicalDeviceObject->DriverObject->DriverName.Buffer, USB3_DRIVERNAME, sizeof(USB3_DRIVERNAME) / sizeof(WCHAR) - 1) == 0;
			BOOLEAN bVhddmpDisk = _wcsnicmp(PhysicalDeviceObject->DriverObject->DriverName.Buffer, VHDDMP_DRIVERNAME, sizeof(VHDDMP_DRIVERNAME) / sizeof(WCHAR) - 1) == 0;
			BOOLEAN bDisklessDisk = _wcsnicmp(PhysicalDeviceObject->DriverObject->DriverName.Buffer, DISKLESS_DRIVERNAME, sizeof(DISKLESS_DRIVERNAME) / sizeof(WCHAR) - 1) == 0;
			if (bUsbStorage || (!bVhddmpDisk && !bDisklessDisk && m_bClearSerialNumber)) {
				return CWDMDevice_ptr(new DiskDevice(this, bUsbStorage, m_bDenyWriteUsbDisk, m_bClearSerialNumber));
			}
		}
		break;
		//case GUID_CLASS_SCSIADAPTER:
		//	if (m_bClearSerialNumber) {
		//		return CWDMDevice_ptr(new DiskDevice(this, FALSE, m_bDenyWriteUsbDisk, m_bClearSerialNumber));
		//	}
		//	break;
	case GUID_CLASS_VOLUME:
		if (PhysicalDeviceObject->DeviceType == FILE_DEVICE_DISK
			&& !FindDiskByPhysicalDevice(PhysicalDeviceObject)) {
			//过滤所有卷,方便非无盘和vhd启动的，只有游戏盘的用户能够修改游戏盘的盘符
			return CWDMDevice_ptr(new VolumeDevice(this, m_bDenyWriteUsbDisk, m_NicPara, m_cDiskLetterLocalFirst, m_cDiskLetterUsbLast));
		}
		break;
	case GUID_CLASS_KEYBOARD:
		if (m_bMonitorKeyboard) {
			return CWDMDevice_ptr(new KeyBoardDevice(this, m_bDisableCtrlAtlDel, m_bDisableKeyboard, m_nEnableKey, m_bMonitorKeyboard));
		}
		break;
		// 	case GUID_CLASS_DISPLAY:
		// 	{
		// 		HANDLE reg_key(NULL);
		// 		if (NT_SUCCESS(IoOpenDeviceRegistryKey(PhysicalDeviceObject, PLUGPLAY_REGKEY_DRIVER, KEY_READ, &reg_key))) {
		// 			CRegKey InstanceKey;
		// 			InstanceKey.Attach(reg_key);
		// 			if (m_Pnp.FindPnpedDevice(InstanceKey.GetKeyName())) {
		// 				return CWDMDevice_ptr(new NetDevice(this));
		// 			}
		// 		}
		// 	}
		// 		break;
	case GUID_CLASS_USB:
	{
#define USB_PRINTER_INFNAME L"usbprint.inf"
#define USB_PRINTER_INFSECTION_UNKNOWPRINTER L"NO_DRV"
#define USB_PRINTER_UNKNOWPRINTER_ID L"usbprint\\unknowprinter"
		if (m_bFixUsbprinter) {
			HANDLE reg_key(NULL);
			if (NT_SUCCESS(IoOpenDeviceRegistryKey(PhysicalDeviceObject, PLUGPLAY_REGKEY_DRIVER, KEY_READ, &reg_key))) {
				CRegKey InstanceKey;
				InstanceKey.Attach(reg_key);
				CStringW sInfPath;
				if (NT_SUCCESS(InstanceKey.QueryStringValue(L"InfPath", sInfPath))
					&& sInfPath.CompareNoCase(USB_PRINTER_INFNAME) == 0) {
					BOOLEAN bUnknowPrinter = (NT_SUCCESS(InstanceKey.QueryStringValue(L"InfSection", sInfPath))
						&& sInfPath.CompareNoCase(USB_PRINTER_INFSECTION_UNKNOWPRINTER) == 0
						&& NT_SUCCESS(InstanceKey.QueryStringValue(L"MatchingDeviceId", sInfPath))
						&& sInfPath.CompareNoCase(USB_PRINTER_UNKNOWPRINTER_ID) == 0) ? TRUE : FALSE;
					if (bUnknowPrinter) {
						RecheckUsbPrinterOnBackThread(FALSE, TRUE);//修复usb打印机unknowprinter
					}
					return CWDMDevice_ptr(new UsbPrinterDevice(this, bUnknowPrinter));
				}
			}
		}
	}
	break;
	case GUID_CLASS_SYSTEM:
	default:
		break;
	}
	return CWDMDevice_ptr();
}

NTSTATUS CiSharePnp::GetiBFT(PVOID Buffer, ULONG Length, PULONG lpBytesReturn)
{
	NTSTATUS status(STATUS_UNSUCCESSFUL);
	DWORD dwByteReturn(0);
	KdPrint(("iBFT requested\n"));
	if (Buffer != NULL && Length > 0) {
		if (m_Ibft.IsBoot()) {
			if (Length >= m_Ibft.GetIbft()->acpi.length) {
				RtlCopyMemory(Buffer, m_Ibft.GetIbft(), m_Ibft.GetIbft()->acpi.length);
				dwByteReturn = m_Ibft.GetIbft()->acpi.length;
				status = STATUS_SUCCESS;
			}
			else {
				status = STATUS_BUFFER_OVERFLOW;;
			}
		}
		else {
			KdPrint(("No iBFT available!\n"));
			status = STATUS_NO_SUCH_FILE;
		}
	}
	else {
		status = STATUS_INVALID_PARAMETER;
	}
	if (lpBytesReturn)
		*lpBytesReturn = dwByteReturn;
	return status;
}

NTSTATUS CiSharePnp::ReadDiskLetterSet(CStringW sDiskName, PCHAR cDiskLetterSet)
{
	if (sDiskName.GetLength() == 0 || cDiskLetterSet == NULL)
		return STATUS_INVALID_PARAMETER;

	NTSTATUS status(STATUS_UNSUCCESSFUL);
	CStringW sRegistryPath = m_RegistryPath + ISHAREPNP_PARAMETERS;
	CRegKey ParaRegKey;
	//读取参数
	if (NT_SUCCESS(ParaRegKey.Open(NULL, (PUNICODE_STRING)sRegistryPath))
		|| NT_SUCCESS(ParaRegKey.Create(NULL, (PUNICODE_STRING)sRegistryPath))) {
		DWORD dwDiskLetterSet(0);
		status = ParaRegKey.QueryDWORDValue((PUNICODE_STRING)sDiskName, dwDiskLetterSet);
		if (NT_SUCCESS(status)) {
			if (IsGoodDiskletterSet((CHAR)dwDiskLetterSet)) {
				if (cDiskLetterSet) *cDiskLetterSet = (CHAR)dwDiskLetterSet;
			}
			else {
				status = STATUS_UNSUCCESSFUL;
			}
		}
	}
	return status;
}

NTSTATUS CiSharePnp::ReloadConfig()
{
	CRegKey ParaRegKey;
	if (ReloadConfig_(ParaRegKey)) {
		ParaRegKey.Close();
	}
	return STATUS_SUCCESS;
}

NTSTATUS CiSharePnp::EnterWindows() {
	auto ret = ReloadConfig();
	if (!m_bEnteredWindows) {
		m_bEnteredWindows = TRUE;
		//禁止页面文件
	//	DisablePageFile(FALSE);
		//禁止继续读取ip.pxe
#ifdef USE_OLD_PNP_FUNCTION
		m_NdisLanMan.OnFileSystemOnline(FILESYTEM_ONLINE_END);
#else
		m_Pnp.OnFileSystemOnline(FILESYTEM_ONLINE_END);//退出后台注册表监控线程
#endif
		UnregisterFileSystem();
		if (IsXp2003Version()) {
			//重新检查一遍盘符，新磁盘因为没有第二次上线而躲过检查
			list< CWDMDevice_ptr >& lDeviceObjects(GetWDMDevices());
			for (auto iter = lDeviceObjects.begin(); iter != lDeviceObjects.end(); iter++) {
				if ((*iter)->IsFilterDevice() == WDMDEVICE_VOLUME) {
					(static_cast<VolumeDevice*>((*iter).get()))->ReCheckDiskLetter();
				}
			}
		}
		//进入windows后修复win7专业版的vhd启动授权问题
		m_Pnp.EnterWindows((m_bSuperUser || m_bLocalKeepMode) ? TRUE : FALSE);
		RecheckUsbPrinterOnBackThread(TRUE, FALSE);//修复usb打印机unknowprinter
	}
	return ret;
};

NTSTATUS CiSharePnp::OnWindowsShutdown()
{
	//if (BOOT_GRUB4DOS == m_NicPara.m_nBootMode && IsWindows7ProVersion()) {
		//CRegFilterVHD::fix_win7pro(TRUE);
	//}
	return STATUS_SUCCESS;
}

NTSTATUS CiSharePnp::FixUsbPrinter()
{
	RecheckUsbPrinterOnBackThread(FALSE, FALSE);//修复usb打印机unknowprinter
	return STATUS_SUCCESS;
}

// NTSTATUS CiSharePnp::Unload()
// {
// 	IoUnregisterFsRegistrationChange(m_DriverObject, CiSharePnp::SfFsNotification_sta);
// 	return CWDMDriver::Unload();
// }

BOOLEAN CiSharePnp::ReloadConfig_(CRegKey& ParaRegKey)
{
	//CStringW sRegistryPath(m_RegistryPath);
	//sRegistryPath += ISHAREPNP_PARAMETERS;
	//读取参数
	if (NT_SUCCESS(ParaRegKey.Open(NULL, m_NicPara.m_RegistryPath))
		|| NT_SUCCESS(ParaRegKey.Create(NULL, m_NicPara.m_RegistryPath))) {
		//m_NicPara.m_RegistryPath = sRegistryPath;
		ParaRegKey.QueryDWORDValue(ISHAREPNP_SETGATEWAY, m_NicPara.m_dwSetGateway);
		ParaRegKey.QueryDWORDValue(ISHAREPNP_DENYWRITEUSBDISK, m_bDenyWriteUsbDisk);
		ParaRegKey.QueryDWORDValue(ISHAREPNP_CLEAN_SERIALNUMBER, m_bClearSerialNumber);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_HIDELOCALDISK, m_bHideLocalDisk);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_CRASHDUMPENABLED, m_bCRASHDUMPENABLED);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_DISABLE_PAGEFILE_UP_WIN10, m_bDisablePageFileUpWin101903);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_CTRL_ATL_DEL, m_bDisableCtrlAtlDel);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_DISABLE_KEYBOARD, m_bDisableKeyboard);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_MONITOR_KEYBOARD, m_bMonitorKeyboard);
		CSimpleBuffer aEnableKeys;
		ParaRegKey.QueryBinaryValue(ISHAREPNP_ENABLE_KEYBOARD_KEY, aEnableKeys);
		RtlZeroMemory(m_nEnableKey, sizeof(m_nEnableKey));
		RtlCopyMemory(m_nEnableKey, aEnableKeys.data(), min(sizeof(m_nEnableKey), aEnableKeys.size()));
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_DISABLE_PNP_NIC, m_bDisablePnpNic);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_DISABLE_PNP_DISPLAY, m_bDisablePnpDisplay);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_SUPERUSER, m_bSuperUser);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_LOCALKEEPMODE, m_bLocalKeepMode);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_DISABLE_HOSTNAME_FROM_DHCP, m_bDisableHostnameFromDHCP);
		ParaRegKey.QueryBOOLEANValue(ISHAREPNP_FIX_USBPRINTER, m_bFixUsbprinter);
		ParaRegKey.QueryIntValue(ISHAREPNP_LOCALDISKFIRSTLETTER, m_cDiskLetterLocalFirst);
		ParaRegKey.QueryIntValue(ISHAREPNP_USBDISKLASTLETTER, m_cDiskLetterUsbLast);
		if (m_bFixUsbprinter) {
			m_BackThread.StartThread(Thread_Function_sta, this);
		}
	}
	return ParaRegKey.IsOpen();
}

//禁止页面文件，必须在无盘启动前设置好，等到真正无盘启动后，启动就蓝屏，不会让你执行设置的。
//win10 1903 以上版本设置页面文件导致蓝屏的最终祸首，必须禁用crash dump
BOOLEAN CiSharePnp::DisablePageFile(BOOLEAN bBackRestore)
{
	if (IsWindows10UpVersion()) {
		//crashdump 会导致无盘启动蓝屏，故意关闭之
		CRegKey RegKey;
		if (!m_bCRASHDUMPENABLED && NT_SUCCESS(RegKey.Open(HKEY_LOCAL_MACHINE, REGSTR_PATH_CRASHCONTROL))) {
			RegKey.SetDWORDValue(REGVALUENAME_AUTOREBOOT, (DWORD)0);
			RegKey.SetDWORDValue(REGVALUENAME_CRASHDUMPENABLED, (DWORD)0);
			RegKey.Close();
		}
		//关闭页面，进桌面后再设置，这样可以避免本地启动的页面错误，和无盘页面文件的优化, 默认值为1
		//0 - 不关闭页面文件， 1 - 只在本地启动关闭页面文件， 2 - 本地启动和无盘启动时都关闭
		if (((m_bDisablePageFileUpWin101903 == 1 && BOOT_GRUB4DOS == m_NicPara.m_nBootMode)
			|| (m_bDisablePageFileUpWin101903 == 2 && BOOT_NORMAL != m_NicPara.m_nBootMode))
			&& NT_SUCCESS(RegKey.Open(HKEY_LOCAL_MACHINE, REGSTR_PATH_MEMORY_MANAGEMENT))) {
			CStringW sPageFiles;
			RegKey.QueryMultiStringValue(bBackRestore ? REGSTR_PATH_PAGEFILES : REGSTR_PATH_PAGEFILESBAK, sPageFiles);
			if (sPageFiles.GetLength() > 0) {
				RegKey.SetMultiStringValue(bBackRestore ? REGSTR_PATH_PAGEFILESBAK : REGSTR_PATH_PAGEFILES, sPageFiles);
				sPageFiles.Empty();
				RegKey.SetMultiStringValue(bBackRestore ? REGSTR_PATH_PAGEFILES : REGSTR_PATH_PAGEFILESBAK, sPageFiles);
			}
			RegKey.Close();
			return TRUE;
		}
	}
	return FALSE;
}

VOID CiSharePnp::OnFileSystemOnline(PDEVICE_OBJECT DeviceObject, CStringW sDiskName)
{
	auto pDiskDevice = FindDiskByRealName(sDiskName);
	if (pDiskDevice) {
		if (BOOT_IBFT != m_NicPara.m_nBootMode
			&& SYSTEMLETTER_CHAR != pDiskDevice->GetDiskLetterCur()
			&& !pDiskDevice->GetUsbDisk()) {
			//&& IsWindows10UpVersion()) {
#ifdef USE_OLD_PNP_FUNCTION
			m_NdisLanMan.OnFileSystemOnline(sDiskName);
#else
			m_Pnp.OnFileSystemOnline(sDiskName);
#endif
		}
	}
	else {
		//xp下，新磁盘不会调用AddDevice,  U盘调用AddDevice比FileSystemOnline晚。
		if (DeviceObject->DeviceType == FILE_DEVICE_DISK
			&& !FindDiskByPhysicalDevice(DeviceObject)) {
			auto pVolumeDevice = CWDMDevice_ptr(new VolumeDevice(this, m_bDenyWriteUsbDisk, m_NicPara, m_cDiskLetterLocalFirst, m_cDiskLetterUsbLast));
			if (pVolumeDevice) {
				AddDevice(pVolumeDevice, DeviceObject);
				static_cast<VolumeDevice*>(pVolumeDevice.get())->OnVolumeOnline();
			}
		}
	}
	return;
}

//卷上线时，检查是否是vhd启动
VOID CiSharePnp::OnVolumeOnline(const CStringW& sDProductName, CHAR cDiskLetter)
{
	if (BOOT_NORMAL == m_NicPara.m_nBootMode && SYSTEMLETTER_CHAR == cDiskLetter
		&& (sDProductName.CompareNoCase(MICROSOFT_VHD_DISK_PRODUCTID, _countof(MICROSOFT_VHD_DISK_PRODUCTID) - 1) == 0
			|| sDProductName.CompareNoCase(VBOOT_XP_VHD_DISK_PRODUCTID, _countof(VBOOT_XP_VHD_DISK_PRODUCTID) - 1) == 0)) {
		m_NicPara.m_nBootMode = BOOT_GRUB4DOS;
		m_NicPara.SaveConfig(TRUE);
	}
	return;
}

VolumeDevice* CiSharePnp::FindDiskByRealName(CStringW sDiskName)
{
	for (auto iter = m_DeviceObjects.begin(); iter != m_DeviceObjects.end(); ++iter) {
		if ((*iter)->IsFilterDevice() == WDMDEVICE_VOLUME) {
			auto pDiskDevice = static_cast<VolumeDevice*>((*iter).get());
			if (pDiskDevice->GetRealDiskName().CompareNoCase((PUNICODE_STRING)sDiskName) == 0)
				return pDiskDevice;
		}
	}
	return NULL;
}

VolumeDevice* CiSharePnp::FindDiskByPhysicalDevice(PDEVICE_OBJECT hPhysicalDevice)
{
	for (auto iter = m_DeviceObjects.begin(); iter != m_DeviceObjects.end(); ++iter) {
		if ((*iter)->IsFilterDevice() == WDMDEVICE_VOLUME) {
			auto pDiskDevice = static_cast<VolumeDevice*>((*iter).get());
			if (pDiskDevice->GetPhysicalDevice() == hPhysicalDevice)
				return pDiskDevice;
		}
	}
	return NULL;
}

BOOLEAN CiSharePnp::FindUnknowPrinter()
{
	BOOLEAN bReHotPlug(FALSE);
	list< CWDMDevice_ptr >& lDeviceObjects(GetWDMDevices());
	for (auto iter = lDeviceObjects.begin(); iter != lDeviceObjects.end(); iter++) {
		if ((*iter)->IsFilterDevice() == WDMDEVICE_USBPRINTER) {
			if ((static_cast<UsbPrinterDevice*>((*iter).get()))->IsUnknowPrinter()) {
				bReHotPlug = TRUE;
				break;
			}
		}
	}
	return bReHotPlug;
}

//是否需要重插打印机端口,找到usb类型的unknow printer时，需要重新拔插所有的打印机端口，直接拔插对应的总线端口(USB打印服务)
VOID CiSharePnp::RecheckUsbPrinter()
{
	if (m_bDelayWhenResetPort) {
		Sleep(1000);//延时一秒再重插
	}
	//修正打印机的问题
	if (!m_bCheckUnknowPrinter || FindUnknowPrinter()) {
		list< CWDMDevice_ptr >& lDeviceObjects(GetWDMDevices());
		for (auto iter = lDeviceObjects.begin(); iter != lDeviceObjects.end(); iter++) {
			if ((*iter)->IsFilterDevice() == WDMDEVICE_USBPRINTER) {
				if (!((static_cast<UsbPrinterDevice*>((*iter).get()))->IsUnknowPrinter())) {
					(static_cast<UsbPrinterDevice*>((*iter).get()))->DeviceIoControl(IOCTL_INTERNAL_USB_CYCLE_PORT, NULL, 0, NULL, 0, NULL, TRUE);
				}
			}
		}
	}
	return;
}

VOID CiSharePnp::Thread_Function()
{
	//等待激活，返回FALSE表示没有结束
	while (!m_BackThread.WaitForExit()) {
		RecheckUsbPrinter();
	}
}

//绕过驱动签名检查
void BypassCheckSign(PDRIVER_OBJECT driver)
{
	typedef struct _LDR_DATA
	{
		struct _LIST_ENTRY InLoadOrderLinks;
		struct _LIST_ENTRY InMemoryOrderLinks;
		struct _LIST_ENTRY InInitializationOrderLinks;
		VOID* DllBase;
		VOID* EntryPoint;
		ULONG32 SizeOfImage;
		UINT8 _PADDING0_[0x4];
		struct _UNICODE_STRING FullDllName;
		struct _UNICODE_STRING BaseDllName;
		ULONG32 Flags;
	} LDR_DATA, * PLDR_DATA;
	PLDR_DATA ldr;
	ldr = (PLDR_DATA)driver->DriverSection;
	ldr->Flags |= 0x20;
}

NTSTATUS DriverEntry(IN PDRIVER_OBJECT DriverObject, IN PUNICODE_STRING  RegistryPath)
{
	BypassCheckSign(DriverObject);
	InitSystemFunctions();
	GetRootDeviceNode(DriverObject);
	CiSharePnp* iSharePnpExtent = new CiSharePnp(DriverObject, RegistryPath);
	if (iSharePnpExtent != NULL)
	{
		NTSTATUS status = iSharePnpExtent->DriverEntry(DriverObject, RegistryPath);
		if (!NT_SUCCESS(status)) {
			delete iSharePnpExtent;
		}
		return status;
	}
	else
	{
		KdPrint(("CiSharePnp New Error!\n"));
		return STATUS_UNSUCCESSFUL;
	}
}