﻿#include "stdafx.h"
#include <boost/lambda/lambda.hpp>
#include "../iShareDll/OEMFunc.h"
#include "../iShareDll/UsbBootISO.h"
#include "../iShareDll/ListDataStatus.h"
#include "../iShareDll/DataInclude.hpp"
#if (defined(_WIN32) || defined(_WIN64))
#include "../iShareDll/iscsi.h"
#endif
#include "iSCSIServer.h"
#include "iSCSIConnectManager.h"
#include "WebServer.h"

CiSCSIConnectManager::CiSCSIConnectManager(CiSCSIServer& pServer, boost::asio::io_service& ioservice)
	: m_pServer(pServer)
	, m_pAcceptor(boost::make_shared<CiSCSIAcceptor>(pServer, ioservice))
	, m_pAdminAcceptor(boost::make_shared<CClientAccept>(pServer, ioservice))
	, m_lUserClass(pServer, CALLBACKTYPE_USERCLASS, OPTIONISCSI_USERCLASS)
	, m_tUdpTimer(pServer.get_recycle_io())
	, m_lUnloginIscsiConnects(100)  //iscsi同时连接数限制在30个
	, m_lUnloginClientConnects(100)  //客户端管理同时连接数限制在10个
{
	if (IsOpenISCSIPortForIPSec()) {
		m_pAcceptor1 = boost::make_shared<CiSCSIAcceptor>(pServer, ioservice);
	}
}

CiSCSIConnectManager::~CiSCSIConnectManager(void)
{
	Unload();
}

bool CiSCSIConnectManager::LoadCallback(const CTreeListDataItem_ptr& pSubOptionItem, BOOL& bNeedSave, const TEMPLATE_COMPUTER_GROUP_MAP& lTemplateList)
{
	auto ptrSession = boost::make_shared<CiSCSISession>(m_pServer);
	if (ptrSession) {
		ptrSession->Load(pSubOptionItem);
		//核对最后更新日期
		if (ptrSession->CheckDiskLastWriteTime(true)) {
			ptrSession->Save(pSubOptionItem);
			bNeedSave = TRUE;
		}
		DWORD dwChangedBit(0);
		CTemplateManager::ApplyTemplate(lTemplateList, ptrSession, dwChangedBit);
		AddSession(ptrSession);
	}
	return true;//始终返回正确，避免中途导入中断
}

void CiSCSIConnectManager::Load()
{
	m_lUserClass.Load();
	auto lTemplateList = m_pServer.m_oTemplateManager.GetTemplateList(m_lUserClass.GetClassIDs());
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		BOOL bNeedSave(FALSE);
		RETVALUE_REF(retvalue)->for_each(boost::bind(&CiSCSIConnectManager::LoadCallback, this, boost::placeholders::_1, boost::ref(bNeedSave), boost::cref(lTemplateList)));
		if (bNeedSave)
			m_pServer.m_pOption.Save();
	}
	m_tUdpTimer.Start(GetDurationFromSeconds(30), boost::bind(&CiSCSIConnectManager::OnTime, this));
}

bool CiSCSIConnectManager::Listen()
{
	m_pAcceptor->Listen(m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_ISCSIPORT), DEFAULT_ACCEPTTHREAD * 2);//开始监听iscsi
	if (m_pAcceptor1) {
		m_pAcceptor1->Listen(m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_ISCSIPORT) + 1, DEFAULT_ACCEPTTHREAD * 2);//开始监听iscsi
	}
	m_pAdminAcceptor->Listen(m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENTPORT), DEFAULT_ACCEPTTHREAD);
	return true;
}

void CiSCSIConnectManager::Unload()
{
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		m_cSessions.clear();
		m_lUserClass.Unload();
	}
	m_lUnloginIscsiConnects.Unload();
	m_lUnloginClientConnects.Unload();
}

bool CiSCSIConnectManager::ReloadTemplate()
{
	auto lTemplateList = m_pServer.m_oTemplateManager.GetTemplateList(m_lUserClass.GetClassIDs());
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			DWORD dwChangedBit(0);
			CTemplateManager::ApplyTemplate(lTemplateList, *iter, dwChangedBit);
			BeforeEditUser_(*iter, dwChangedBit);
			if (dwChangedBit) {
				boost::json::object oRow = (*iter)->Export(false, true);
				AfterEditUser_(*iter, dwChangedBit, CMac_ptr(), &oRow);
			}
			else {
				AfterEditUser_(*iter, dwChangedBit);
			}
		}
	}
	return true;
}

bool CiSCSIConnectManager::ReloadTemplate(const CiSCSISession_ptr& pSession, bool bNew, bool bEdit)
{
	if (pSession) {
		DWORD dwChangedBit(0);
		if (!bNew && pSession->GetTemplateComputerBak()) {
			//创建用于复位用户设置的空模板
			auto pTemplateEmpty = boost::make_shared<CTemplateComputer>(m_pServer);
			pSession->ApplyTemplate(pTemplateEmpty, dwChangedBit);
		}
		auto lTemplateList = m_pServer.m_oTemplateManager.GetTemplateList(m_lUserClass.GetClassIDs());
		CTemplateManager::ApplyTemplate(lTemplateList, pSession, dwChangedBit);
		if (bEdit) {
			if (dwChangedBit) {
				boost::json::object oRow = pSession->Export(false, true);
				AfterEditUser_(pSession, dwChangedBit, CMac_ptr(), &oRow);
			}
			else {
				AfterEditUser_(pSession, dwChangedBit);
			}
		}
		return true;
	}
	return false;
}

bool CiSCSIConnectManager::PowerOnByTemplate()
{
	std::set<CMac> lMacs;
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if (!(*iter)->IsOnline() && (*iter)->GetMac().is_valid()
				&& ((*iter)->NeedPowerOnByTemplate()
					|| (*iter)->CheckPowerOnUpdateAvailable())) {
				lMacs.insert((*iter)->GetMac());
			}
		}
	}
	return PowerOnClient(lMacs);
}

BOOL CiSCSIConnectManager::Stop()
{
	m_pAcceptor->Stop();//前台监听停止
	if (m_pAcceptor1) {
		m_pAcceptor1->Stop();//前台监听停止
	}
	m_pAdminAcceptor->Stop();
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter)
			(*iter)->Close();
	}
	m_lUnloginIscsiConnects.Stop();
	m_lUnloginClientConnects.Stop();
	m_tUdpTimer.Close();//将包含的回调函数里的智能指针清除掉
	if (m_lMulticaseSenders) {
		m_lMulticaseSenders->Stop();
	}
	return TRUE;
}

bool CiSCSIConnectManager::GetComputerInfo(boost::json::object& dJsonDoc, const char* pIDs, int nClassID, bool bBaseInfo)
{
	std::set<DWORD> lIDs;
	if (pIDs) lIDs = SplitIDs(pIDs);
	boost::json::array lRows;//生成一个Array类型的元素，用来存放Object
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if ((lIDs.empty() || lIDs.count((*iter)->GetID()) > 0)
				&& (nClassID == -1 || nClassID == (*iter)->GetTypeID())) {
				lRows.push_back((*iter)->Export(false, bBaseInfo));           //添加到数组
			}
		}
	}
	InsertRowsToJson(lRows, dJsonDoc);
	return true;
}

// #ifdef NAME_AS_ID_SHAREIP
//
// //第一次登陆，按照名字查询客户机
// CiSCSISession_ptr CiSCSIConnectManager::FindAddSessionIscsi(CiSCSIConnect_ptr& pConnect)
// {//不管是否登录成功,都要从未登录列表里删除
// 	m_pServer.io_recycle_post(boost::bind(&CiSCSIConnectManager::DelUnloginIscsiConnect, this, pConnect));
// 	CMac sMac;
// 	CiSCSISession_ptr pSessionPtr;
// 	boost::mutex::scoped_lock lock(m_mLockSession);
// 	SESSION_INDEX_NAME& indexNAME = m_cSessions.get<SESSION_NAME>();
// 	auto sHostName = pConnect->GetHostName(&sMac);
// 	auto iterNAME = indexNAME.find(sHostName);
// 	//按照名字找到计算机
// 	if (iterNAME != indexNAME.end()) {
// 		pSessionPtr = (*iterNAME);
// 	}
// 	bool bNewAddComputer(false);//是否是通过iscsi添加的新计算机
// 	if (!pSessionPtr && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {//添加新计算机
// 		pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);//没有符合的会话,自动添加新会话
// 		if (pSessionPtr) {
// 			pConnect->GetRemoteMacAddress(sMac);
// 			pSessionPtr->AddFromDHCP(GetComputerNewID_(), sHostName, sMac, pConnect->GetRemoteIpAddress(), FALSE);
// 			bNewAddComputer = true;
// 		}
// 	}
// 	if (pSessionPtr) {
// 		if (m_pServer.CheckRegisterLogin(m_dwOnlineCount, pSessionPtr->GetSystemVersion())) {
// 			DWORD dwChangedBit(0);
// 			if (pSessionPtr->AddiSCSIConnect(pConnect, sMac, dwChangedBit)) {
// 				if (bNewAddComputer) {//新计算机
// 					if (AddSession_(pSessionPtr)){//添加会话
// 						pSessionPtr->SaveToOption(TRUE, TRUE);
// 					}
// 					else{
// 						pSessionPtr->Close();
// 						pSessionPtr.reset();
// 					}
// 				}
// 				else {
// 					if (dwChangedBit&CHANGE_IP) {//更新索引,用旧的iter进行更新
// 						if (iterNAME != indexNAME.end()) {
// 							auto sessionid = m_cSessions.project<SESSION_ID>(iterNAME);//转换为ID索引的iter
// 							UpdateSessionsIndex_(sessionid, dwChangedBit);
// 						}
// 						pSessionPtr->SaveToOption(FALSE, TRUE);
// 						OnDelDHCPCLient_(pSessionPtr);//更新dhcp缓存
// 						OnEditUser_(pSessionPtr);//通知用户回调
// 					}
// 				}
// 				if (pSessionPtr) {
// 					//添加后，通知上线
// 					pSessionPtr->OniSCSIOnline(TRUE, pConnect->IsBootLogin());
// 				}
// 			}
// 		}
// 	}
// 	return pSessionPtr;
// }
//
// #else

//第一次登录时查找关联的会话，判断标准见 RFC 3720 5.3.1
CiSCSISession_ptr CiSCSIConnectManager::FindAddSessionIscsi(CiSCSIConnect_ptr& pConnect)
{
	CMac sMac;
	CiSCSISession_ptr pSessionPtr;
	boost::mutex::scoped_lock lock(m_mLockSession);
	SESSION_INDEX_IP& indexIP = m_cSessions.get<SESSION_IP>();
	SESSION_INDEX_MAC& indexMac = m_cSessions.get<SESSION_MAC>();
	SESSION_INDEX_NAME& indexName = m_cSessions.get<SESSION_NAME>();
	auto iterIP = indexIP.end();
	auto iterMAC = indexMac.end();
	auto iterName = indexName.end();
	ULONG nClientIP(0);
	auto sHostName = pConnect->GetHostName(&sMac, &nClientIP);//从InitiatorName里获得mac和客户机IP
	if (!nClientIP) {
		//直接取远程IP
		nClientIP = pConnect->GetRemoteIpAddress();
	}
	iterMAC = sMac.is_valid() ? indexMac.find(sMac) : indexMac.end();
	if (iterMAC != indexMac.end()) {
		pSessionPtr = (*iterMAC);
	}
	else {
		iterIP = indexIP.find(nClientIP);
		if (iterIP != indexIP.end()) {
			//找到,这也有可能找到的是新添加的计算机,而导致改名字和newip标志啦
			pSessionPtr = (*iterIP);
			//if (pSessionPtr->IsNewIP() && !sMac.is_valid()) {
			//	pConnect->GetRemoteMacAddress(sMac);
			//}
		}
	}
	if (!pSessionPtr && !sHostName.empty()) {
		//最后查找计算机名字
		iterName = indexName.find(sHostName);
		if (iterName != indexName.end()) {
			pSessionPtr = (*iterName);
			if (pSessionPtr->IsIscsiOnline() && pSessionPtr->GetClientIP() != nClientIP) {
				//同名字的计算机，但是IP不一样，可能是新的计算机, 虚拟磁盘版本可能会出现
				pSessionPtr.reset();
				iterName = indexName.end();
			}
		}
	}

	bool bNewAddComputer(false);//是否是通过iscsi添加的新计算机
	if (!pSessionPtr && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {//添加新计算机
		pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);//没有符合的会话,自动添加新会话
		if (pSessionPtr) {
			//if (!sMac.is_valid()) {
			//	pConnect->GetRemoteMacAddress(sMac);
			//}
			if (sHostName.empty()) {
				//按照规则生成新的计算机名
				sHostName = GetNewHostname_();
			}
			pSessionPtr->AddFromDHCP(GetComputerNewID_(), sHostName, sMac, nClientIP, FALSE);
			bNewAddComputer = true;
		}
	}
	if (pSessionPtr) {
		if (m_pServer.CheckRegisterLogin(m_dwOnlineCount, pSessionPtr->GetSystemVersion())) {
			DWORD dwChangedBit(0);
			if (pSessionPtr->AddiSCSIConnect(pConnect, sHostName, sMac, nClientIP, dwChangedBit)) {
				if (bNewAddComputer) {//新计算机
					if (AddSession_(pSessionPtr)) {//添加会话
						boost::json::object oRow;
						if (pSessionPtr->SaveToOption(TRUE, TRUE, &oRow)) {
							AfterAddUser_(pSessionPtr, &oRow);
						}
					}
					else {
						pSessionPtr->Close();
						pSessionPtr.reset();
					}
				}
				else {
					AfterAddNewUser_(pSessionPtr, iterIP, iterMAC, iterName, dwChangedBit);
				}
				if (pSessionPtr) {
					//添加后，通知上线
					pSessionPtr->OniSCSIOnline(TRUE, pConnect->IsBootLogin());
				}
			}
			else {
				pSessionPtr.reset();
			}
		}
		else {
			WRITE_ISCSILOG(_T("CheckRegisterLogin Error, Remote IP:") << pConnect->GetRemoteAddress().c_str());
			pSessionPtr.reset();
		}
	}
	else {
		WRITE_ISCSILOG(_T("Can not find client by name:") << sHostName << (", IP:") << ip2wstring(nClientIP) << (", Mac:") << sMac.to_wstring());
	}
	//不管是否登录成功,都要从未登录列表里删除
	m_pServer.io_recycle_post(boost::bind(&CiSCSIConnectManager::DelUnloginIscsiConnect, this, pConnect));
	return pSessionPtr;
}

// #endif

void CiSCSIConnectManager::AfterAddNewUser_(const CiSCSISession_ptr& pSession, SESSION_ITER_IP& iterIP,
	SESSION_ITER_MAC& iterMAC, SESSION_ITER_NAME& iterName, DWORD dwChangedBit, PBOOL pNewSession /*= NULL*/)
{
	if (dwChangedBit & CHANGE_NEWIP) {
		//新计算机终于保存和提交到界面了
		if ((dwChangedBit & CHANGE_IP) || (dwChangedBit & CHANGE_NAME) || (dwChangedBit & CHANGE_MAC)) {
			//更新索引,用旧的iter进行更新
			if (iterIP != m_cSessions.get<SESSION_IP>().end()) {
				auto sessionid = m_cSessions.project<SESSION_ID>(iterIP);//转换为ID索引的iter
				UpdateSessionsIndex_(sessionid, dwChangedBit);
			}
			else if (iterMAC != m_cSessions.get<SESSION_MAC>().end()) {
				auto sessionid = m_cSessions.project<SESSION_ID>(iterMAC);//转换为ID索引的iter
				UpdateSessionsIndex_(sessionid, dwChangedBit);
			}
			else if (iterName != m_cSessions.get<SESSION_NAME>().end()) {
				auto sessionid = m_cSessions.project<SESSION_ID>(iterName);//转换为ID索引的iter
				UpdateSessionsIndex_(sessionid, dwChangedBit);
			}
		}
		boost::json::object oRow;
		if (pSession->SaveToOption(TRUE, TRUE, &oRow)) {
			AfterAddUser_(pSession, &oRow);
			OnAddUser_(pSession);//提交到界面
		}
		if (m_dwNewCount) m_dwNewCount--;//减少新计算机个数
		if (pNewSession)
			*pNewSession = TRUE;
	}
}

//不允许相同IP：先查找mac,未找到再找ip，未找到再找名字，都未找到就添加新计算机
CiSCSISession_ptr CiSCSIConnectManager::FindAddSessionClient(CClientConnect_ptr& pConnect, const std::wstring& sPassword, const std::wstring& sMacStr, const std::wstring& sName, PBOOL pNewSession, const std::wstring& sLocalIP)
{
	CMac sMac(sMacStr);
	CiSCSISession_ptr pSessionPtr;
	ULONG nClientIP = IsValidIPString(sLocalIP) ? string2ip(sLocalIP).to_uint() : pConnect->GetRemoteIpAddress();

	boost::mutex::scoped_lock lock(m_mLockSession);
	auto& indexIP = m_cSessions.get<SESSION_IP>();
	auto& indexMac = m_cSessions.get<SESSION_MAC>();
	auto& indexName = m_cSessions.get<SESSION_NAME>();
	auto iterIP = indexIP.end();
	auto iterMAC = indexMac.end();
	auto iterName = indexName.end();

	auto findByMac = [&]() -> CiSCSISession_ptr {
		if (sMac.is_valid()) {
			iterMAC = indexMac.find(sMac);
			if (iterMAC != indexMac.end()) {
				WRITE_ISCSILOG(_T("Find client by Mac:") << sMac.to_wstring() << _T(", client ID: ") << (*iterMAC)->GetID() << _T(", name: ") << sName << _T(", IP: ") << ip2wstring(nClientIP) << _T(", Remote IP : ") << pConnect->GetRemoteAddressW());
				if (sName != (*iterMAC)->GetHostName() || nClientIP != string2ip((*iterMAC)->GetIpAddress()).to_uint()) {
					WRITE_ISCSILOG(_T("Replaced client's old Name:") << (*iterMAC)->GetHostName() << _T(", IP:") << (*iterMAC)->GetIpAddress());
				}
				return *iterMAC;
			}
		}
		return nullptr;
		};

	auto findByIP = [&]() -> CiSCSISession_ptr {
		iterIP = indexIP.find(nClientIP);
		if (iterIP != indexIP.end()) {
			// 如果是 IsOEMICloud 模式且允许相同 IP，需进一步检查
			if (IsOEMICloud() && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENT_SAME_IP)) {
				// 查找相同 IP 的所有记录
				//允许相同IP的情况下，继续比较信息，看是否需要新建
				//https://www.boost.org/doc/libs/1_55_0/libs/multi_index/doc/tutorial/key_extraction.html
				auto range = indexIP.equal_range(nClientIP);
				while (range.first != range.second) {
					if ((*range.first)->GetMac() == sMac) {
						// 找到相同 IP 和 MAC 的记录
						iterIP = range.first;
						WRITE_ISCSILOG(_T("Find client by IP:") << ip2wstring(nClientIP)
							<< (" ,Mac:") << sMac.to_wstring()
							<< (" ,name:") << sName << (", SAME IP set:") << TRUE);
						if (sName != (*range.first)->GetHostName() || sMac != (*range.first)->GetMac()) {
							WRITE_ISCSILOG(_T("Replaced client's old Name:") << (*range.first)->GetHostName() << _T(", Mac:") << (*range.first)->GetMac().to_wstring());
						}
						return *range.first;
					}
					else if (!(*range.first)->GetTypeID()) {
						//不同mac,再查分组, 因为，新机器的默认分组是0
						//同分组（主机名变新，IP不变，MAC变新）就是分组是0
						// 默认分组 (TypeID=0)，处理 MAC 和分组逻辑
						iterIP = range.first;
						WRITE_ISCSILOG(_T("Find client by IP:") << ip2wstring(nClientIP)
							<< (" and Type: 0, Mac:") << sMac.to_wstring()
							<< (" ,name:") << sName << (", SAME IP set:") << TRUE);
						if (sName != (*range.first)->GetHostName() || sMac != (*range.first)->GetMac()) {
							WRITE_ISCSILOG(_T("Replaced client's old Name:") << (*range.first)->GetHostName() << _T(", Mac:") << (*range.first)->GetMac().to_wstring());
						}
						return *range.first;
					}
					++range.first;
				}
				// 未找到符合条件的记录, 返回第一次找到的记录
				return *iterIP;
			}
			else {
				WRITE_ISCSILOG(_T("Find client by IP:") << ip2wstring(nClientIP) << _T(", client ID: ") << (*iterIP)->GetID() << _T(", name: ") << sName << _T(", Mac: ") << sMac.to_wstring() << _T(", Remote IP : ") << pConnect->GetRemoteAddressW());
				if (sName != (*iterIP)->GetHostName() || sMac != (*iterIP)->GetMac()) {
					WRITE_ISCSILOG(_T("Replaced client's old Name:") << (*iterIP)->GetHostName() << _T(", Mac:") << (*iterIP)->GetMac().to_wstring());
				}
				return *iterIP;
			}
		}
		return nullptr;
		};

	auto findByName = [&]() -> CiSCSISession_ptr {
		if (!sName.empty()) {
			iterName = indexName.find(sName);
			if (iterName != indexName.end()
				&& (IsOEMICloud() || !sMac.is_valid()
					|| !(*iterName)->GetMac().is_valid() || sMac == (*iterName)->GetMac())) {
				WRITE_ISCSILOG(_T("Find client by Name:") << sName << _T(", client ID: ") << (*iterName)->GetID() << _T(", Mac: ") << sMac.to_wstring() << _T(", IP: ") << ip2wstring(nClientIP) << _T(", Remote IP : ") << pConnect->GetRemoteAddressW());
				if (ip2wstring(nClientIP) != (*iterName)->GetIpAddress()
					|| sMac != (*iterName)->GetMac()) {
					WRITE_ISCSILOG(_T("Replaced client old IP:") << (*iterName)->GetIpAddress() << (", Mac:") << (*iterName)->GetMac().to_wstring());
				}
				return *iterName;
			}
		}
		return nullptr;
		};

	if (IsOEMICloud()) {
		//武汉模式
		//不允许相同IP：先查找名字,未找到再找ip，未找到再找mac，都未找到就添加新计算机
		//允许相同IP： 先查找名字,未找到再找ip，(找到相同IP的计算机，有相同MAC, 确认找到.
		// 发现mac不一样，同分组找到，不同分组(默认组)新建),
		//未找到再找名字，都未找到就添加新计算机

		// 当不允许IP地址重复时：
		// 1. 首先，通过名称进行查找。
		// 2. 如果名称未找到，那么通过IP进行查找。
		// 3. 如果IP也未找到，那么通过MAC地址进行查找。
		// 4. 如果MAC地址也未找到，那么添加新的计算机。

		// 当允许IP地址重复时：
		// 1. 首先，通过名称进行查找。
		// 2. 如果名称未找到，那么通过IP进行查找。
		// 3. 如果找到了具有相同IP的计算机，并且MAC地址也相同，那么确认找到。
		// 4. 如果MAC地址不同，我们需要进一步检查分组。这是因为新机器的默认分组是0。
		//    如果在同一个分组（即使主机名已更新，IP保持不变，MAC地址已更新）中找到了计算机，
		//    并且该分组是0（默认分组），那么我们认为找到了该计算机。
		// 5. 如果IP未找到，那么通过名称进行查找。
		// 6. 如果名称也未找到，那么添加新的计算机。
		if (!(pSessionPtr = findByName())) {
			if (!(pSessionPtr = findByIP())) {
				pSessionPtr = findByMac();
			}
		}
	}
	else if (IsOEMShenzhenTongxinqiao()) {
		//深圳同芯桥, 只找 mac 地址
		pSessionPtr = findByMac();
	}
	else {
		//正常模式，先查找 MAC 地址，未找到再查找 IP 地址，未找到再查找名字，广东林泽查找 MAC 地址
		if (!(pSessionPtr = findByMac()) && !IsOEMCloudAtlas()) {
			if (!(pSessionPtr = findByIP()) && !IsOEMCloudAtlas()) {
				pSessionPtr = findByName();
			}
		}
	}

	// 添加新计算机逻辑
	bool bNewAddComputer = false;
	auto sHostName = sName;
	if (!pSessionPtr && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {
		pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);
		if (pSessionPtr) {
			if (sHostName.empty()) {
				sHostName = GetNewHostname_();
			}
			pSessionPtr->AddFromDHCP(GetComputerNewID_(), sHostName, sMac, nClientIP, FALSE);
			WRITE_ISCSILOG(_T("Add new client by Name:") << sHostName
				<< (", IP:") << ip2wstring(nClientIP)
				<< (", Mac:") << sMac.to_wstring());
			bNewAddComputer = true;
		}
	}

	// 检查注册和处理登录逻辑
	if (pSessionPtr) {
		if (m_pServer.CheckRegisterLogin(m_dwOnlineCount, pSessionPtr->GetSystemVersion())) {
			DWORD dwChangedBit = 0;
			if (pSessionPtr->AddClientConnect(pConnect, nClientIP, sHostName, sMac, dwChangedBit)) {
				if (bNewAddComputer) {
					if (AddSession_(pSessionPtr)) {
						boost::json::object oRow;
						if (pSessionPtr->SaveToOption(TRUE, TRUE, &oRow)) {
							AfterAddUser_(pSessionPtr, &oRow);
						}
						if (pNewSession) {
							*pNewSession = TRUE;
						}
					}
					else {
						pSessionPtr->Close();
						pSessionPtr.reset();
					}
				}
				else {
					AfterAddNewUser_(pSessionPtr, iterIP, iterMAC, iterName, dwChangedBit, pNewSession);
				}
			}
			else {
				pSessionPtr.reset();
			}
		}
		else {
			WRITE_ISCSILOG(_T("CheckRegisterLogin Error in client login, Remote IP:") << ip2wstring(nClientIP));
			pSessionPtr.reset();
		}
	}

	// 删除未登录客户端连接
	m_pServer.io_recycle_post(boost::bind(&CiSCSIConnectManager::DelUnloginClientConnect, this, pConnect));
	return pSessionPtr;
}

BOOL CiSCSIConnectManager::AddSession(CiSCSISession_ptr& pSession)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	return AddSession_(pSession);
}

BOOL CiSCSIConnectManager::AddSession_(CiSCSISession_ptr& pSession)
{
	auto result = m_cSessions.insert(pSession).second;
	if (result) {
		OnAddUser_(pSession);
	}
	return result;
}

void CiSCSIConnectManager::OnAddUser_(const CiSCSISession_ptr& pSession)
{
	if (!m_funOnAddUser.empty()) {//调用预设的回调函数
		std::vector<BYTE> pUserInfoBuffer;
		pSession->SetUserInfo(pUserInfoBuffer);
		m_funOnAddUser((PUSERINFO)pUserInfoBuffer.data());
	}
}

//添加一个临时的session,不上报给界面
BOOL CiSCSIConnectManager::AddNewSession_(CiSCSISession_ptr& pSession)
{
	BOOL result(FALSE);
	if (pSession->IsNewIP()) {
		result = m_cSessions.insert(pSession).second;
		if (result) {
			m_dwNewCount++;//增加新计算机计数
		}
	}
	return result;
}

void CiSCSIConnectManager::DelLun(DWORD nID, BOOL bSave, DWORD dwExcludeComputerID)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->GetID() != dwExcludeComputerID)//排除自己
			(*iter)->DelLun(nID, bSave);
	}
}

void CiSCSIConnectManager::CheckLunClosed(std::list<CVirtualDisk_Ptr> lVirtualDiskList)
{
	for (int i = 0; i < 30; i++) {
		for (auto iter = lVirtualDiskList.begin(); iter != lVirtualDiskList.end(); ) {
			bool bDeleted(false);
			{
				boost::mutex::scoped_lock lock(m_mLockSession);
				if (!m_cSessions.empty()) {
					for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
						bDeleted = (*iter)->CheckDeletedLun((*iter)->GetID());
						if (!bDeleted) break;
					}
				}
				else {
					//无session,已经删除
					bDeleted = true;
				}
			}
			if (bDeleted)
				iter = lVirtualDiskList.erase(iter);
			else
				iter++;
		}
		if (!lVirtualDiskList.empty()) {
			//https://blog.csdn.net/huang_xw/article/details/8453506
			Sleep_Second();
		}
		else {
			break;
		}
	}
}

//系统磁盘信息有变化时，刷新缓存或者通知客户端
void CiSCSIConnectManager::OnEditLun(DWORD nID)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->GetSystemNoHideIDset().count(nID) > 0) {
			//更新缓存
			BeforeEditUser_(*iter, CHANGE_SYSTEMDISK);
			AfterEditUser_(*iter, CHANGE_SYSTEMDISK);
		}
	}
}

int CiSCSIConnectManager::AddUser(PUSERINFO pUserInfo, boost::json::object* oRow /*= NULL*/)
{	//参数错误
	if (pUserInfo == NULL || pUserInfo->dwLength < sizeof(USERINFO)
		|| _tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetName)) == 0)
		return ERRORSTATUS_PARAMETER;

	std::string userip = ws2s(GETTEXT_FROMOFFSET(pUserInfo, sOffsetIP));
	if (userip.size() == 0
		|| ntohl(inet_addr(userip.c_str())) == INADDR_ANY
		|| ntohl(inet_addr(userip.c_str())) == INADDR_NONE)
		return ERRORSTATUS_PARAMETER;

	if (IsOEMICloud()) {
		//云桌面要验证添加的台数是否符合注册台数的数量
		if (!m_pServer.CheckRegisterLogin(GetAllUserCount(), _T(""))) {
			return ERRORSTATUS_USBKEYLOST;
		}
	}

	CMac oNewMac(GETTEXT_FROMOFFSET(pUserInfo, sOffsetMac));
	WORD dwXimaMacID(0);
	if (IsXimaServerHardware()) {
		if (oNewMac.GetXimaID(dwXimaMacID)) {
			if (dwXimaMacID && m_pServer.m_pXimaMacUpdater->CheckXimaMac(dwXimaMacID)) {
				return ERRORSTATUS_ADDHAVEID;
			}
		}
	}

	if (!CiSCSISession::CheckUserInfoHideDiskValid(pUserInfo)) {
		// 如果 vHideSysDiskIDs 包含 vSysDiskIDs 的所有元素
		return ERRORSTATUS_NOEXISTDISK;
	}

	boost::filesystem::path sNewDiskPath(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDisk));
	if (!sNewDiskPath.empty() && !sNewDiskPath.is_absolute()) {
		sNewDiskPath = boost::filesystem::absolute(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDisk), m_pServer.m_pOption.GetPersonalFolder());
	}

	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ) {
			if ((*iter)->GetID() == pUserInfo->dwID) {
				//ID存在
				return ERRORSTATUS_ADDHAVEID;
			}

			if (IsXimaServerHardware()) {
				if (dwXimaMacID && (*iter)->GetXimaMacID() == dwXimaMacID) {
					//ID存在
					return ERRORSTATUS_ADDHAVEID;
				}
			}
			if (wstringicmp((*iter)->GetHostName(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetName)) == 0) {
				//重名
				if ((*iter)->IsNewIP()) {
					BeforeDeleteUser_(*iter);
					iter = m_cSessions.erase(iter);
					continue;
				}
				else {
					return ERRORSTATUS_HAVENAME;
				}
			}

			auto ipExists = (wstringicmp((*iter)->GetIpAddress(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetIP)) == 0);
			bool condition = false;
			if (IsOEMICloud()) {
				auto dwSameIPSet = m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENT_SAME_IP);
				condition = ipExists && (dwSameIPSet == 0
					|| (dwSameIPSet == 1 && (*iter)->GetTypeID() == pUserInfo->dwTypeID));
			}
			else if (!IsNameAsIDShareIP()) {
				condition = ipExists;
			}
			if (condition) {
				if ((*iter)->IsNewIP()) {
					BeforeDeleteUser_(*iter);
					iter = m_cSessions.erase(iter);
					continue;
				}
				else {
					return ERRORSTATUS_HAVEIP;
				}
			}

			if (!IsNameAsIDShareIP()) {
				if (oNewMac.is_valid() && (*iter)->GetMac().is_valid()
					&& (*iter)->GetMac() == oNewMac) {//MAC已经存在
					if ((*iter)->IsNewIP()) {
						//删除临时计算机
						BeforeDeleteUser_(*iter);
						iter = m_cSessions.erase(iter);
						continue;
					}
					else {
						return ERRORSTATUS_HAVEMAC;
					}
				}
			}
			if (!sNewDiskPath.empty() && !(*iter)->GetPersonDisk().empty()
				&& wstringicmp((*iter)->GetPersonDisk(), sNewDiskPath.wstring().c_str()) == 0)//个人磁盘已经存在
				return ERRORSTATUS_PERSONALDISK;

			if (_tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder)) != 0
				&& wstringicmp(GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder), _T(ISCSI_PROROCOL_NAME)) != 0
				&& !(*iter)->GetShareFolder().empty()
				&& wstringicmp((*iter)->GetShareFolder(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder)) == 0)//个人磁盘已经存在
				return ERRORSTATUS_SHAREFOLDER;
			//因为中途有删除，所以继续放到最后
			iter++;
		}
	}

	if (!sNewDiskPath.empty()) {//创建新个人磁盘
		if (pUserInfo->bNewPersonDisk) {
			if (_tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDiskCopyFrom))) {//有参考磁盘
				if (!Copy_File(boost::filesystem::absolute(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDiskCopyFrom), m_pServer.m_pOption.GetSampleDiskPath()), sNewDiskPath, true))
					return ERRORSTATUS_COPYFROM;
			}
			else if (pUserInfo->dwPersonDiskCapacity > 0) {
				if (!ISHAREDISK_DiskFile_Create(sNewDiskPath.wstring().c_str(), pUserInfo->dwPersonDiskCapacity))
					return ERRORSTATUS_CREATEDISK;
			}
		}
		//更新磁盘容量
		if (!ISHAREDISK_Capacity_DiskFile(sNewDiskPath.wstring().c_str(), &pUserInfo->dwPersonDiskCapacity, NULL))
			return ERRORSTATUS_NOEXISTDISK;
	}

	pUserInfo->dwID = GetComputerNewID();//得到新计算机ID
	auto pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);
	boost::json::object oRow1;
	if (pSessionPtr->GetFromUserInfo(pUserInfo, sNewDiskPath.wstring().c_str())
		&& pSessionPtr->CheckDiskLastWriteTime()
		&& pSessionPtr->SaveToOption(TRUE, TRUE, oRow ? oRow : (&oRow1))
		&& AddSession(pSessionPtr)) {
		pUserInfo->dwID = pSessionPtr->GetID();
		AfterAddUser_(pSessionPtr, oRow ? NULL : (&oRow1));
		return ERRORSTATUS_SUCCESS;
	}
	return ERRORSTATUS_SAVEOPTION;
}

void CiSCSIConnectManager::AfterAddUser_(const CiSCSISession_ptr& pSession, boost::json::object* oRow /*= NULL*/)
{
	ReloadTemplate(pSession, true, false);
	if (pSession->IsCanBoot() && pSession->GetMac().is_valid()) {
		OnChangeClientState(pSession, DHCPCLIENTSTATE_ADD);
	}
	if (IsXimaServerHardware()) {
		if (pSession->GetXimaMacID()) {
			m_pServer.io_recycle_post(boost::bind(&CXimaMacUpdater::UpdateXimaMac, m_pServer.m_pXimaMacUpdater, pSession->GetXimaMacID(), pSession->GetMac()));
		}
	}
	if (oRow) {
		m_pServer.OnAddEditToSSE(*oRow, WEBSOCKET_JSON_COMMAND_ADDCOMPUTER_RESPONSE, \
			WEBSOCKET_JSON_COMMAND_ADDCOMPUTER);
	}
}

BOOL CiSCSIConnectManager::AddUserForMountDisk(DWORD nID, DWORD nIP)
{
	std::vector<BYTE> pUserInfoBuffer;
	const int aUserTextMeta[] = { OPTIONISCSI_USER_NAME, OPTIONISCSI_USER_SYSDISKID, OPTIONISCSI_USER_LUNS, OPTIONISCSI_USER_IP };
	pUserInfoBuffer.reserve(sizeof(USERINFO) + MAX_PATH * _countof(aUserTextMeta) * sizeof(TCHAR));
	pUserInfoBuffer.assign(sizeof(USERINFO), 0);
	SETTEXT_FROMOFFSET(pUserInfoBuffer, sOffsetName, ip2wstring(boost::asio::ip::address_v4(nIP)));
	SETTEXT_FROMOFFSET(pUserInfoBuffer, sOffsetSystemIDs, DWORD2wstring(nID));
	SETTEXT_FROMOFFSET(pUserInfoBuffer, sOffsetDiskIDs, NOGAMEDISK_STRING);
	SETTEXT_FROMOFFSET(pUserInfoBuffer, sOffsetIP, ip2wstring(boost::asio::ip::address_v4(nIP)));

	auto pUserInfo = (PUSERINFO)pUserInfoBuffer.data();
	pUserInfo->dwLength = pUserInfoBuffer.size();//矫正长度
	pUserInfo->bSuperUser = TRUE;//超级用户
	pUserInfo->bUnrestoredSystemImage = FALSE;//设置为用户自定义,避免被adduser, EditUser函数修改
	BOOL ret(FALSE);
	auto pUser = FindByIP(nIP);
	if (pUser) {
		pUserInfo->dwID = pUser->GetID();
		pUser.reset();
		ret = EditUser(pUserInfo) == ERRORSTATUS_SUCCESS;
	}
	else {
		ret = AddUser(pUserInfo) == ERRORSTATUS_SUCCESS;
	}
	return ret;
}

int CiSCSIConnectManager::EditUser(PUSERINFO pUserInfo, BOOL bSaveToFile /*= TRUE*/, boost::json::object* oRow /*= NULL*/)
{
	BOOL ret(ERRORSTATUS_NOFINDID);
	//参数错误
	if (pUserInfo == NULL || pUserInfo->dwLength < sizeof(USERINFO) || pUserInfo->dwID == 0
		|| _tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetName)) == 0)
		return ERRORSTATUS_PARAMETER;

	std::string userip = ws2s(GETTEXT_FROMOFFSET(pUserInfo, sOffsetIP));
	if (userip.size() == 0
		|| ntohl(inet_addr(userip.c_str())) == INADDR_ANY
		|| ntohl(inet_addr(userip.c_str())) == INADDR_NONE
		)
		return ERRORSTATUS_PARAMETER;

	if (!CiSCSISession::CheckUserInfoHideDiskValid(pUserInfo)) {
		// 如果 vHideSysDiskIDs 包含 vSysDiskIDs 的所有元素
		return ERRORSTATUS_NOEXISTDISK;
	}

	std::vector<BYTE> pUserInfoBuffer;
	if (m_pServer.m_VirDiskManager.CheckPersonalRestorePoint(pUserInfo, pUserInfoBuffer)) {
		//检查个人还原点,需要创建的就创建,需要恢复的就恢复//更新userinfo
		pUserInfo = (PUSERINFO)pUserInfoBuffer.data();
	}

	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		auto iter = m_cSessions.find(pUserInfo->dwID);
		if (iter != m_cSessions.end()) {
			auto pSessionPtr = *iter;
			CMac sMac(GETTEXT_FROMOFFSET(pUserInfo, sOffsetMac));
			DWORD dwChangedBit(0);
			if (IsXimaServerHardware()) {
				WORD dwXimaMacID(pSessionPtr->GetXimaMacID());//默认只能通过mac特殊形式修改
				if (sMac.GetXimaID(dwXimaMacID)) {
					//有输入值
					if (dwXimaMacID && pSessionPtr->GetXimaMacID() != dwXimaMacID) {
						//修改了ximaid,要检查新值是否重复
						if (m_pServer.m_pXimaMacUpdater->CheckXimaMac(dwXimaMacID)) {
							return ERRORSTATUS_ADDHAVEID;
						}
						dwChangedBit |= CHANGE_XIMA_ID;
					}
				}
			}
			if (wstringicmp(pSessionPtr->GetHostName(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetName)) != 0) {
				//计算机名修改,检查是否重复//有相同名字
				if (FindByName_(std::wstring(GETTEXT_FROMOFFSET(pUserInfo, sOffsetName)), TRUE).get()) {
					return ERRORSTATUS_HAVENAME;
				}
				//只允许在线修改
				else if (IsOEMShenzhenTongxinqiao() && !pSessionPtr->IsClientOnline()) {
					return ERRORSTATUS_HAVENAME;
				}
				dwChangedBit |= CHANGE_NAME;
				dwChangedBit |= CHANGE_DHCP;
			}
			if ((wstringicmp(pSessionPtr->GetIpAddress(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetIP)) != 0)) {
				//修改了IP
				auto ipSessionPtrlist = FindByIPs_(string2ip(GETTEXT_FROMOFFSET(pUserInfo, sOffsetIP)).to_uint(), TRUE);
				bool ipConflict = (!ipSessionPtrlist.empty());
				if (IsOEMICloud()) {
					auto dwSameIPSet = m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENT_SAME_IP);
					if (ipConflict && (dwSameIPSet == 0
						|| (dwSameIPSet == 1 && std::find_if(ipSessionPtrlist.begin(), ipSessionPtrlist.end(), [pUserInfo](const CiSCSISession_ptr& pSession) {
							return pSession->GetTypeID() == pUserInfo->dwTypeID;
							}) != ipSessionPtrlist.end()))) {
						return ERRORSTATUS_HAVEIP;
					}
				}
				else if (!IsNameAsIDShareIP()) {
					//已经存在
					if (ipConflict)
						return ERRORSTATUS_HAVEIP;
				}
				if (IsOEMShenzhenTongxinqiao() && !pSessionPtr->IsClientOnline()) {
					//只允许在线修改
					return ERRORSTATUS_HAVEIP;
				}
				dwChangedBit |= CHANGE_IP;
				dwChangedBit |= CHANGE_DHCP;
			}

			if ((pSessionPtr->GetMac() != sMac)) {
				if (!IsNameAsIDShareIP()) {
					if (sMac.is_valid()) {
						//检查是否存在//已经存在
						if (FindByMac_(sMac, TRUE).get())
							return ERRORSTATUS_HAVEMAC;
					}
				}
				if (pSessionPtr->GetMac().is_valid() && IsOEMShenzhenTongxinqiao()) {
					//不允许修改
					return ERRORSTATUS_HAVEMAC;
				}
				//保存旧的mac
				sMac = pSessionPtr->GetMac();
				dwChangedBit |= CHANGE_MAC;
				dwChangedBit |= CHANGE_DHCP;
			}
			if (pSessionPtr->IsSuperUser() != pUserInfo->bSuperUser) {
				if (pSessionPtr->IsDisklessBootMode_() && !pSessionPtr->IsIscsiOnline()
					&& (pUserInfo->bSuperUser != VHD_BOOT_MODE_SUPER
						|| CanAddSuperUser(wstringicmp(pSessionPtr->GetSystemID(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetSystemIDs)) == 0 ? pSessionPtr->GetSystemID() : (GETTEXT_FROMOFFSET(pUserInfo, sOffsetSystemIDs))))) {
					//变为超级用户，要检查超级用户数量
					dwChangedBit |= CHANGE_SUPERUSER;
				}
				else if ((pSessionPtr->IsSuperUser() == VHD_BOOT_MODE_NORMAL && pUserInfo->bSuperUser == VHD_BOOT_MODE_SAVE)
					|| (pSessionPtr->IsSuperUser() == VHD_BOOT_MODE_SAVE && pUserInfo->bSuperUser == VHD_BOOT_MODE_NORMAL)) {
					dwChangedBit |= CHANGE_SUPERUSER;
				}
				else if ((pSessionPtr->IsSuperUser() == VHD_BOOT_MODE_NORMAL && pUserInfo->bSuperUser == VHD_BOOT_MODE_PE)
					|| (pSessionPtr->IsSuperUser() == VHD_BOOT_MODE_PE && pUserInfo->bSuperUser == VHD_BOOT_MODE_NORMAL)) {
					dwChangedBit |= CHANGE_UPDATEP2PMODE;
				}
				else {
					return ERRORSTATUS_DELWRITEBACK;
				}
			}

			boost::filesystem::path sNewDiskPath(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDisk));
			if (!sNewDiskPath.empty() && !sNewDiskPath.is_absolute()) {
				sNewDiskPath = boost::filesystem::absolute(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDisk), m_pServer.m_pOption.GetPersonalFolder());
			}
			if (wstringicmp(pSessionPtr->GetPersonDisk(), sNewDiskPath.wstring().c_str()) != 0 || pUserInfo->bNewPersonDisk) {
				if (wstringicmp(pSessionPtr->GetPersonDisk(), sNewDiskPath.wstring().c_str()) != 0 && !sNewDiskPath.empty()) {
					//查找是否有重复的磁盘
					for (auto iter1 = m_cSessions.begin(); iter1 != m_cSessions.end(); ++iter1) {
						if (!(*iter1)->GetPersonDisk().empty() && wstringicmp((*iter1)->GetPersonDisk(), sNewDiskPath.wstring().c_str()) == 0)//已经存在
							return ERRORSTATUS_PERSONALDISK;
					}
				}
				dwChangedBit |= CHANGE_PERSONDISK;
				//pSessionPtr->ClosePersonalDisk();//关闭个人磁盘，下次重启再关闭
				if (!sNewDiskPath.empty()) {
					//创建新磁盘
					if (pUserInfo->bNewPersonDisk) {
						if (_tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDiskCopyFrom))) {
							//有参考磁盘
							if (!Copy_File(boost::filesystem::absolute(GETTEXT_FROMOFFSET(pUserInfo, sOffsetPersonDiskCopyFrom), m_pServer.m_pOption.GetSampleDiskPath()), sNewDiskPath.wstring().c_str(), true))
								return ERRORSTATUS_COPYFROM;
						}
						else if (pUserInfo->dwPersonDiskCapacity > 0) {
							if (!ISHAREDISK_DiskFile_Create(sNewDiskPath.wstring().c_str(), pUserInfo->dwPersonDiskCapacity))
								return ERRORSTATUS_CREATEDISK;
						}
					}
					//更新磁盘容量
					if (!ISHAREDISK_Capacity_DiskFile(sNewDiskPath.wstring().c_str(), &pUserInfo->dwPersonDiskCapacity, NULL))
						return ERRORSTATUS_NOEXISTDISK;
				}
			}
			if (wstringicmp(pSessionPtr->GetShareFolder(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder)) != 0) {
				//查找是否有重复的磁盘
				if (_tcslen(GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder)) != 0
					&& wstringicmp(GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder), _T(ISCSI_PROROCOL_NAME)) != 0) {
					for (auto iter1 = m_cSessions.begin(); iter1 != m_cSessions.end(); ++iter1) {
						if (!(*iter1)->GetShareFolder().empty() && wstringicmp((*iter1)->GetShareFolder(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetSharefolder)) == 0)//已经存在
							return ERRORSTATUS_SHAREFOLDER;
					}
				}
				dwChangedBit |= CHANGE_DHCP;
			}
			//检查用户信息是否变化，有变化的话要更新DHCP缓存
			if (pSessionPtr->CheckUserInfoChange(pUserInfo)) {
				//if (IsOEMShenzhenTongxinqiao() && !pSessionPtr->IsClientOnline()) {
				//	//只允许在线修改
				//	return ERRORSTATUS_HAVEIP;
				//}
				dwChangedBit |= CHANGE_DHCP;
			}

			//系统盘变化了
			if (wstringicmp(pSessionPtr->GetSystemID(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetSystemIDs)) != 0
				|| wstringicmp(pSessionPtr->GetHideSystemIDs(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetHideSystemIDs)) != 0
				|| pSessionPtr->GetDefaultNo() != pUserInfo->bDefaultNo)
				dwChangedBit |= CHANGE_SYSTEMDISK;

			if (wstringicmp(pSessionPtr->GetDiskIDs(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetDiskIDs)) != 0)
				dwChangedBit |= CHANGE_GAMEDISK;

			if (pUserInfo->bApplicationLayer) {
				//超级保留下，本地保留要打开
				pUserInfo->bLocalKeepMode = TRUE;
			}
			if (pSessionPtr->GetbLocalKeepMode() != pUserInfo->bLocalKeepMode) {
				dwChangedBit |= CHANGE_KEEPMODE;
			}

			if (pSessionPtr->GetVhdUpdateMode() != pUserInfo->bVHDUpdateMode
				|| pSessionPtr->CheckUpdateModeChange(pUserInfo)) {
				dwChangedBit |= CHANGE_UPDATEP2PMODE;
			}
			if (pSessionPtr->GetbKeepMode() != pUserInfo->bUnrestoredSystemImage) {
				dwChangedBit |= CHANGE_UNRESTOREIMAGE;
			}
			if (wstringicmp(pSessionPtr->GetTargetIP(), GETTEXT_FROMOFFSET(pUserInfo, sOffsetTargetIP)) != 0) {
				dwChangedBit |= CHANGE_TARGET_IP;
			}
			//计算机设置变化时，用于通知计算机
			if (pSessionPtr->CompareComputerSetFromUserInfo(pUserInfo)) {
				dwChangedBit |= CHANGE_COMPUTER_SET;
			}

			//在修改之前，根据变化,进行响应的操作，删除其他模块的缓存 //系统盘变化 //更新多系统启动菜单
			BeforeEditUser_(pSessionPtr, dwChangedBit);
			//因为普通用户也保留回写,所以超级用户,普通用户角色互换时,都要删除回写
			if (pSessionPtr->IsDisklessBootMode_() && !pSessionPtr->IsIscsiOnline()
				&& ((dwChangedBit & CHANGE_SUPERUSER) || (dwChangedBit & CHANGE_UNRESTOREIMAGE))) {
				//非在线用户，删除回写文件,锁定回写的文件不删除
				if (!pSessionPtr->IsLockWriteback() && !pSessionPtr->DelWriteBackFile())
					return ERRORSTATUS_DELWRITEBACK;
			}
			if (pSessionPtr->CompareUserProfileFromUserInfo(pUserInfo)
				|| pSessionPtr->CompareApplicationLayerFromUserInfo(pUserInfo)) {
				dwChangedBit |= CHANGE_USERPROFILE;
			}

			//if (dwChangedBit&CHANGE_IP){
			//	pSessionPtr->Close();//需要重新登陆
			//	pSessionPtr->DelWriteBackFile();//修改IP后回写文件变化,必须删除哦.
			//}
			//编辑数据 //矫正个人磁盘的最后写入时间 //数据变为与输入一致
			boost::json::object oRow1;
			if (pSessionPtr->GetFromUserInfo(pUserInfo, sNewDiskPath.wstring().c_str())
				&& pSessionPtr->CheckDiskLastWriteTime()
				&& pSessionPtr->SaveToOption(FALSE, bSaveToFile, oRow ? oRow : (&oRow1))) {
				//传统界面编辑才会通知sse
				AfterEditUser_(pSessionPtr, dwChangedBit, boost::make_shared<CMac>(sMac), oRow ? NULL : (&oRow1));
				ReloadTemplate(pSessionPtr, false, true);
				ret = ERRORSTATUS_SUCCESS;
			}
			else {
				ret = ERRORSTATUS_SAVEOPTION;
			}
		}
	}
	return ret;
}

int CiSCSIConnectManager::EditUsers(std::wstring pXmlString)
{
	int ret(ERRORSTATUS_SUCCESS);
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		auto pDataItem = RETVALUE_REF(retvalue)->GetTempListData(true);
		if (pDataItem) {
			CMarkup xml;
			xml.SetDoc(pXmlString);
			pDataItem->SetData(xml);
			auto IDs = pDataItem->GetIDString();
			auto arrayIDs = SplitIDs(IDs);
			for (auto iter = arrayIDs.begin(); iter != arrayIDs.end(); iter++) {
				auto pNewDataItem = RETVALUE_REF(retvalue)->GetTempListData();
				auto pEditItem = RETVALUE_REF(retvalue)->get_item_all(*iter);
				if (RETVALUE_BOOL(pEditItem) && pNewDataItem) {
					pNewDataItem->CopyFrom(RETVALUE_REF(pEditItem).get(), LISTDATATYPE_NORMAL);
					pDataItem->SetID(*iter);
					if (pNewDataItem->CopyFrom(pDataItem.get(), LISTDATATYPE_NORMAL, true)) {
						std::vector<BYTE> pUserInfoBuffer;
						CListDataUser::GetItemData(pNewDataItem, pUserInfoBuffer);
						ret = EditUser((PUSERINFO)pUserInfoBuffer.data(), FALSE);
						if (ret != ERRORSTATUS_SUCCESS)
							break;
					}
				}
			}
			if (!arrayIDs.empty() && ret == ERRORSTATUS_SUCCESS) {
				m_pServer.m_pOption.Save();
			}
		}
	}
	return ret;
}

//更新dhcp缓存,关闭多菜单显示 //删除启动菜单//更新多系统启动菜单
void CiSCSIConnectManager::BeforeEditUser_(const CiSCSISession_ptr& pSession, DWORD dwChangedBit)
{
	if ((dwChangedBit & CHANGE_IP) || (dwChangedBit & CHANGE_SYSTEMDISK) || (dwChangedBit & CHANGE_DHCP)) {
		OnDelBootMenu(pSession->GetIpAddressU());
	}
	//DHCP或者系统盘变化为无启动盘 //改为禁止启动了或者改变了超级用户的状态 //更新dhcp缓存
	if ((dwChangedBit & CHANGE_IP) || (dwChangedBit & CHANGE_MAC) || (dwChangedBit & CHANGE_NAME)
		|| (dwChangedBit & CHANGE_SUPERUSER) || (dwChangedBit & CHANGE_SYSTEMDISK) || (dwChangedBit & CHANGE_DHCP)
		|| (dwChangedBit & CHANGE_SYSTEMDISK)) {
		OnDelDHCPCLient_(pSession);
	}
	if (dwChangedBit & CHANGE_UPDATEP2PMODE) {
		pSession->ResetVHDUpdateStatus(false);
	}
	if (IsXimaServerHardware()) {
		if (pSession->GetXimaMacID() && (dwChangedBit & CHANGE_XIMA_ID)) {
			//删除旧值
			m_pServer.io_recycle_post(boost::bind(&CXimaMacUpdater::DeleteXimaMac, m_pServer.m_pXimaMacUpdater, pSession->GetXimaMacID()));
			WRITE_ISCSILOG(_T("Delete Xima ID :") << pSession->GetXimaMacID() << _T(", MAC:") << pSession->GetMac().to_wstring());
		}
	}
}

void CiSCSIConnectManager::SendCommandToUserOnEdit(const CiSCSISession_ptr& pSession, int nAction /*= ISHAREDISKCLIENTCMD_ONEDITUSER*/, DWORD64 dwPara1 /*= FALSE*/) {
	//避免客户端登陆时造成互相持锁
	m_pServer.SendCommandToUser_Post(pSession, nAction, dwPara1);
	if (m_lMulticaseSenders && !pSession->IsClientOnline()) {
		m_pServer.io_recycle_post(boost::bind(&CMulticastSender::SendCommandToUser, m_lMulticaseSenders, pSession, nAction, dwPara1));
	}
}

void CiSCSIConnectManager::AfterEditUser_(const CiSCSISession_ptr& pSession, DWORD dwChangedBit, CMac_ptr pOldMac /*= CMac_ptr()*/, boost::json::object* oRow /*= NULL*/)
{
	//因为IP变化，所以要调用两次OnDelBootMenu，OnDelDHCPCLient_
	if ((dwChangedBit & CHANGE_IP) || (dwChangedBit & CHANGE_SYSTEMDISK) || (dwChangedBit & CHANGE_DHCP)) {
		OnDelBootMenu(pSession->GetIpAddressU());
	}
	//DHCP或者系统盘变化为无启动盘 //改为禁止启动了或者改变了超级用户的状态 //更新dhcp缓存
	if ((dwChangedBit & CHANGE_IP) || (dwChangedBit & CHANGE_MAC) || (dwChangedBit & CHANGE_NAME)
		|| (dwChangedBit & CHANGE_SUPERUSER) || (dwChangedBit & CHANGE_SYSTEMDISK) || (dwChangedBit & CHANGE_DHCP)
		|| (dwChangedBit & CHANGE_FROM_CLIENT)) {
		OnDelDHCPCLient_(pSession);
	}
	if (dwChangedBit & CHANGE_SUPERUSER) {
		//超级用户变化
		m_pServer.io_recycle_post(boost::bind(&CiSCSIConnectManager::UpdateSuperUserCount, this));
	}
	if ((dwChangedBit & CHANGE_SUPERUSER) || (dwChangedBit & CHANGE_UNRESTOREIMAGE)) {
		pSession->OnChangeSuperUser();
	}

	UpdateSessionsIndex_(pSession->GetID(), dwChangedBit);
	if ((dwChangedBit & CHANGE_MAC)) {
		OnChangeClientState(pSession, DHCPCLIENTSTATE_EDIT, pOldMac);
	}
	//修改为不允许启动
	if ((dwChangedBit & CHANGE_DHCP) && pSession->GetMac().is_valid()) {
		OnChangeClientState(pSession, (pSession->IsCanBoot() ? DHCPCLIENTSTATE_ADD : DHCPCLIENTSTATE_DEL));
	}
	if ((dwChangedBit & CHANGE_SUPERUSER)) {
		OnChangeClientState(pSession, (pSession->IsSuperUser() ? DHCPCLIENTSTATE_SUPER : DHCPCLIENTSTATE_UNSUPER));
	}
	//通知新客户端，系统磁盘有变化
	if (pSession->GetbISOfile() || (dwChangedBit & CHANGE_USERPROFILE)) {
		if ((dwChangedBit & CHANGE_SYSTEMDISK) || (dwChangedBit & CHANGE_GAMEDISK)) {
			SendCommandToUserOnEdit(pSession, ISHAREDISKCLIENTCMD_ONEDITUSER, FALSE);
		}
		//通知新客户端，IP有变化
		if ((dwChangedBit & CHANGE_NAME) || (dwChangedBit & CHANGE_IP)
			|| (dwChangedBit & CHANGE_DHCP) || (dwChangedBit & CHANGE_KEEPMODE)
			|| (dwChangedBit & CHANGE_USERPROFILE) || (dwChangedBit & CHANGE_TARGET_IP)
			|| (dwChangedBit & CHANGE_UPDATEP2PMODE)) {
			//客户机修改IP后，不自动重启
			SendCommandToUserOnEdit(pSession, ISHAREDISKCLIENTCMD_CHANGEIP, FALSE);
		}
	}
	//计算机设置变化时，用于通知计算机
	if (dwChangedBit & CHANGE_COMPUTER_SET) {
		SendCommandToUserOnEdit(pSession, ISHAREDISKCLIENTCMD_ONEDITUSER, TRUE);
	}

	if (IsXimaServerHardware()) {
		if (pSession->GetXimaMacID() && ((dwChangedBit & CHANGE_MAC) || (dwChangedBit & CHANGE_XIMA_ID))) {
			//更新新值
			m_pServer.io_recycle_post(boost::bind(&CXimaMacUpdater::UpdateXimaMac, m_pServer.m_pXimaMacUpdater, pSession->GetXimaMacID(), pSession->GetMac()));
			WRITE_ISCSILOG(_T("Update Xima ID:") << pSession->GetXimaMacID() << _T(", Mac:") << pSession->GetMac().to_wstring());
		}
	}
	OnEditUser_(pSession, oRow);
}

int CiSCSIConnectManager::AddEditComputers(const rapidjson::Document::GenericValue& dJsonPersonal, bool bAdd)
{
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		return ProcessJsonCommand1(dJsonPersonal, [this, bAdd](CTreeListDataItem_ptr pDataItem)->int {
			if (pDataItem) {
				std::vector<BYTE> pUserInfoBuffer;
				CListDataUser::GetItemData(pDataItem, pUserInfoBuffer);
				return bAdd ? AddUser((PUSERINFO)pUserInfoBuffer.data()) : EditUser((PUSERINFO)pUserInfoBuffer.data());
			}
			return ERRORSTATUS_PARAMETER;
			}, RETVALUE_REF(retvalue), bAdd ? FALSE : TRUE);
	}
	return ERRORSTATUS_PARAMETER;
}

int CiSCSIConnectManager::AddEditComputers(const boost::json::value& dJsonPersonal, boost::json::object& dDocRsp, bool bAdd)
{
	int ret(ERRORSTATUS_PARAMETER);
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		ret = ProcessBoostJsonCommand(dJsonPersonal, dDocRsp, [this, bAdd](CTreeListDataItem_ptr pDataItem, boost::json::object& oRow)->int {
			if (pDataItem) {
				std::vector<BYTE> pUserInfoBuffer;
				CListDataUser::GetItemData(pDataItem, pUserInfoBuffer);
				return bAdd ? AddUser((PUSERINFO)pUserInfoBuffer.data(), &oRow) : EditUser((PUSERINFO)pUserInfoBuffer.data(), TRUE, &oRow);
			}
			return ERRORSTATUS_PARAMETER;
			}, RETVALUE_REF(retvalue), bAdd ? FALSE : TRUE);
	}
	return ret;
}

int CiSCSIConnectManager::EditComputersObject(rapidjson::Document::GenericValue& dJsonPersonal, rapidjson::Document::AllocatorType& allocator)
{
	int ret(ERRORSTATUS_PARAMETER);
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		ret = ProcessJsonEditIDsCommand(dJsonPersonal, allocator, [this](CTreeListDataItem_ptr pDataItem)->int {
			if (pDataItem) {
				std::vector<BYTE> pUserInfoBuffer;
				CListDataUser::GetItemData(pDataItem, pUserInfoBuffer);
				return EditUser((PUSERINFO)pUserInfoBuffer.data(), FALSE);
			}
			return ERRORSTATUS_PARAMETER;
			}, RETVALUE_REF(retvalue));
		if (ret == ERRORSTATUS_SUCCESS) {
			m_pServer.m_pOption.Save();
		}
	}
	return ret;
}

int CiSCSIConnectManager::EditComputersObject(boost::json::value& dJsonPersonal, boost::json::object& dDocRsp)
{
	int ret(ERRORSTATUS_PARAMETER);
	auto retvalue = m_pServer.m_pOption.GetOptionSub(OPTIONISCSI_USER);
	if (RETVALUE_BOOL(retvalue)) {
		ret = ProcessBoostJsonEditIDsCommand(dJsonPersonal, dDocRsp, [this](CTreeListDataItem_ptr pDataItem, boost::json::object& oRow)->int {
			if (pDataItem) {
				std::vector<BYTE> pUserInfoBuffer;
				CListDataUser::GetItemData(pDataItem, pUserInfoBuffer);
				return EditUser((PUSERINFO)pUserInfoBuffer.data(), FALSE, &oRow);
			}
			return ERRORSTATUS_PARAMETER;
			}, RETVALUE_REF(retvalue));
		if (ret == ERRORSTATUS_SUCCESS) {
			m_pServer.m_pOption.Save();
		}
	}
	return ret;
}

int CiSCSIConnectManager::DelUser(DWORD nID, boost::json::object* dDocRsp /*= NULL*/)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	auto iter = m_cSessions.find(nID);
	if (iter != m_cSessions.end() && !(*iter)->IsOnline()) {
		BeforeDeleteUser_(*iter);
		m_cSessions.erase(iter);
		boost::json::object dDocRspDump;
		boost::json::object& dDocRspRef = dDocRsp ? (*dDocRsp) : dDocRspDump;
		m_pServer.m_pOption.GetComputerInfo(dDocRspRef, DWORD2string(nID).c_str());
		if (m_pServer.m_pOption.DelSubItem(OPTIONISCSI_USER, nID)
			&& m_pServer.m_pOption.Save()) {
			if (!m_funOnDeleteUser.empty())
				m_funOnDeleteUser(nID);
			if (!dDocRsp) {
				m_pServer.OnRawObjectToSSE(dDocRspRef, \
					WEBSOCKET_JSON_COMMAND_REMOVECOMPUTER_RESPONSE, \
					WEBSOCKET_JSON_COMMAND_REMOVECOMPUTER);
			}
			return ERRORSTATUS_SUCCESS;
		}
		else {
			return ERRORSTATUS_SAVEOPTION;
		}
	}
	return ERRORSTATUS_NOFINDID;
}

void CiSCSIConnectManager::BeforeDeleteUser_(const CiSCSISession_ptr& pSession)
{
	if (IsXimaServerHardware()) {
		if (pSession->GetXimaMacID()) {
			//删除旧值
			m_pServer.io_recycle_post(boost::bind(&CXimaMacUpdater::DeleteXimaMac, m_pServer.m_pXimaMacUpdater, pSession->GetXimaMacID()));//避免双重加锁
		}
	}
	BeforeEditUser_(pSession, CHANGE_SYSTEMDISK);
	if (pSession->GetMac().is_valid()) {
		OnChangeClientState(pSession, DHCPCLIENTSTATE_DEL);//更新多服务器缓存
	}
	if (pSession->IsSuperUser() == VHD_BOOT_MODE_SUPER) {
		pSession->SetSuperUser(VHD_BOOT_MODE_NORMAL);
		AfterEditUser_(pSession, CHANGE_SUPERUSER, CMac_ptr());
	}
	pSession->Close();
	pSession->DelWriteBackFile();//删除回写文件
	if (pSession->IsNewIP()) {
		m_dwNewCount--;
	}
}

//批量编辑所有的用户属性
int CiSCSIConnectManager::EditAllUser(DWORD nType, DWORD64 nVal, DWORD nClassID)
{
	int ret(ERRORSTATUS_NOFINDID);
	boost::mutex::scoped_lock writelock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		boost::json::object oRow;
		switch (nType) {
		case ALLEDITUSER_SYSTEMDISK:
			if (0 == nClassID || (*iter)->GetTypeID() == nClassID) {
				auto nSystemID = (*iter)->GetSystemIDset();
				//原来没系统盘，直接添加。原来有一个系统盘，不一样直接修改。
				//原来有多个系统盘，旧的和新的都在，调换顺序。旧的在新的不在，替换。
				if (nSystemID.size() != 1 || nSystemID.count(nVal) == 0) {
					if ((*iter)->ChangeSysDisk_((*iter)->GetSystemIDFirst(), nVal, FALSE)) {
						ret = ERRORSTATUS_SUCCESS;
					}
				}
			}
			break;
		case ALLEDITUSER_BOOTMODE:
			if ((*iter)->GetTypeID() == nClassID
				&& ((*iter)->SetBootModeBak(nVal) || (*iter)->SetBootMode(nVal))
				&& (*iter)->SaveToOption(FALSE, FALSE, &oRow)) {
				AfterEditUser_((*iter), CHANGE_DHCP, CMac_ptr(), &oRow);
				ret = ERRORSTATUS_SUCCESS;
			}
			break;
		case ALLEDITUSER_MEMORYCACHE:
			if ((*iter)->GetTypeID() == nClassID
				&& ((*iter)->SetClientCacheSizeBak(nVal) || (*iter)->SetClientCacheSize(nVal))
				&& (*iter)->SaveToOption(FALSE, FALSE, &oRow)) {
				AfterEditUser_((*iter), CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
				ret = ERRORSTATUS_SUCCESS;
			}
			break;
		case ALLEDITUSER_FILECACHE:
			if ((*iter)->GetTypeID() == nClassID
				&& ((*iter)->SetClientFileCacheSizeBak(nVal) || (*iter)->SetClientFileCacheSize(nVal))
				&& (*iter)->SaveToOption(FALSE, FALSE, &oRow)) {
				AfterEditUser_((*iter), CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
				ret = ERRORSTATUS_SUCCESS;
			}
			break;
		}
	}
	if (ERRORSTATUS_SUCCESS == ret) {
		m_pServer.m_pOption.Save();
	}
	ret = ERRORSTATUS_SUCCESS;
	return ret;
}

//查询vnc客户端是否在线，不在线就给客户端发送启动的命令
BOOL CiSCSIConnectManager::VNCComputer(DWORD nID)
{
	BOOL bVNC(FALSE);
	boost::mutex::scoped_lock lock(m_mLockSession);
	auto iter = m_cSessions.find(nID);
	if (iter != m_cSessions.end() && (*iter)->IsClientOnline()) {
		bVNC = (*iter)->IsVNCOnline();
		//		if (!bVNC) {
					//每次都发送启动命令，可以支持反向连接
		(*iter)->SendCommandToUser(ISHAREDISKCLIENTCMD_STARTVNC);
		//		}
	}
	return bVNC;
}

BOOL CiSCSIConnectManager::VNCComputers(const std::string& sIDs, rapidjson::Document& dJsonDoc)
{
	rapidjson::Document::AllocatorType& allocator = dJsonDoc.GetAllocator(); //获取分配器
	rapidjson::Value lRows(rapidjson::kArrayType);//生成一个Array类型的元素，用来存放Object
	auto ret = DeleteIDsJsonCommand(sIDs, [this, &lRows, &allocator](DWORD nID)->int {
		rapidjson::Value sName, oRow(rapidjson::kObjectType);
		oRow.AddMember(LISTDATAITEM_ID_NAME_UTF8, (unsigned int)nID, allocator);
		sName.SetString(CListDataUser::GetIDNameUtf8(LISTDATAUSER_VNC), allocator);
		oRow.AddMember(sName, VNCComputer(nID), allocator);
		lRows.PushBack(oRow, allocator); //添加到数组
		return TRUE;
		}, TRUE);
	dJsonDoc.AddMember(WEBSOCKET_JSON_COMMAND_TOTAL, lRows.Size(), allocator);
	dJsonDoc.AddMember(WEBSOCKET_JSON_COMMAND_ROWS, lRows, allocator);
	return ret;
}

BOOL CiSCSIConnectManager::VNCComputers(const std::string& sIDs, boost::json::object& dJsonDoc)
{
	boost::json::array lRows;//生成一个Array类型的元素，用来存放Object
	auto ret = DeleteIDsJsonCommand(sIDs, [this, &lRows](DWORD nID)->int {
		boost::json::object oRow;
		oRow[LISTDATAITEM_ID_NAME_UTF8] = (unsigned int)nID;
		oRow[CListDataUser::GetIDNameUtf8(LISTDATAUSER_VNC)] = VNCComputer(nID);
		lRows.push_back(oRow);
		return TRUE;
		}, TRUE);
	SetJsonRows(dJsonDoc, lRows);
	return ret;
}

BOOL CiSCSIConnectManager::ResetUser(DWORD nID)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	WRITE_ISCSILOG(_T("CiSCSIConnectManager ResetUser ID : ") << nID);
	if (nID) {
		auto iter = m_cSessions.find(nID);
		if (iter != m_cSessions.end()) {
			ResetUser_(*iter);
			return TRUE;
		}
	}
	else {
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			ResetUser_(*iter);
		}
		return TRUE;
	}
	return FALSE;
}

//强制复位用户，避免假上线导致客户机无法开机
BOOL CiSCSIConnectManager::ResetUser_(const CiSCSISession_ptr& pSession, BOOL bSuperUser /*= FALSE*/)
{
	BOOL ret(FALSE);
	if (pSession && pSession->IsOnline()) {
		pSession->ResetUser();
		if (bSuperUser && pSession->m_IscsiPara.IsDisklessBoot()
			&& pSession->IsSuperUser() == VHD_BOOT_MODE_NORMAL) {
			if (!pSession->ChangeSuperUser(VHD_BOOT_MODE_SUPER)) {
				//ChangeSuperUser 会调用 OnEditUser_， 所以要检查返回值
				OnEditUser_(pSession);
			}
		}
		else {
			OnEditUser_(pSession);
		}
		ret = TRUE;
	}
	return ret;
}

BOOL CiSCSIConnectManager::RebootSaveUser(DWORD nID)
{
	BOOL ret(FALSE);
	auto pUserSession = GetUser(nID);
	if (pUserSession && pUserSession->IsIscsiOnline() && pUserSession->IsSuperUser() == VHD_BOOT_MODE_NORMAL) {
		ret = pUserSession->ChangeSuperUser(VHD_BOOT_MODE_SAVE, FALSE);
		if (ret) {
			SendCommandToUser(nID, ISHAREDISKCLIENTCMD_REBOOT, TRUE);
		}
	}
	return ret;
}

BOOL CiSCSIConnectManager::RebootSaveUsers(const std::set<DWORD>& nIDs)
{
	BOOL ret(FALSE);
	if (nIDs.size() == 1) {
		ret = RebootSaveUser(*nIDs.begin());
	}
	else {
		for (auto iter = nIDs.begin(); iter != nIDs.end(); iter++) {
			ret = RebootSaveUser(*iter);
			if (!ret)
				break;
		}
	}
	return ret;
}

BOOL CiSCSIConnectManager::ClientCmdUser(std::set<DWORD> nIDs, int nAction, DWORD64 dwPara1, const std::string& sPara2)
{
	boost::mutex::scoped_lock writelock(m_mLockSession);
	BOOL ret(FALSE);
	if (ISHAREDISKCLIENTCMD_POWERON != nAction) {
		bool bNeedSaveToFile(false);
		if (nIDs.size() == 1) {
			ret = ClientCmdUser_(GetUser_(*(nIDs.begin())), nAction, dwPara1, sPara2, bNeedSaveToFile);
		}
		else {
			for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
				auto iter1 = nIDs.find((*iter)->GetID());
				if (iter1 != nIDs.end()) {
					ret |= ClientCmdUser_(*iter, nAction, dwPara1, sPara2, bNeedSaveToFile);
					nIDs.erase(iter1);
				}
				if (nIDs.empty()) break;
			}
		}
		if (ret && bNeedSaveToFile) {
			m_pServer.m_pOption.Save();
		}
	}
	else {
		//唤醒开机由另外的模块执行
		std::set<CMac> lMacs;
		if (nIDs.size() == 1) {
			auto pSession = GetUser_(*(nIDs.begin()));
			if (pSession) {
				lMacs.insert(pSession->GetMac());
			}
		}
		else {
			for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
				auto iter1 = nIDs.find((*iter)->GetID());
				if (iter1 != nIDs.end()) {
					lMacs.insert((*iter)->GetMac());
					nIDs.erase(iter1);
				}
				if (nIDs.empty()) break;
			}
		}
		ret = PowerOnClient(lMacs);
	}
	return ret;
}

BOOL CiSCSIConnectManager::ClientCmdUser_(const CiSCSISession_ptr& pSession, int nAction, DWORD64 dwPara1, const std::string& sPara2, bool& bNeedSaveToFile)
{
	BOOL ret(FALSE);
	if (pSession) {
		switch (nAction) {
		case ISHAREDISKCLIENTCMD_POWERON:
		{
			ret = PowerOnClient(pSession->GetMac());
		}
		break;
		case ISHAREDISKCLIENTCMD_RESET:
			//复位命令，不需要发到客户机
			ret = ResetUser_(pSession, dwPara1);
			break;
		case ISHAREDISKCLIENTCMD_STARTVNC:
		{
			int dwServerVNCPort = m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_SERVER_VNC_LISTEN_PORT) ?
				m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_SERVER_VNC_LISTEN_PORT) : GetIShareDiskVNCClientPort();  //云服务器上VNC监听端口，用于客户机上的vnc反向连接端口
			int dwClientVNCPort = m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENT_VNC_LISTEN_PORT) ?
				m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_CLIENT_VNC_LISTEN_PORT) : GetVNCClientListenPort();  //客户机上VNC监听端口
			dwPara1 = (dwClientVNCPort == 5900) ? dwServerVNCPort : MAKELONGLONG(dwServerVNCPort, dwClientVNCPort);
		}
		case ISHAREDISKCLIENTCMD_STARTUPDATE:
		case ISHAREDISKCLIENTCMD_REBOOT:
		case ISHAREDISKCLIENTCMD_POWEROFF:
		case ISHAREDISKCLIENTCMD_STOPUPDATE:
		case ISHAREDISKCLIENTCMD_INSTALLVHD:
		case ISHAREDISKCLIENTCMD_UNLOCKSCREEN:
		default:
			//新客户端专用命令
			ret = SendCommandToUser_(pSession, nAction, dwPara1, sPara2, bNeedSaveToFile);
			break;
		}
	}
	return ret;
}

BOOL CiSCSIConnectManager::SendCommandToUser(DWORD nID, int nAction, DWORD64 dwPara1, const std::string& sPara2) {
	boost::mutex::scoped_lock writelock(m_mLockSession);
	bool bNeedSaveToFile(false);
	auto ret = SendCommandToUser_(GetUser_(nID), nAction, dwPara1, sPara2, bNeedSaveToFile);
	if (ret && bNeedSaveToFile) {
		m_pServer.m_pOption.Save();
	}
	return ret;
}

BOOL CiSCSIConnectManager::SendCommandToUser_(const CiSCSISession_ptr& pUserSession, int nAction, DWORD64 dwPara1, const std::string& sPara2, bool& bNeedSaveToFile)
{
	auto funSendCommand = [this](const CiSCSISession_ptr& pUserSession, int nAction, DWORD64 dwPara1, const std::string& sPara2) {
		BOOL ret(FALSE);
		if (pUserSession->IsClientOnline()) {
			ret = pUserSession->SendCommandToUser(nAction, dwPara1, sPara2);
		}
		else if (pUserSession->IsMulticastOnline() && sPara2.empty() && m_lMulticaseSenders) {
			ret = m_lMulticaseSenders->SendCommandToUser(pUserSession, nAction, dwPara1);
		}
		return ret;
		};
	BOOL ret(FALSE);
	if (pUserSession) {
		//排除 重启和关机命令，不在线时，不保存到发送队列
		if (nAction != ISHAREDISKCLIENTCMD_REBOOT && nAction != ISHAREDISKCLIENTCMD_POWEROFF) {
			ret = funSendCommand(pUserSession, nAction, dwPara1, sPara2);
		}
		//不在线时，也要保存起来。
		boost::json::object oRow;
		switch (nAction) {
		case ISHAREDISKCLIENTCMD_STARTUPDATE:
			if (pUserSession->SetVhdUpdateModeBak(dwPara1, sPara2, true)
				|| pUserSession->SetVhdUpdateMode(dwPara1, sPara2, true)) {
				BeforeEditUser_(pUserSession, CHANGE_UPDATEP2PMODE);
				ret = pUserSession->SaveToOption(FALSE, FALSE, &oRow);
				if (ret) {
					AfterEditUser_(pUserSession, CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
					bNeedSaveToFile = true;
				}
			}
			break;
		case ISHAREDISKCLIENTCMD_INSTALLVHD:
			//安装vhd命令失败后，保存起来，下次在线时发送
			if (!ret) {
				WRITE_ISCSIFILELOG(_T("SendCommandToUser_ ISHAREDISKCLIENTCMD_INSTALLVHD Error Client IP: ") << pUserSession->GetIpAddress()
					<< _T("ClientOnline: ") << pUserSession->IsClientOnline() << _T("MulticastOnline: ") << pUserSession->IsMulticastOnline()
					<< _T("Command: ") << nAction << _T(", descript: ") << GetClientCommandDescript(nAction) << _T(", result: ") << ret);
				//等待下一次发送
				pUserSession->AddSendedClientCmds(nAction, dwPara1, sPara2);
			}
			if (dwPara1) {
				//自动开始更新
				pUserSession->SetVhdUpdateMode(VHD_UPDATE_INSTALL_UPDATE_GRUB);
				pUserSession->UpdateCommandTime();
				BeforeEditUser_(pUserSession, CHANGE_UPDATEP2PMODE);
			}
			ret = pUserSession->SaveToOption(FALSE, FALSE, &oRow);
			if (ret) {
				AfterEditUser_(pUserSession, CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
				bNeedSaveToFile = true;
			}
			break;
		case ISHAREDISKCLIENTCMD_STOPUPDATE:
			//使手动更新的记录状态失效，避免客户机重启后，继续更新
			if (pUserSession->ResetVHDUpdateStatus(true)) {
				ret = pUserSession->SaveToOption(FALSE, FALSE, &oRow);
				if (ret) {
					AfterEditUser_(pUserSession, CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
					bNeedSaveToFile = true;
				}
			}
			break;
		case ISHAREDISKCLIENTCMD_REBOOT:
			if (!sPara2.empty()) {
				ret = ((pUserSession->ParseSetRebootParaBak(sPara2)
					|| pUserSession->ParseSetRebootPara(sPara2))
					&& pUserSession->SaveToOption(FALSE, FALSE, &oRow)) ? TRUE : FALSE;
				if (ret) {
					AfterEditUser_(pUserSession, CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
					bNeedSaveToFile = true;
				}
			}
			else {
				//立即重启
				ret = funSendCommand(pUserSession, nAction, dwPara1, sPara2);
			}
			break;
		case ISHAREDISKCLIENTCMD_POWEROFF:
			if (!sPara2.empty()) {
				ret = ((pUserSession->ParseSetShutdownParaBak(sPara2)
					|| pUserSession->ParseSetShutdownPara(sPara2))
					&& pUserSession->SaveToOption(FALSE, FALSE, &oRow)) ? TRUE : FALSE;
				if (ret) {
					AfterEditUser_(pUserSession, CHANGE_COMPUTER_SET, CMac_ptr(), &oRow);
					bNeedSaveToFile = true;
				}
			}
			else {
				//立即关机
				ret = funSendCommand(pUserSession, nAction, dwPara1, sPara2);
			}
			break;
		}
	}
	return ret;
}

CiSCSISession_ptr CiSCSIConnectManager::GetUser_(DWORD nID)
{
	auto iter = m_cSessions.find(nID);
	if (iter != m_cSessions.end()) {
		return *iter;
	}
	else {
		return CiSCSISession_ptr();
	}
}

BOOL CiSCSIConnectManager::StatusGetUserAll(ONUSERSTATUS_CALLBACKFUN pFun, PVOID pContext, int bOnline)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	auto pStatus = boost::make_shared<USERSTATUS>();
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if (!(*iter)->IsNewIP() && (bOnline == USER_STATUS_ALL || (*iter)->IsOnline() == bOnline ? TRUE : FALSE)) {
			(*iter)->SetUserStatus(pStatus.get());
			pFun(pStatus.get(), pContext);
		}
	}
	return TRUE;
}

bool CiSCSIConnectManager::GetComputerStatus(rapidjson::Document& dJsonDoc, PPAGE_SET_QUERY pPageSetQuery /*= NULL*/)
{
	PAGE_SET_QUERY defaultSet;
	PAGE_SET_QUERY& pageSetQuery = pPageSetQuery ? (*pPageSetQuery) : defaultSet;
	enum ORDERRULE { ORDER_UNKNOW, ORDER_NAME_ASCE, ORDER_NAME_DESC, ORDER_IP_ASCE, ORDER_IP_DESC };
	ORDERRULE orderrule(ORDER_UNKNOW);
	if (!pageSetQuery.OrderBy.empty()) {
		if (wstring2icmp(CListDataUser::GetIDName(LISTDATAUSER_NAME), pageSetQuery.OrderBy) == 0) {
			orderrule = pageSetQuery.Desc ? ORDER_NAME_DESC : ORDER_NAME_ASCE;
		}
		else if (wstring2icmp(CListDataUser::GetIDName(LISTDATAUSER_IP), pageSetQuery.OrderBy) == 0) {
			orderrule = pageSetQuery.Desc ? ORDER_IP_DESC : ORDER_IP_ASCE;
		}
	}
	//提取数据
	rapidjson::Document::AllocatorType& allocator = dJsonDoc.GetAllocator(); //获取分配器
	rapidjson::Value lRows(rapidjson::kArrayType);//生成一个Array类型的元素，用来存放Object
	bool bUnPageUnOrder = (orderrule == ORDER_UNKNOW && (pageSetQuery.PageSize == 0 || pageSetQuery.PageSize >= m_cSessions.size())) ? true : false;
	auto aIDs = SplitIDs(pageSetQuery.IDs);//单独查找的ID
	std::vector<CiSCSISession_ptr> listSessions;
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		if (aIDs.empty()) {
			//遍历全部
			listSessions.reserve(m_cSessions.size());
		}
		else {
			listSessions.reserve(aIDs.size());
		}
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			bool bFind(false);
			if (aIDs.empty()) {
				if (!(*iter)->IsNewIP() && (pageSetQuery.ClassID == -1 || pageSetQuery.ClassID == (*iter)->GetTypeID())
					&& (pageSetQuery.SearchKey.empty() || string_contains((*iter)->GetHostName(), pageSetQuery.SearchKey)
						|| string_contains((*iter)->GetIpAddress(), pageSetQuery.SearchKey))) {
					bFind = true;
				}
			}
			else if (aIDs.find((*iter)->GetID()) != aIDs.end()) {
				bFind = true;
			}
			if (bFind) {
				if (bUnPageUnOrder) {
					(*iter)->GetComputerStatus(lRows, allocator);
				}
				else {
					//放到后面，分页排序
					listSessions.insert(listSessions.end(), *iter);
				}
			}
		}
	}
	if (!bUnPageUnOrder) {
		if (orderrule != ORDER_UNKNOW) {
			//需要排序
			std::sort(listSessions.begin(), listSessions.end(), [orderrule](const CiSCSISession_ptr& s1, const CiSCSISession_ptr& s2)->bool {
				switch (orderrule)
				{
				case ORDER_NAME_DESC:
					return wstring2icmp(s1->GetHostName(), s2->GetHostName()) > 0;
					break;
				case ORDER_IP_ASCE:
					return wstring2icmp(s1->GetIpAddress(), s2->GetIpAddress()) < 0;
					break;
				case ORDER_IP_DESC:
					return wstring2icmp(s1->GetIpAddress(), s2->GetIpAddress()) > 0;
					break;
				default:
				case ORDER_NAME_ASCE:
					return wstring2icmp(s1->GetHostName(), s2->GetHostName()) < 0;
					break;
				}
				});
		}
		//分页输出
		if (pageSetQuery.PageSize > 0) {
			if (pageSetQuery.PageNum < 1) {
				pageSetQuery.PageNum = 1;
			}
			else {
				int nPageCount = _countof_up(listSessions.size(), pageSetQuery.PageSize);
				if (pageSetQuery.PageNum > nPageCount) {
					pageSetQuery.PageNum = nPageCount;
				}
			}

			for (auto i = pageSetQuery.PageSize * (pageSetQuery.PageNum - 1);
				i < pageSetQuery.PageSize * pageSetQuery.PageNum && i < listSessions.size(); i++) {
				listSessions[i]->GetComputerStatus(lRows, allocator);
			}
		}
		else {
			//页面大小未定义，全部输出
			for (auto i = 0; i < listSessions.size(); i++) {
				listSessions[i]->GetComputerStatus(lRows, allocator);
			}
		}
		dJsonDoc.AddMember(WEBSOCKET_JSON_COMMAND_TOTAL, listSessions.size(), allocator);
	}
	else {
		dJsonDoc.AddMember(WEBSOCKET_JSON_COMMAND_TOTAL, lRows.Size(), allocator);
	}
	dJsonDoc.AddMember(WEBSOCKET_JSON_COMMAND_ROWS, lRows, allocator);
	return true;
}

bool CiSCSIConnectManager::GetComputerStatus(boost::json::object& dJsonDoc, PPAGE_SET_QUERY pPageSetQuery)
{
	PAGE_SET_QUERY defaultSet;
	PAGE_SET_QUERY& pageSetQuery = pPageSetQuery ? (*pPageSetQuery) : defaultSet;
	enum ORDERRULE { ORDER_UNKNOW, ORDER_NAME_ASCE, ORDER_NAME_DESC, ORDER_IP_ASCE, ORDER_IP_DESC };
	ORDERRULE orderrule(ORDER_UNKNOW);
	if (!pageSetQuery.OrderBy.empty()) {
		if (wstring2icmp(CListDataUser::GetIDName(LISTDATAUSER_NAME), pageSetQuery.OrderBy) == 0) {
			orderrule = pageSetQuery.Desc ? ORDER_NAME_DESC : ORDER_NAME_ASCE;
		}
		else if (wstring2icmp(CListDataUser::GetIDName(LISTDATAUSER_IP), pageSetQuery.OrderBy) == 0) {
			orderrule = pageSetQuery.Desc ? ORDER_IP_DESC : ORDER_IP_ASCE;
		}
	}
	//提取数据
	boost::json::array lRows;//生成一个Array类型的元素，用来存放Object
	bool bUnPageUnOrder = (orderrule == ORDER_UNKNOW && (pageSetQuery.PageSize == 0 || pageSetQuery.PageSize >= m_cSessions.size())) ? true : false;
	auto aIDs = SplitIDs(pageSetQuery.IDs);//单独查找的ID
	std::vector<CiSCSISession_ptr> listSessions;
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		if (aIDs.empty()) {
			//遍历全部
			listSessions.reserve(m_cSessions.size());
		}
		else {
			listSessions.reserve(aIDs.size());
		}
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			bool bFind(false);
			if (aIDs.empty()) {
				if (!(*iter)->IsNewIP() && (pageSetQuery.ClassID == -1 || pageSetQuery.ClassID == (*iter)->GetTypeID())
					&& (pageSetQuery.SearchKey.empty() || string_contains((*iter)->GetHostName(), pageSetQuery.SearchKey)
						|| string_contains((*iter)->GetIpAddress(), pageSetQuery.SearchKey))) {
					bFind = true;
				}
			}
			else if (aIDs.find((*iter)->GetID()) != aIDs.end()) {
				bFind = true;
			}
			if (bFind) {
				if (bUnPageUnOrder) {
					(*iter)->GetComputerStatus(lRows);
				}
				else {
					//放到后面，分页排序
					listSessions.insert(listSessions.end(), *iter);
				}
			}
		}
	}
	if (!bUnPageUnOrder) {
		if (orderrule != ORDER_UNKNOW) {
			//需要排序
			std::sort(listSessions.begin(), listSessions.end(), [orderrule](const CiSCSISession_ptr& s1, const CiSCSISession_ptr& s2)->bool {
				switch (orderrule)
				{
				case ORDER_NAME_DESC:
					return wstring2icmp(s1->GetHostName(), s2->GetHostName()) > 0;
					break;
				case ORDER_IP_ASCE:
					return wstring2icmp(s1->GetIpAddress(), s2->GetIpAddress()) < 0;
					break;
				case ORDER_IP_DESC:
					return wstring2icmp(s1->GetIpAddress(), s2->GetIpAddress()) > 0;
					break;
				default:
				case ORDER_NAME_ASCE:
					return wstring2icmp(s1->GetHostName(), s2->GetHostName()) < 0;
					break;
				}
				});
		}
		//分页输出
		if (pageSetQuery.PageSize > 0) {
			if (pageSetQuery.PageNum < 1) {
				pageSetQuery.PageNum = 1;
			}
			else {
				int nPageCount = _countof_up(listSessions.size(), pageSetQuery.PageSize);
				if (pageSetQuery.PageNum > nPageCount) {
					pageSetQuery.PageNum = nPageCount;
				}
			}

			for (auto i = pageSetQuery.PageSize * (pageSetQuery.PageNum - 1);
				i < pageSetQuery.PageSize * pageSetQuery.PageNum && i < listSessions.size(); i++) {
				listSessions[i]->GetComputerStatus(lRows);
			}
		}
		else {
			//页面大小未定义，全部输出
			for (auto i = 0; i < listSessions.size(); i++) {
				listSessions[i]->GetComputerStatus(lRows);
			}
		}
		//dJsonDoc[WEBSOCKET_JSON_COMMAND_TOTAL] = listSessions.size();
	}
	//else {
	//	dJsonDoc[WEBSOCKET_JSON_COMMAND_TOTAL] = lRows.size();
	//}
	SetJsonRows(dJsonDoc, lRows, !bUnPageUnOrder ? listSessions.size() : lRows.size());
	//dJsonDoc[WEBSOCKET_JSON_COMMAND_ROWS] = lRows;
	return true;
}

//统计显示大屏数据,在线统计数据
BOOL CiSCSIConnectManager::GetBigScreenAll(boost::json::object& dDocRsp)
{
	boost::mutex::scoped_lock lock(m_LockOnline);
	JsonArrayToIDTotal(m_OnlineDay, dDocRsp, "onlineuserday");
	JsonArrayToIDTotal(m_OnlineDay1, dDocRsp, "onlineuserday1");
	JsonArrayToIDTotal(m_OnlineWeek, dDocRsp, "onlineuserweek", 1);
	JsonArrayToIDTotal(m_OnlineWeek1, dDocRsp, "onlineuserweek1", 1);
	return TRUE;
}

#ifdef PERSONALDISK_HISTORY
bool CiSCSIConnectManager::GetPersonalNeedSaveHistory(CiSCSISession_ptr& pPersonal, CPersonalDisk_Ptr& pPersonalDisk)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->IsNeedSaveHistory()) {
			auto pDisk = (*iter)->GetDiskForHistory();
			if (pDisk) {
				pPersonal = (*iter);
				pPersonalDisk = pDisk;
				return true;
			}
		}
	}
	return false;
}
#endif

//调用回调函数
BOOL CiSCSIConnectManager::OnlineUser(CiSCSISession_ptr pSession, BOOL bOnline/*, BOOL bIscsi*/)
{
	boost::mutex::scoped_lock lock(m_LockOnline);
	if (bOnline) {
		++m_dwOnlineCount;
	}
	else {
		--m_dwOnlineCount;
	}

	if (IsOEMUnencryptedSet()) {
		//set文件不加密时才保存.保存会导致客户机秒卡.
		pSession->SaveToOption(FALSE, TRUE);//保存最后登陆时间和离线时间
	}

	//线程个数保持在 10 - 200 个
	if (m_pServer.m_ThreadManager->worksize() < (std::min)((int)m_dwOnlineCount + DEFAULT_EXTENDTHREADCOUNT, MAX_EXTENDTHREADCOUNT))
		m_pServer.m_ThreadManager->StartIOThread();

	OnOnlineUser_(pSession);
	return TRUE;
}

void CiSCSIConnectManager::OnOnlineUser_(CiSCSISession* pSession)
{
	if (!m_funOnOnlineUser.empty() && pSession) {
		auto pStatus = boost::make_shared<USERSTATUS>();
		if (pStatus) {
			pSession->SetUserStatus(pStatus.get());
			m_funOnOnlineUser(pStatus.get());
		}
	}
}

//设置IP分配范围和自身IP
void CiSCSIConnectManager::SetDHCPRange(const dhcpinfo_& dhcpinfo)
{
	m_ipDHCPStart = dhcpinfo.startip;
	m_ipDHCPStop = dhcpinfo.stopip;
	m_ipDHCPMask = dhcpinfo.mask;
	RtlZeroMemory(m_ipDHCPSelf, sizeof(m_ipDHCPSelf));
	int i = 0;
	//添加自己的服务器IP
	for (; i < dhcpinfo.nAdapterCount; ++i) {
		m_ipDHCPSelf[i] = dhcpinfo.selfip[i];
	}
	//添加其他的特殊IP
	if (dhcpinfo.gateway != INADDR_ANY && i < MAX_ADAPTER_COUNT) {
		m_ipDHCPSelf[i] = dhcpinfo.gateway;
		++i;
	}
	if (dhcpinfo.dns1 != INADDR_ANY && i < MAX_ADAPTER_COUNT) {
		m_ipDHCPSelf[i] = dhcpinfo.dns1;
		++i;
	}
	if (dhcpinfo.dns2 != INADDR_ANY && i < MAX_ADAPTER_COUNT) {
		m_ipDHCPSelf[i] = dhcpinfo.dns2;
		++i;
	}
}

//DHCP的回调函数,得到相关客户的信息缓存
BOOL CiSCSIConnectManager::FindAddSessionDHCP(PGETIPFUNC_PARA para)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	if (!para->sMac.is_valid()) return FALSE;

	// #ifdef NAME_AS_ID_SHAREIP
	//
	// 	auto pSessionPtr = FindByName_(s2ws_s(para->sHostname));
	// 	if (pSessionPtr) {//找到名字
	// 		if (para->sMac != pSessionPtr->GetMac()) {//有可能,自动加的MAC不正确,这里矫正下
	// 			auto sMac = boost::make_shared<CMac>(pSessionPtr->GetMac());//保存旧的mac
	// 			pSessionPtr->SetMac(para->sMac);
	// 			if (pSessionPtr->SaveToOption(FALSE, TRUE)) {
	// 				UpdateSessionsIndex_(pSessionPtr->GetID(), CHANGE_MAC);
	// 				OnChangeClientState(pSessionPtr, DHCPCLIENTSTATE_EDIT, sMac);
	// 				OnEditUser_(pSessionPtr);
	// 			}
	// 		}
	// 	}
	//
	// #else

	auto pSessionPtr = FindByMac_(para->sMac);
	if (pSessionPtr) {//找到mac
		para->nIP = pSessionPtr->GetIpAddressU();
		para->sHostname = pSessionPtr->GetHostNameS();
	}
	//因为DHCP数据包的误发问题,DHCP的数据包只可以修改MAC为空的计算机
	if (!pSessionPtr && para->nIP != INADDR_ANY && para->nIP != INADDR_NONE) {
		//继续查找ip
		pSessionPtr = FindByIP_(para->nIP);
		if (pSessionPtr && !pSessionPtr->GetMac().is_valid()) {
			//MAC原值为空 //MAC新值有数据
			para->sHostname = pSessionPtr->GetHostNameS();
			//有可能,自动加的MAC不正确,这里矫正下
			auto sMac = pSessionPtr->GetMacPtr();//保存旧的mac
			pSessionPtr->SetMac(para->sMac);
			boost::json::object oRow;
			if (pSessionPtr->SaveToOption(FALSE, TRUE, &oRow)) {
				AfterEditUser_(pSessionPtr, CHANGE_MAC, sMac, &oRow);
			}
		}
		else {
			pSessionPtr.reset();//不符合条件,必须mac为空才可以修改
		}
	}
	if (!pSessionPtr && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {//自动添加
		ULONG uNewIP(para->nIP);
		if (GetNewIP_(uNewIP)) {
			//使用参考值,或者得到新IP
			para->nIP = uNewIP;
			std::wstring newhostname = s2ws_s(para->sHostname);
			if (newhostname.empty()) {//分配一个名字
				newhostname = GetNewHostname_();
				para->sHostname = ws2s_s(newhostname);
			}
			pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);
			if (pSessionPtr) {
				pSessionPtr->AddFromDHCP(GetComputerNewID_(), newhostname, para->sMac, uNewIP, TRUE);
				if (IsOEMXima()) {
					if (!AddSession_(pSessionPtr)) {//添加失败,复位
						pSessionPtr.reset();
					}
					else {
						//TFTP直接添加保存
						boost::json::object oRow;
						if (pSessionPtr->SaveToOption(TRUE, TRUE, &oRow)) {
							AfterAddUser_(pSessionPtr, &oRow);
						}
					}
				}
				else {
					if (!AddNewSession_(pSessionPtr)) {//添加失败,复位
						pSessionPtr.reset();
					}
				}
			}
		}
	}

	// #endif

	if (pSessionPtr) {
		pSessionPtr->SetDHCPpara(para);
		return pSessionPtr->IsCanBoot();//返回是否能够启动
	}
	return FALSE;
}

//当客户机信息变化,需要删除DHCP缓存里的信息是调用
BOOL CiSCSIConnectManager::OnDelDHCPCLient_(const CiSCSISession_ptr& pSession)
{
	if (!m_funDelDhcpClient.empty()) {
		auto dhcpinfo = boost::make_shared<DHCPCLIENTINFO>();
		if (dhcpinfo) {
			dhcpinfo->sMac = pSession->GetMac();
			dhcpinfo->nIP = pSession->GetIpAddressU();
			dhcpinfo->sHostname = pSession->GetHostNameS();
			m_pServer.io_recycle_post(boost::bind(m_funDelDhcpClient, dhcpinfo));//避免双重加锁
		}
		return TRUE;
	}
	return FALSE;
}

//从后往前分配IP，避免多个新IP的客户端连接冲突
BOOL CiSCSIConnectManager::GetNewIP_(ULONG& uIP)
{
	SESSION_INDEX_IP& index = m_cSessions.get<SESSION_IP>();
	if (uIP != INADDR_ANY && uIP != INADDR_NONE) {
		//先检查参考IP是否符合要求
		if (uIP <= m_ipDHCPStop && uIP >= m_ipDHCPStart
			&& !CheckSelfIP(uIP) && index.find(uIP) == index.end())
			return TRUE;
	}
	for (uIP = m_ipDHCPStop; uIP >= m_ipDHCPStart; --uIP) {
		if (!CheckSelfIP(uIP) && index.find(uIP) == index.end())
			return TRUE;
	}
	return FALSE;
}

// int CiSCSIConnectManager::GetNewIP_(int nCount, std::vector<ULONG>& uIPs)
// {
// 	SESSION_INDEX_IP& index = m_cSessions.get<SESSION_IP>();
// 	int i(0);
// 	if (nCount > 0){
// 		for (auto uIP = m_ipDHCPStart; uIP != m_ipDHCPStop; ++uIP)	{
// 			if (!CheckSelfIP(uIP) && index.find(uIP) == index.end()){
// 				uIPs.push_back(uIP);
// 				if (++i >= nCount)//达到了要求的数量
// 					break;
// 			}
// 		}
// 	}
// 	return i;
// }
//检查是否是自己服务器的IP
BOOL CiSCSIConnectManager::CheckSelfIP(ULONG uIP)
{
	if (uIP != INADDR_ANY && uIP != INADDR_NONE) {
		for (int i = 0; i < MAX_ADAPTER_COUNT; ++i) {
			if (uIP == m_ipDHCPSelf[i])
				return TRUE;
		}
	}
	return FALSE;
}

CiSCSISession_ptr CiSCSIConnectManager::FindByIP_(ULONG uIP, BOOL bDelNewIP)
{
	CiSCSISession_ptr pSessionPtr;
	SESSION_INDEX_IP& index = m_cSessions.get<SESSION_IP>();
	auto iter = index.find(uIP);
	if (iter != index.end()) {//找到
		if (bDelNewIP && (*iter)->IsNewIP()) {
			BeforeDeleteUser_(*iter);
			auto iter1 = m_cSessions.project<SESSION_ID>(iter);//转换为IP索引的iter
			m_cSessions.erase(iter1);
		}
		else {
			pSessionPtr = *iter;
		}
	}
	return pSessionPtr;
}

std::list<CiSCSISession_ptr> CiSCSIConnectManager::FindByIPs_(ULONG uIP, BOOL bDelNewIP /*= FALSE*/)
{
	std::list<CiSCSISession_ptr> lSessions;
	SESSION_INDEX_IP& indexIP = m_cSessions.get<SESSION_IP>();
	auto range = indexIP.equal_range(uIP);
	for (auto iter = range.first; iter != range.second; ++iter) {
		if (bDelNewIP && (*iter)->IsNewIP()) {
			BeforeDeleteUser_(*iter);
			auto iter1 = m_cSessions.project<SESSION_ID>(iter);//转换为IP索引的iter
			//删除后，是否影响iter还不清楚，需要调试。
			m_cSessions.erase(iter1);
		}
		else {
			lSessions.push_back(*iter);
		}
	}
	return lSessions;
}

CiSCSISession_ptr CiSCSIConnectManager::FindByMac_(const CMac& pMac, BOOL bDelNewIP)
{
	CiSCSISession_ptr pSessionPtr;
	SESSION_INDEX_MAC& index = m_cSessions.get<SESSION_MAC>();
	auto iter = index.find(pMac);
	if (iter != index.end()) {//找到
		if (bDelNewIP && (*iter)->IsNewIP()) {
			BeforeDeleteUser_(*iter);
			auto iter1 = m_cSessions.project<SESSION_ID>(iter);//转换为IP索引的iter
			m_cSessions.erase(iter1);
		}
		else {
			pSessionPtr = *iter;
		}
	}
	return pSessionPtr;
}

CiSCSISession_ptr CiSCSIConnectManager::FindByName_(const std::wstring& pName, BOOL bDelNewIP)
{
	CiSCSISession_ptr pSessionPtr;
	SESSION_INDEX_NAME& index = m_cSessions.get<SESSION_NAME>();
	auto iter = index.find(pName);
	if (iter != index.end()) {//找到
		if (bDelNewIP && (*iter)->IsNewIP()) {
			BeforeDeleteUser_(*iter);
			auto iter1 = m_cSessions.project<SESSION_ID>(iter);//转换为IP索引的iter
			m_cSessions.erase(iter1);
		}
		else {
			pSessionPtr = *iter;
		}
	}
	return pSessionPtr;
}

BOOL CiSCSIConnectManager::CheckHostname_(const std::wstring& pName)
{
	SESSION_INDEX_NAME& index = m_cSessions.get<SESSION_NAME>();
	return (index.count(pName) > 0);
}

std::wstring CiSCSIConnectManager::GetNewHostname_()
{
	std::wstring sHostname;
	SESSION_INDEX_NAME& index = m_cSessions.get<SESSION_NAME>();
	int i(-1);
	do {
		sHostname = m_pServer.m_pOption.GetNewComputerName(i);
	} while (index.count(sHostname) > 0);//如果文件存在就继续生成新的名字
	return sHostname;
}

int CiSCSIConnectManager::GetNewIPHostname(int nCount, std::vector<ULONG>& uIPs, std::vector<std::wstring>& sNames)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	SESSION_INDEX_IP& indexIP = m_cSessions.get<SESSION_IP>();
	std::wstring sHostname;
	SESSION_INDEX_NAME& indexName = m_cSessions.get<SESSION_NAME>();
	int i(0);
	if (nCount > 0) {
		int nComputerNum = (m_ipDHCPStart & (~0xFFFFFF00)) - 1;//m_ipDHCPMask, 编号只看客户机IP的最后三位
		for (auto uIP = m_ipDHCPStart; uIP != m_ipDHCPStop; ++uIP) {
			if (!CheckSelfIP(uIP)) {
				auto iterIP = indexIP.find(uIP);
				if (iterIP == indexIP.end() || (*iterIP)->IsNewIP()) {
					uIPs.push_back(uIP);
					auto sHostnameBase = m_pServer.m_pOption.GetNewComputerName(nComputerNum);
					int j(0);
					do {
						if (j > 0) {
							sHostname = wstring_format(_T("%s-%d"), sHostnameBase.c_str(), j);
						}
						else {
							sHostname = sHostnameBase;
						}
						auto iterName = indexName.find(sHostname);
						if (iterName == indexName.end() || (*iterName)->IsNewIP()) {
							break;
						}
					} while (++j > 0);
					string_remove(sHostname, GetSuperuserSuffix());
					sNames.push_back(sHostname);
					if (++i >= nCount)//达到了要求的数量
						break;
				}
				else {
					++nComputerNum;
				}
			}
			else {
				++nComputerNum;
			}
		}
	}
	return i;
}

BOOL CiSCSIConnectManager::TftpCallbackFun(PTFTPCALLBACK pPara)
{
	if (pPara->uIP == 0)
		return FALSE;

	BOOL result(FALSE), bNewSession(FALSE);
	auto pSessionPtr = FindAddSessionTftp(pPara, &bNewSession);
	if (pSessionPtr.get()) {
		if (pPara->bStartEnd == TFTPCALLBACK_START) { //开始tftp是调用
			if (pPara->bSendRecv == TFTPCALLBACK_RECV
				&& !pSessionPtr->IsTftpOnline()) {//不允许重复请求上传文件，避免超时等待
				if (bNewSession)//新添加的用户
					EnableUploadUser(pSessionPtr->GetID(), TRUE);//新用户允许上传
				result = pSessionPtr->GetEnableUpload();//允许上传母盘
				if (result) {
					pSessionPtr->OnTftpOnline(TRUE);//标记为上传在线
					WRITE_ISCSILOG(_T("User start upload image, Remote IP:") << boost::asio::ip::address_v4(pPara->uIP).to_string().c_str());
				}
			}
			else if (pPara->bSendRecv == TFTPCALLBACK_SEND) {//发送启动文件
				pSessionPtr->OnClientShutdown(true, false);//标记为开始启动,开始监控
				result = TRUE;
			}
		}
		else if (pPara->bStartEnd == TFTPCALLBACK_END) { //结束时调用
			if (pPara->bSendRecv == TFTPCALLBACK_RECV
				&& pSessionPtr->IsTftpOnline()) {//接收文件,并在线
				if (pPara->bFinish && !pPara->sFileName.empty()) {//上传成功
					auto pDiskInfo = boost::make_shared<VIRDISKINFO>();
					boost::filesystem::path pFilepath(pPara->sFileName); int nDiskType(0);
					if (pDiskInfo && IsFile(pFilepath) && ISHAREDISK_DiskFile_Type(pFilepath.wstring().c_str(), &nDiskType)) {
						RtlZeroMemory(pDiskInfo.get(), sizeof(VIRDISKINFO));
						pDiskInfo->dwLength = sizeof(VIRDISKINFO);
						pDiskInfo->dwID = 0;
						pDiskInfo->bFormatType = nDiskType;
						pDiskInfo->bSystem = TRUE;
						pDiskInfo->cClientLetter = 0;//auto
						pDiskInfo->dwCapacity = 0;
						pDiskInfo->dwLastWriteTime = LastWriteTime(pPara->sFileName); //自动修改为文件修改日期
						wstringcopy(pDiskInfo->sName, _countof(pDiskInfo->sName), pFilepath.stem().wstring());
						wstringcopy(pDiskInfo->sDiskPath, _countof(pDiskInfo->sDiskPath), pPara->sFileName);
						if (m_pServer.m_VirDiskManager.AddLun(pDiskInfo.get()) == ERRORSTATUS_SUCCESS) {
							//上传完毕,关闭之
							if (AddSystemDiskToUser(pDiskInfo->dwID, pSessionPtr.get())
								&& EnableUploadUser(pSessionPtr->GetID(), FALSE)) {
								WRITE_ISCSILOG(_T("User finish upload image, Remote IP:") << boost::asio::ip::address_v4(pPara->uIP).to_string().c_str());
								result = TRUE;
							}
						}
					}
				}
				pSessionPtr->OnTftpOnline(FALSE);
			}
		}
	}
	return result;
}

BOOL CiSCSIConnectManager::AddSystemDiskToUser(DWORD dwDiskId, CiSCSISession* pSessionPtr)
{
	std::vector<BYTE> pUserInfoBuffer;
	pSessionPtr->SetUserInfo(pUserInfoBuffer);
	auto pUserInfo = (PUSERINFO)pUserInfoBuffer.data();
	auto dwIDs(SplitIDs(GETTEXT_FROMOFFSET(pUserInfo, sOffsetSystemIDs)));
	dwIDs.insert(dwDiskId);
	pUserInfo->sOffsetSystemIDs = pUserInfoBuffer.size();
	auto sTemp = JoinIDs(dwIDs);
	pUserInfoBuffer.insert(pUserInfoBuffer.end(), (const PBYTE)sTemp.c_str(), (const PBYTE)(sTemp.c_str() + sTemp.size() + 1));
	pUserInfo = (PUSERINFO)pUserInfoBuffer.data();//更新指针
	pUserInfo->dwLength = pUserInfoBuffer.size();//矫正长度
	return (EditUser(pUserInfo) == ERRORSTATUS_SUCCESS);
}

BOOL CiSCSIConnectManager::EnableUploadUser(DWORD nID, BOOL bEnable)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	auto iter = m_cSessions.find(nID);
	if (iter != m_cSessions.end()) {
		return  (*iter)->EnableUploadUser(bEnable);
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::SwitchUploadUser_(DWORD nID)
{
	auto iter = m_cSessions.find(nID);
	if (iter != m_cSessions.end()) {
		return (*iter)->SwitchUploadUser();
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::SwitchUploadUsers(const std::set<DWORD>& nIDs)
{
	BOOL ret(FALSE);
	if (nIDs.size() == 1) {
		ret = SwitchUploadUser(*nIDs.begin());
	}
	else {
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = nIDs.begin(); iter != nIDs.end(); iter++) {
			ret = SwitchUploadUser_(*iter);
			if (!ret)
				break;
		}
	}
	return ret;
}

//通过TFTP添加的新用户
CiSCSISession_ptr CiSCSIConnectManager::FindAddSessionTftp(PTFTPCALLBACK pPara, PBOOL pNewSession)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	CiSCSISession_ptr pSessionPtr = FindByIP_(pPara->uIP);//查找IP
	if (pSessionPtr) {
		//是通过DHCP添加的新计算机,要求上传镜像
		if (pSessionPtr->IsNewIP() && pPara->bStartEnd == TFTPCALLBACK_START && pPara->bSendRecv == TFTPCALLBACK_RECV) {
			pSessionPtr->SetNewIP(FALSE);//设置为旧计算机
			auto iterIP = m_cSessions.get<SESSION_IP>().end();
			auto iterMAC = m_cSessions.get<SESSION_MAC>().end();
			auto iterName = m_cSessions.get<SESSION_NAME>().end();
			AfterAddNewUser_(pSessionPtr, iterIP, iterMAC, iterName, CHANGE_NEWIP, pNewSession);
		}
	}
	else {
		if (pPara->uIP != INADDR_NONE && pPara->uIP != INADDR_ANY && pPara->uIP != INADDR_LOOPBACK//请求的用户不存在
			&& m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {//自动添加新用户
			CMac sMac;
			std::wstring sHostname;
			//if (GetRemoteMac(pPara->uIP, sMac)) {
			//	GetRemoteHostName(pPara->uIP, sHostname);
			//}
			if (sHostname.empty()) //未获取到远程计算机名
				sHostname = GetNewHostname_();
			pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);
			if (pSessionPtr) {
				pSessionPtr->AddFromDHCP(GetComputerNewID_(), sHostname, sMac, pPara->uIP, FALSE);
				if (AddSession_(pSessionPtr)) {
					//TFTP直接添加保存
					boost::json::object oRow;
					if (pSessionPtr->SaveToOption(TRUE, TRUE, &oRow)) {
						AfterAddUser_(pSessionPtr, &oRow);
					}
					if (pNewSession)
						*pNewSession = TRUE;
				}
				else {
					pSessionPtr.reset();//添加失败关闭之
				}
			}
		}
	}
	return pSessionPtr;
}

CiSCSISession_ptr CiSCSIConnectManager::FindAddSessionMulticast(const UDPENDPOINT& sender_endpoint, bool bLoginCmd,
	const std::wstring* sPassword /* = NULL */, const std::wstring* sMacStr /* = NULL */, const std::wstring* sName /* = NULL */)
{
	CMac sMac(bLoginCmd && sMacStr ? (*sMacStr) : _T(""));
	CiSCSISession_ptr pSessionPtr;
	boost::mutex::scoped_lock lock(m_mLockSession);
	SESSION_INDEX_IP& indexIP = m_cSessions.get<SESSION_IP>();
	SESSION_INDEX_MAC& indexMac = m_cSessions.get<SESSION_MAC>();
	auto iterIP = indexIP.end();
	//空mac禁止查找，避免重复的修改同一个只有IP信息的计算机
	auto iterMAC = sMac.is_valid() ? indexMac.find(sMac) : indexMac.end();
	if (iterMAC != indexMac.end()) {
		pSessionPtr = (*iterMAC);
	}
	else {
		iterIP = indexIP.find(sender_endpoint.address().to_v4().to_uint());
		if (iterIP != indexIP.end()) {
			pSessionPtr = (*iterIP);
		}
	}
	bool bNewAddComputer(false);//是否是通过iscsi添加的新计算机
	auto sHostName = sName ? (*sName) : GetNewHostname_();
	if (bLoginCmd && !pSessionPtr && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {//添加新计算机
		pSessionPtr = boost::make_shared<CiSCSISession>(m_pServer);//没有符合的会话,自动添加新会话
		if (pSessionPtr) {
			pSessionPtr->AddFromDHCP(GetComputerNewID_(), sHostName, sMac, sender_endpoint.address().to_v4().to_uint(), FALSE);
			bNewAddComputer = true;
		}
	}
	if (pSessionPtr) {
		if (bLoginCmd) {
			//登陆请求
			if (m_pServer.CheckRegisterLogin(m_dwOnlineCount, pSessionPtr->GetSystemVersion())) {
				DWORD dwChangedBit(0);
				if (pSessionPtr->CheckNewIPPC(sender_endpoint.address().to_v4().to_uint(), sHostName, sMac, dwChangedBit)) {
					if (bNewAddComputer) {//新计算机
						if (AddSession_(pSessionPtr)) {//添加会话
							boost::json::object oRow;
							if (pSessionPtr->SaveToOption(TRUE, TRUE, &oRow)) {
								AfterAddUser_(pSessionPtr, &oRow);
							}
						}
						else {
							pSessionPtr->Close();
							pSessionPtr.reset();
						}
					}
					else {
						auto iterName = m_cSessions.get<SESSION_NAME>().end();
						AfterAddNewUser_(pSessionPtr, iterIP, iterMAC, iterName, dwChangedBit);
					}
				}
				else {
					pSessionPtr.reset();
				}
			}
			else {
				WRITE_ISCSILOG(_T("CheckRegisterLogin Error in Multicast, Remote IP:") << FormatEndpoint(sender_endpoint).c_str());
				pSessionPtr.reset();
			}
		}
		else {
			//一般的查询,不返回隐藏的新计算机
			if (pSessionPtr->IsNewIP()) {
				pSessionPtr.reset();
			}
		}
	}
	return pSessionPtr;
}

//合并还原点,添加还原点后，更改用户的系统盘为新的系统盘
BOOL CiSCSIConnectManager::ChangeSysDisk(DWORD dwOldSysDisk, DWORD dwNewSysDisk, const CiSCSISession_ptr& pSession)
{
	BOOL ret(FALSE);
	if (pSession) {
		ret = pSession->ChangeSysDisk(dwOldSysDisk, dwNewSysDisk, TRUE);
	}
	else {
		boost::mutex::scoped_lock lock(m_mLockSession);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			ret = (*iter)->ChangeSysDisk(dwOldSysDisk, dwNewSysDisk, FALSE);
		}
		m_pServer.m_pOption.Save();
	}
	return ret;
}

//tftp回调函数，得到新计算机的IP选择列表
membuffer_ptr CiSCSIConnectManager::TftpMenuCallback(ULONG uLocalIP, ULONG uRemoteIP, BOOL bHaveUserSystem, const std::wstring& sName)
{
	CiSCSISession_ptr pSessionPtr = FindByIP(uRemoteIP);//查找IP
	membuffer_ptr outmembuffer;
	if (pSessionPtr) {
		if (wstringicmp(sName, _T(BOOTMENU_FILENAME)) == 0) {
			if (pSessionPtr->GetBindDHCPIP() != INADDR_ANY)//要使用自定义的iscsi的IP
				uLocalIP = pSessionPtr->GetBindDHCPIP();
			auto menustring = m_pServer.m_VirDiskManager.MakeBootMenu(uLocalIP, bHaveUserSystem, pSessionPtr->GetSystemID(), pSessionPtr->GetHideSystemIDs());
			//WRITE_ISCSILOG(ERROR_PROFIX<<_T("TftpMenuCallback menu uLocalIP:")<<CiSCSISession::IP2String(uLocalIP)<<_T(", uRemoteIP:")<<CiSCSISession::IP2String(uRemoteIP)<<_T(", menustring:\r\n")<<menustring.c_str());
			outmembuffer = boost::make_shared< std::vector<BYTE> >(menustring.begin(), menustring.end());
		}
		else if (wstringicmp(sName, _T(NEWIP_FILENAME)) == 0) {
			std::wstring sMacPre(_T(""));
			if (pSessionPtr->IsNewIP() && pSessionPtr->GetMac().is_valid()) {
				sMacPre = _T("-") + pSessionPtr->GetMac().to_hexstring() + _T("-");
			}
			std::string menustring = "#!ipxe\r\n\
iseq ${platform} efi && goto label_is_efi || goto label_is_legacy\r\n\
:label_is_legacy\r\n\
cpair --foreground 7 1\r\n\
cpair --foreground 7 2\r\n\
cpair --foreground 0 3\r\n\
console --picture tftp://${next-server}/logo.png --keep\r\n\
:label_is_efi\r\n\
menu Please choose an IP for new computer\r\n";
			std::vector<ULONG> uIPs;
			std::vector<std::wstring> sNames;
			if (m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_AUTOADDCOMPUTER)) {
				GetNewIPHostname(19, uIPs, sNames);
			}

			int i(0);
			if (uIPs.empty()) {
				//分配的IP用完了，只显示自己
				menustring += "item label_start_boot ${hostname} ---- ${ip}\r\n";
			}
			else {
				for (i = 0; i < uIPs.size(); ++i) {
					menustring += string_format("item label%d %s ---- %s\r\n", i, ws2utf8(sNames[i]).c_str(), ip2string(uIPs[i]).c_str());
				}
			}
			menustring += "item label_input_name Edit the name and IP\r\n";
			//https://ipxe.org/cmd/choose
			//choose --default exit --timeout 3000 target && goto ${target}
			menustring += "choose --default label_start_boot";
			if (m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_SYSMENUTIME)) {
				menustring += string_format(" --timeout %d", m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_SYSMENUTIME) * 1000);
			}
			menustring += " target && goto ${target} || goto label_start_boot\r\n";
			for (i = 0; i < uIPs.size(); ++i) {
				menustring += string_format(":label%d\r\n", i);
				menustring += string_format("set hostname %s\r\n", ws2utf8(sNames[i] + sMacPre).c_str());
				menustring += string_format("set net0/ip %s\r\n", ip2string(uIPs[i]).c_str());
				menustring += "set net0/gateway 0.0.0.0\r\n";
				menustring += "goto label_change_nameip\r\n";
			}
			menustring += ":label_input_name\r\n\
echo -n Enter new computer name ( default \"${hostname}\" ): ${} && read hostname && goto abel_input_ip || goto label_error_name\r\n\
:label_error_name\r\n\
prompt --key 0x20 Press SPACE key for input name again otherwise use default: ${hostname} && oto label_input_name || goto label_input_ip\r\n\
:label_input_ip\r\n\
echo -n Enter new IP address ( default \"${ip}\" ): ${} && read net0/ip && goto abel_change_nameip || goto label_error_ip\r\n\
:label_error_ip\r\n\
prompt --key 0x20 Press SPACE key for input ip again otherwise use default: ${ip} && goto abel_input_ip || goto label_change_nameip\r\n\
:label_change_nameip\r\n\
sanhook ${root-path}\r\n\
reboot\r\n\
goto label_exit\r\n\
:label_start_boot\r\n\
chain tftp://${next-server}/menu.ipxe\r\n\
:label_exit\r\n\
exit 0\r\n";
			outmembuffer = boost::make_shared< std::vector<BYTE> >(menustring.begin(), menustring.end());
		}
	}
	return outmembuffer;
}
//当客户机信息变化,需要删除多选项启动菜单时调用
BOOL CiSCSIConnectManager::OnDelBootMenu(ULONG uIP)
{
	if (!m_funOnDelBootMenu.empty()) {
		m_pServer.io_recycle_post(boost::bind(m_funOnDelBootMenu, uIP));//避免双重加锁
		return TRUE;
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::OnInitDHCPClientState(CLIENTSTATEARRAY& sClientState)
{
	sClientState.clear();
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if (!(*iter)->IsNewIP() && (*iter)->IsCanBoot() && (*iter)->GetMac().is_valid())
			sClientState.insert(CLIENTSTATEARRAY::value_type((*iter)->GetMac(), ((*iter)->IsSuperUser() ? DHCPCLIENTSTATE_SUPER : ((*iter)->IsSCSIReaded() ? DHCPCLIENTSTATE_ONLINE : DHCPCLIENTSTATE_OFFLINE))));
	}
	return TRUE;
}
//客户机信息变化时通知dhcp变化,多服务器同步用
void CiSCSIConnectManager::OnChangeClientState(const CiSCSISession_ptr& pSession, int nAction, CMac_ptr sOldMac /*= CMac_ptr()*/)
{
	if (!m_funOnChangeClientState.empty())
		m_pServer.io_recycle_post(boost::bind(m_funOnChangeClientState, pSession->GetMacPtr(), nAction, sOldMac));//避免双重加锁
}

BOOL CiSCSIConnectManager::DelUserClass(DWORD nID, std::set<DWORD>* pDelChildIDs, boost::json::object* dDocRsp /*= NULL*/)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	std::set<DWORD> aDelChildIDs;
	auto ret = m_lUserClass.DelUserClass(nID, &aDelChildIDs, dDocRsp);
	if (ret) {
		bool bNeedSaveToFile(false);
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if (aDelChildIDs.count((*iter)->GetTypeID())
				|| nID == (*iter)->GetTypeID()) {
				(*iter)->SetUserType(0);
				boost::json::object oRow;
				bNeedSaveToFile = (*iter)->SaveToOption(FALSE, FALSE, &oRow);
				if (bNeedSaveToFile) {
					OnEditUser_(*iter, &oRow);
					ReloadTemplate(*iter, false, true);
				}
			}
		}
		if (pDelChildIDs) *pDelChildIDs = aDelChildIDs;
		if (bNeedSaveToFile) m_pServer.m_pOption.Save();
	}
	return ret;
}

void CiSCSIConnectManager::UpdateSessionsIndex_(SESSION_ITER_ID& iter, DWORD dwChangedBit)
{
	if ((dwChangedBit & CHANGE_IP)) {
		SESSION_INDEX_IP& index = m_cSessions.get<SESSION_IP>();
		auto iter1 = m_cSessions.project<SESSION_IP>(iter);//转换为IP索引的iter
		index.modify_key(iter1, boost::lambda::_1 = (*iter)->GetIpAddressU());
	}
	if ((dwChangedBit & CHANGE_NAME)) {
		SESSION_INDEX_NAME& index = m_cSessions.get<SESSION_NAME>();
		auto iter1 = m_cSessions.project<SESSION_NAME>(iter);//转换为NAME索引的iter
		index.modify_key(iter1, boost::lambda::_1 = (*iter)->GetHostName());
	}
	if ((dwChangedBit & CHANGE_MAC)) {
		SESSION_INDEX_MAC& index = m_cSessions.get<SESSION_MAC>();
		auto iter1 = m_cSessions.project<SESSION_MAC>(iter);//转换为MAC索引的iter
		index.modify_key(iter1, boost::lambda::_1 = (*iter)->GetMac());
	}
}

void CiSCSIConnectManager::OnEditUser_(const CiSCSISession_ptr& pSession, boost::json::object* oRow /*= NULL*/)
{
	if (!m_funOnEditUser.empty()) {
		std::vector<BYTE> pUserInfoBuffer;
		pSession->SetUserInfo(pUserInfoBuffer);
		m_funOnEditUser((PUSERINFO)pUserInfoBuffer.data());
	}
	if (oRow) {
		m_pServer.OnAddEditToSSE(*oRow, WEBSOCKET_JSON_COMMAND_EDITCOMPUTER_RESPONSE, \
			WEBSOCKET_JSON_COMMAND_EDITCOMPUTER);
	}
}

void CiSCSIConnectManager::OptionSub2UserClassInfo(const CTreeListDataItem_ptr& pItem, PUSERCLASSINFO userclassinfo)
{
	if (userclassinfo) {
		RtlZeroMemory(userclassinfo, sizeof(USERCLASSINFO));
		userclassinfo->dwLength = sizeof(USERCLASSINFO);
		userclassinfo->dwID = pItem->GetID();
		wstringcopy(userclassinfo->sName, _countof(userclassinfo->sName), pItem->GetOption(OPTIONISCSI_USERCLASS_NAME));
	}
}

void CiSCSIConnectManager::OptionSub2UserInfo(const CTreeListDataItem_ptr& pItem, std::vector<BYTE>& pUserInfoBuffer)
{
	auto pSession = boost::make_shared<CiSCSISession>(m_pServer);
	pSession->Load(pItem);
	pSession->SetUserInfo(pUserInfoBuffer);
}
//客户端命令, 刷新磁盘,关机
// bool CiSCSIConnectManager::OnClientCommand(ULONG uClientIP, USHORT uPort, CiSharediskClientCmd_ptr pClientCmd)
// {
// 	bool ret(false);
// 	CiSCSISession_ptr pSessionPtr = FindByIP(uClientIP);//查找IP
// 	if (pSessionPtr){
// 		if (pClientCmd->GetCommand() == ISHAREDISKCLIENTCMD_POWEROFF) {
// 			pSessionPtr->OnClientShutdown();//客户机关闭时调用
// 			ret = true;
// 		}
// 		else if (pClientCmd->GetCommand() == ISHAREDISKCLIENTCMD_REFRESHDISK) {
// 			int dwLunID(0);
// 			pClientCmd->GetPara(0, dwLunID);
// 			if (dwLunID > 0) {
// 				pSessionPtr->RestartWriteback(dwLunID);//清理回写
// 				ret = true;
// 			}
// 		}
// 	}
// 	return ret;
// }

// void CiSCSIConnectManager::OnClientHardware(ULONG uClientIP, CHardwareInfo_ptr pHardwareInfo, DHCP_CLIENT_INFO_ptr pClientInfo)
// {
// 	CiSCSISession_ptr pSessionPtr = FindByIP(uClientIP);//查找IP
// 	if (pSessionPtr && pHardwareInfo && pClientInfo) {
// 		pSessionPtr->GetHardwareInfoFromClient(pHardwareInfo, pClientInfo.get(), NULL);//更新硬件信息
// 	}
// }

BOOL CiSCSIConnectManager::MountDisk(DWORD nID, DWORD nIP)
{
	BOOL ret = AddUserForMountDisk(nID, nIP);
#if (defined(_WIN32) || defined(_WIN64))
	if (ret && INADDR_LOOPBACK == nIP) {
		//在服务器端直接登录，不要返回界面了。
		StartiSCSIService();
		ret = LoginAndOpenDisk(nIP, FALSE);
	}
#endif
	return ret;
}

BOOL CiSCSIConnectManager::UnmountDisk(DWORD nID, DWORD nIP, BOOL bSave)
{
	BOOL ret(TRUE);
	auto pUser = FindByIP(nIP);
	if (pUser.get()) {
		auto funCloseConnect = [pUser](LPCTSTR pIP)->void {
			pUser->LockWriteback();
			pUser->Close(true);
			};
		if (INADDR_LOOPBACK == nIP && nID) {
#if (defined(_WIN32) || defined(_WIN64))
			//只卸载本地连接
			ret = UnloginAndUnmountDisk(funCloseConnect, false, ip2wstring(nIP).c_str(), false);
#endif
		}
		else if (bSave == ISHAREDISK_UNMOUNT_DISK_CLOSECONNECT_ONLY) {
			funCloseConnect(NULL);
		}
		if (ret && !bSave) {
			auto nUserID = pUser->GetID();
			pUser->ResetUser();

			//编辑用户禁止再次连接
			//if (pUser->GetBootMode() != ISHAREDISK_BOOTMODE_DISABLE) {
			//	pUser->SetBootMode(ISHAREDISK_BOOTMODE_DISABLE);
			//	pUser->UnlockWriteback();
			//	if (pUser->SaveToOption(FALSE, TRUE)) {
			//		OnDelDHCPCLient_(pUser);//更新dhcp缓存
			//		AfterEditUser_(pUser, CHANGE_DHCP);
			//		ret = TRUE;
			//	}
			//}

			pUser.reset();
			ret = TRUE;
			m_pServer.io_recycle_post([this, nUserID]() {
				//延时删除，避免用户再次连接
				Sleep_Second(3);
				DelUser(nUserID);
				});
			//ret = (DelUser(nUserID) == ERRORSTATUS_SUCCESS);
		}
	}
	return ret;
}

ISCSI_TSIH CiSCSIConnectManager::GetNextTSIH()
{
	boost::mutex::scoped_lock lock(m_LockTSIH);
	return ++m_dwNextTSIH;
}

// BOOL CiSCSIConnectManager::MakeISOBootFile(LPCTSTR pFilePath, const dhcpinfo_& dhcpinfo)
// {
// 	//替换原iso文件里的脚本
// 	auto srcISO = boost::filesystem::absolute(_T("ipxe.iso"), m_pServer.m_pOption.GetAppPath());
// 	if (wstringicmp(srcISO.wstring().c_str(), pFilePath) != 0 && IsFile(srcISO)){
// 		auto pUsbBootISO = boost::make_shared<CUsbBootISO>();
// 		pUsbBootISO->SetDefault(ip2string(dhcpinfo.mask),
// 			ip2string(dhcpinfo.gateway),
// 			ip2string(dhcpinfo.dns1), ip2string(dhcpinfo.dns2));
// 		if (dhcpinfo.nAdapterCount == 0)	{
// 			pUsbBootISO->AddServer("***********");//添加虚假服务器，避免出错
// 		}
// 		else {
// 			for (int i = 0; i < dhcpinfo.nAdapterCount; i++) {
// 				pUsbBootISO->AddServer(ip2string(dhcpinfo.selfip[i]));
// 			}
// 		}
// 		{
// 			boost::mutex::scoped_lock lock(m_mLockSession);
// 			for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter){
// 				if (!(*iter)->IsNewIP() && (*iter)->IsCanBoot() && (*iter)->GetMac().is_valid()){
// 					USBBOOTISO_COMPUTER sNewComputer;
// 					sNewComputer.m_sName = (*iter)->GetHostNameS();
// 					sNewComputer.m_sIP = (*iter)->GetIpAddressS();
// 					sNewComputer.m_sMAC = (*iter)->GetMac().to_string_low();
// 					auto pIscsiPara = (*iter)->GetiScsiParameter();
// 					if (pIscsiPara->m_dwDHCPIP != INADDR_ANY && pIscsiPara->m_dwDHCPIP != INADDR_BROADCAST){
// 						//自定义server IP
// 						sNewComputer.m_siScsiIP = ip2string(pIscsiPara->m_dwDHCPIP);
// 					}
// 					if (pIscsiPara->m_dwDHCPMask != INADDR_ANY)
// 						sNewComputer.m_sMask = ip2string(pIscsiPara->m_dwDHCPMask);
// 					if (pIscsiPara->m_dwDHCPGateway != INADDR_ANY)
// 						sNewComputer.m_sGateway = ip2string(pIscsiPara->m_dwDHCPGateway);
// 					if (pIscsiPara->m_dwDHCPDns1 != INADDR_ANY)//自定义优先
// 						sNewComputer.m_sDns = ip2string(pIscsiPara->m_dwDHCPDns1);
// 					if (pIscsiPara->m_dwDHCPDns2 != INADDR_ANY)//自定义优先
// 						sNewComputer.m_sDns2 = ip2string(pIscsiPara->m_dwDHCPDns2);
// 					switch ((*iter)->GetBootMode()){
// 					case ISHAREDISK_BOOTMODE_ENABLE:
// 					{
// 						if ((*iter)->IsBootMenu())//多系统启动直接显示菜单
// 							sNewComputer.m_boottype = ISOBOOTTYPE_MENU;
// 						else
// 							sNewComputer.m_boottype = ISOBOOTTYPE_DISKLESS;
// 					}
// 					break;
// 					case ISHAREDISK_BOOTMODE_MUSTLOGIN:
// 						sNewComputer.m_boottype = ISOBOOTTYPE_LOGIN;
// 						break;
// 					case ISHAREDISK_BOOTMODE_LOCAL:
// 						sNewComputer.m_boottype = ISOBOOTTYPE_LOCAL;
// 						break;
// 					case ISHAREDISK_BOOTMODE_USERSELECT:
// 						sNewComputer.m_boottype = ISOBOOTTYPE_MENU;
// 						break;
// 					default:
// 						sNewComputer.m_boottype = ISOBOOTTYPE_EXIT;
// 					}
// 					pUsbBootISO->AddComputer(std::move(sNewComputer));
// 				}
// 			}
// 		}
// 		boost::filesystem::path sDstFile(pFilePath);
// 		return pUsbBootISO->Save(srcISO.string(), sDstFile.string());
// 	}
// 	return FALSE;
// }

DWORD CiSCSIConnectManager::GetComputerNewID()
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	return GetComputerNewID_();
}

DWORD CiSCSIConnectManager::GetComputerNewID_()
{
	DWORD nID(1);
	while (true) {
		if (m_cSessions.find(nID) == m_cSessions.end())
			break;//未使用的ID
		else
			nID++;
	}
	return nID;
}
//10分钟后清理未连接的客户端
bool CiSCSIConnectManager::OnTime()
{
	{
		boost::mutex::scoped_lock lock(m_mLockSession);
		if (m_dwNewCount > 0 || m_lMulticaseSenders && m_lMulticaseSenders->IsOpen()) {
			auto dwNow = GetTickCount();
			for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ) {
				if ((*iter)->IsNewTimeoutPC(dwNow)) {
					BeforeDeleteUser_(*iter);
					iter = m_cSessions.erase(iter);
				}
				else {
					if ((*iter)->IsMulticastTimeout(dwNow)) {
						//组播客户端超时下线
						(*iter)->SetMulticastStatus(MULTICASTSTATUS_OFFLINE);
					}
					++iter;
				}
			}
		}
	}
	{
		//统计在线信息
		boost::mutex::scoped_lock lock(m_LockOnline);
		auto dLocalDay = boost::gregorian::day_clock::local_day();
		if (m_tLastDayOfYear != dLocalDay.day_of_year()) {
			//一年中第几天,新的一天
			m_OnlineDay1.swap(m_OnlineDay);
			m_OnlineDay.fill(0);
			m_tLastDayOfYear = dLocalDay.day_of_year();
		}
		auto nHourIndex = boost::posix_time::to_tm(second_clock::local_time()).tm_hour;
		if (m_dwOnlineCount > m_OnlineDay.at(nHourIndex)) {
			m_OnlineDay.at(nHourIndex) = m_dwOnlineCount;
		}
		if (m_tLastWeekOfYear != dLocalDay.week_number()) {
			//一年中的第几个星期
			m_OnlineWeek1.swap(m_OnlineWeek);
			m_OnlineWeek.fill(0);
			m_tLastWeekOfYear = dLocalDay.week_number();
		}
		//星期几  the day of the week (0==Sunday, 1==Monday, etc)
		auto nWeekindex = dLocalDay.day_of_week();
		if (m_dwOnlineCount > m_OnlineWeek.at(nWeekindex)) {
			m_OnlineWeek.at(nWeekindex) = m_dwOnlineCount;
		}
	}
	return true;
}

BOOL CiSCSIConnectManager::StartMulticast(DWORD nDiskID, ULONG nBindIP /*= 0*/)
{
	if (!m_lMulticaseSenders) {
		m_lMulticaseSenders = boost::make_shared<CMulticastSender>(*this, m_pServer.get_io_service(), m_pServer.GetSeactorPool());
	}
	if (m_lMulticaseSenders && !m_lMulticaseSenders->IsOpen()) {
		//清除组播状态
		{
			boost::mutex::scoped_lock lock(m_mLockSession);
			for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
				(*iter)->SetMulticastStatus(MULTICASTSTATUS_OFFLINE);
				if ((*iter)->GetSystemIDset().count(nDiskID) && (*iter)->IsClientOnline()) {
					//通知客户机开始组播
					(*iter)->SendCommandToUser(ISHAREDISKCLIENTCMD_START_MULTICAST);
				}
			}
		}
		return m_lMulticaseSenders->StartMulticast(nDiskID, nBindIP);
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::MulticastDisk(DWORD nDiskID, DWORD dwSendSpeedLimit, DWORD dwFirstPartitionPercent, DWORD dwPacketSize, const std::string& sPara2)
{
	if (m_lMulticaseSenders && m_lMulticaseSenders->IsOpen()) {
		//等待写入的目标磁盘
		auto pDisk = m_pServer.m_VirDiskManager.GetLun(nDiskID);
		if (pDisk && pDisk->IsSystemDisk()) {
			m_lMulticaseSenders->SetLimit(dwSendSpeedLimit, dwPacketSize);
			return m_lMulticaseSenders->MulticastDisk(pDisk, dwFirstPartitionPercent, sPara2);
		}
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::CancelMulticastDisk(DWORD nDiskID)
{
	if (m_lMulticaseSenders) {
		//等待写入的目标磁盘
		return m_lMulticaseSenders->CancelMulticastDisk(nDiskID);
	}
	return FALSE;
}

//查询组播状态
BOOL CiSCSIConnectManager::QueryMulticast(PMULTICASTSTATUS pStatus)
{
	if (pStatus && pStatus->dwLength == sizeof(MULTICASTSTATUS)
		&& m_lMulticaseSenders) {
		m_lMulticaseSenders->GetStatus(pStatus);
		return TRUE;
	}
	return FALSE;
}

BOOL CiSCSIConnectManager::StopMulticast()
{
	if (m_lMulticaseSenders && m_lMulticaseSenders->IsOpen()
		&& m_lMulticaseSenders->Stop()) {
		//清除组播状态
			{
				boost::mutex::scoped_lock lock(m_mLockSession);
				for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
					if ((*iter)->IsClientOnline() && (*iter)->GetMulticastStatus() != MULTICASTSTATUS_OFFLINE) {
						//通知客户机开始组播
						(*iter)->SendCommandToUser(ISHAREDISKCLIENTCMD_STOP_MULTICAST);
					}
					(*iter)->SetMulticastStatus(MULTICASTSTATUS_OFFLINE);
				}
			}
			return TRUE;
	}
	return FALSE;
}

bool CiSCSIConnectManager::handle_multicast_message(CMulticastSender* pMulticastSender, const UDPENDPOINT& sender_endpoint, const CAdminMsg_Ptr& pReceiveMsg)
{
	bool bResult(false);
	CiSCSISession_ptr FindSession_ptr;
	switch (pReceiveMsg->GetComType()) {
	case iCOMMAND_MULTICAST_ADD:
		if (pReceiveMsg->GetComID1() <= LOGIN_VERSION) {
			//无会话，登录的第一个包
			auto pDataItem = boost::dynamic_pointer_cast<CTreeListDataItem>(boost::make_shared<CListDataCommon>());
			auto xml = pReceiveMsg->GetXml();
			if (pDataItem && xml) {
				pDataItem->SetData(*xml);
				std::wstring sPassword(pDataItem->GetItemData(LISTDATASTATUS_STRING1)),
					sMacStr(pDataItem->GetItemData(LISTDATASTATUS_STRING2)),
					sName(pDataItem->GetItemData(LISTDATASTATUS_STRING3));
				FindSession_ptr = FindAddSessionMulticast(sender_endpoint, true,
					&sPassword, &sMacStr, &sName);
				if (FindSession_ptr) {
					FindSession_ptr->SetMulticastStatus(MULTICASTSTATUS_ONLINE);
					bResult = pMulticastSender->SendCommand_(&sender_endpoint, iCOMMAND_LOGIN_RESPONSE, pReceiveMsg->GetComID(), TRUE, LOGINRESPONSE_VERSION);
					if (!FindSession_ptr->IsClientOnline()) {
						//登陆成功后，发送客户机信息给客户端，包括客户机名字
						pMulticastSender->OnChangeIP(sender_endpoint, FindSession_ptr, FALSE);
					}
				}
			}
		}
		break;
	case iCOMMAND_MULTICAST_PING_RESPONSE:
		FindSession_ptr = FindAddSessionMulticast(sender_endpoint, false);
		if (FindSession_ptr && FindSession_ptr->IsMulticastLogined() && pReceiveMsg->GetComID()) {
			//更新在线状态
			FindSession_ptr->UpdateMulticastTime(true);
			bResult = true;
		}
		break;
	case iCOMMAND_ALLDISK:
		FindSession_ptr = FindAddSessionMulticast(sender_endpoint, false);
		if (FindSession_ptr) {
			//更新在线状态
			FindSession_ptr->UpdateMulticastTime(true);
			auto xml = boost::make_shared<CMarkup>();
			if (xml) {
				bResult = m_pServer.GetOptionDiskList(FindSession_ptr.get(), pReceiveMsg->GetComID(), *xml);
			}
			if (bResult && xml)
				bResult = pMulticastSender->SendCommand_(&sender_endpoint, iCOMMAND_ALLDISK_RESPONSE, *xml, bResult);
			else
				bResult = pMulticastSender->SendCommand_(&sender_endpoint, iCOMMAND_ALLDISK_RESPONSE, FALSE);
		}
		break;
	case iCOMMAND_MULTICAST_FORMATDISK:
	case iCOMMAND_MULTICAST_DISK_RESPONSE:
	case iCOMMAND_MULTICAST_RESEND:
	case iCOMMAND_MULTICAST_IMAGE_END:
		if (pMulticastSender->GetImageDisk()
			&& pMulticastSender->GetImageDisk()->GetID() == pReceiveMsg->GetComID()
			&& pReceiveMsg->GetComID1()) {
			FindSession_ptr = FindAddSessionMulticast(sender_endpoint, false);
			if (FindSession_ptr) {
				//正在格式化
				FindSession_ptr->SetMulticastStatus(MultiCmdToStatus(pReceiveMsg->GetComType()));
				if (pReceiveMsg->GetComType() == iCOMMAND_MULTICAST_IMAGE_END) {
					bResult = pMulticastSender->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_IMAGE_END_RESPONSE, pReceiveMsg->GetComID(), TRUE);
				}
				else {
					bResult = true;
				}
			}
		}
		break;
	case iCOMMAND_MULTICAST_CANCELDISK_RESPONSE:
		//服务器上文件可能关闭，不能再检查
		if (pReceiveMsg->GetComID1()) {
			FindSession_ptr = FindAddSessionMulticast(sender_endpoint, false);
			if (FindSession_ptr) {
				//取消成功了
				FindSession_ptr->SetMulticastStatus(MULTICASTSTATUS_ONLINE);
				bResult = true;
			}
		}
		break;
	}
	return bResult;
}

int CiSCSIConnectManager::MultiCmdToStatus(int nClientToServerCmd)
{
	int nClientMulticastStatus(MULTICASTSTATUS_OFFLINE);
	switch (nClientToServerCmd)
	{
	case iCOMMAND_MULTICAST_FORMATDISK:
		nClientMulticastStatus = MULTICASTSTATUS_INITDISK_START;
		break;
	case iCOMMAND_MULTICAST_DISK_RESPONSE:
		nClientMulticastStatus = MULTICASTSTATUS_IMAGE_START;
		break;
	case iCOMMAND_MULTICAST_RESEND:
		nClientMulticastStatus = MULTICASTSTATUS_RESEND_START;
		break;
	case iCOMMAND_MULTICAST_IMAGE_END:
		nClientMulticastStatus = MULTICASTSTATUS_IMAGE_END;
		break;
	}
	return nClientMulticastStatus;
}

BOOL CiSCSIConnectManager::CheckMulticastStatus(DWORD nDiskID, int nCheckStatus, DWORD& nReadyCount, DWORD& dwNoReady)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	nReadyCount = 0;
	dwNoReady = 0;
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if (nDiskID == 0 || (*iter)->GetSystemIDset().count(nDiskID)) {
			if ((*iter)->GetMulticastStatus() != MULTICASTSTATUS_OFFLINE) {
				if (nCheckStatus != MULTICASTSTATUS_END && (*iter)->GetMulticastStatus() != nCheckStatus) {
					dwNoReady++;
				}
				else {
					nReadyCount++;
				}
			}
			else if ((*iter)->IsClientOnline()) {
				//通知客户机开始组播
				(*iter)->SendCommandToUser(ISHAREDISKCLIENTCMD_START_MULTICAST, TRUE);//重启组播
				dwNoReady++;
			}
		}
	}
	return (dwNoReady > 0) ? FALSE : (nReadyCount > 0);
}

//检查是否需要重启的IP
void CiSCSIConnectManager::CheckNeedHardRebootClients(DWORD nStartTime, DWORD nReadSize,/* DWORD nNextTime,*/ std::list< std::pair<ULONG, CMac> >& lNeedRebootIPs)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->CheckNeedHardReboot(nStartTime, nReadSize/*, nNextTime*/)) {
			lNeedRebootIPs.push_back(std::make_pair((*iter)->GetBindDHCPIP(), (*iter)->GetMac()));
		}
	}
}

BOOL CiSCSIConnectManager::OnClientShutdown(bool bBooted, bool bDisableMonitor)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		(*iter)->OnClientShutdown(bBooted, bDisableMonitor);
	}
	return TRUE;
}

void CiSCSIConnectManager::InitXimaMacList(std::map< WORD, CMac >& lXimaMacList)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	lXimaMacList.clear();
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->GetXimaMacID()) {
			lXimaMacList.insert(std::make_pair((*iter)->GetXimaMacID(), (*iter)->GetMac()));
		}
	}
}

bool CiSCSIConnectManager::UpdateXimaMac(WORD dwMacID, CMac pMac)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->GetXimaMacID() && (*iter)->GetXimaMacID() == dwMacID) {
			if ((*iter)->GetMac() != pMac) {
				if (pMac.is_valid()) {
					//检查是否存在//已经存在
					auto pOldSession = FindByMac_(pMac, TRUE);
					if (pOldSession) {
						//计算机更改了mac,或则计算机换了端口板,强制修改旧计算机
						OnDelDHCPCLient_(pOldSession);//更新dhcp缓存
						auto sMac = pOldSession->GetMacPtr();//保存旧的mac
						pOldSession->SetMac(CMac());//复位mac
						if (pOldSession->SaveToOption(FALSE, TRUE)) {
							AfterEditUser_(pOldSession, CHANGE_MAC, sMac);
						}
					}
				}
				OnDelDHCPCLient_(*iter);//更新dhcp缓存
				auto sMac = (*iter)->GetMacPtr();//保存旧的mac
				(*iter)->SetMac(pMac);//修改mac
				if ((*iter)->SaveToOption(FALSE, TRUE)) {
					AfterEditUser_((*iter), CHANGE_MAC, sMac);
					return true;
				}
			}
			else {
				//如果那边被删除，这里同步两边的数据
				m_pServer.io_recycle_post(boost::bind(&CXimaMacUpdater::UpdateXimaMac, m_pServer.m_pXimaMacUpdater, dwMacID, pMac));
				WRITE_ISCSILOG(_T("Update Xima ID:") << dwMacID << _T(", Mac:") << pMac.to_wstring());
			}
			break;
		}
	}
	return false;
}

//检查本地更新数量的限制
BOOL CiSCSIConnectManager::CheckLocalUpdateCountLimit(CiSCSISession* pSession)
{
	BOOL bCanUpdate(FALSE);
	if (pSession->IsLocalUpdating()) {
		bCanUpdate = TRUE;//正在更新，
	}
	else {
		boost::mutex::scoped_lock lock(m_LockOnline);
		auto dwLocalUpdateCountLimit = m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_UPDATE_COUNT);
		if (dwLocalUpdateCountLimit == 0 || m_dwLocalUpdateCount.size() < dwLocalUpdateCountLimit) {
			m_dwLocalUpdateCount.insert(pSession->GetID());
			pSession->SetLocalUpdating(TRUE);
			bCanUpdate = TRUE;//可以开始更新
			WRITE_ISCSILOG(_T("Check Local Update Count Limit:") << dwLocalUpdateCountLimit \
				<< _T(",  Local Update Count:") << m_dwLocalUpdateCount.size() << _T(", bCanUpdate:") << bCanUpdate
				<< _T(", Client IP: ") << pSession->GetIpAddress());
		}
	}
	return bCanUpdate;
}

void CiSCSIConnectManager::DelLocalUpateCount(CiSCSISession_ptr pSession)
{
	if (pSession) {
		boost::mutex::scoped_lock lock(m_LockOnline);
		if (m_dwLocalUpdateCount.count(pSession->GetID()) > 0) {
			m_dwLocalUpdateCount.erase(pSession->GetID());
			WRITE_ISCSILOG(_T("Delete Local Update Count:") << m_dwLocalUpdateCount.size() \
				<< _T(", Client IP: ") << pSession->GetIpAddress());
		}
	}
}

bool CiSCSIConnectManager::ReSendClientCommandToUser()
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((*iter)->IsClientOnline() && (*iter)->HaveNoSendCommand()) {
			m_pServer.io_recycle_post(boost::bind(&CiSCSISession::ReSendAllCommandToUser, *iter, false));
		}
	}
	return true;
}

//磁盘的保留模式发生变化时,通知客户机
void CiSCSIConnectManager::OnChangeLunKeepMode(std::set<DWORD> nID, BYTE bKeepMode, DWORD dwChangedBit)
{
	boost::mutex::scoped_lock lock(m_mLockSession);
	for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
		if ((dwChangedBit & CHANGE_UNRESTOREIMAGE) && (*iter)->IsIscsiOnline()) {
			auto nSystemID = (*iter)->GetSystemNoHideIDset();
			for (const auto& id : nID) {
				if (nSystemID.find(id) != nSystemID.end()) {
					(*iter)->OnChangeLunDisklessKeepMode(nID, bKeepMode);
					break;
				}
			}
		}
		if ((dwChangedBit & CHANGE_KEEPMODE) && (*iter)->IsClientOnline()) {
			auto nSystemID = (*iter)->GetSystemNoHideIDset();
			for (const auto& id : nID) {
				if (nSystemID.find(id) != nSystemID.end()) {
					SendCommandToUserOnEdit((*iter), ISHAREDISKCLIENTCMD_ONEDITUSER, FALSE);
					break;
				}
			}
		}
	}
}

BOOL CiSCSIConnectManager::CanAddSuperUser(const std::wstring& sSystemIDs) {
	if (!IsSuperUserCountUnlimit()) {
		auto arrayIDs = SplitIDs(sSystemIDs);
		for (auto iter = arrayIDs.begin(); iter != arrayIDs.end(); iter++) {
			if (m_lSuperUserDisk.find(*iter) != m_lSuperUserDisk.end()) {
				return FALSE;
			}
		}
	}
	return TRUE;
}

//更新超级用户的数量，用于检查
void CiSCSIConnectManager::UpdateSuperUserCount() {
	if (!IsSuperUserCountUnlimit()) {
		boost::mutex::scoped_lock lock(m_mLockSession);
		std::set<DWORD> lSuperUserDisk;
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if ((*iter)->IsSuperUser() == VHD_BOOT_MODE_SUPER) {
				auto arrayIDs = SplitIDs((*iter)->GetSystemID());
				if (!arrayIDs.empty()) lSuperUserDisk.insert(arrayIDs.begin(), arrayIDs.end());
			}
		}
		m_lSuperUserDisk.swap(lSuperUserDisk);
	}
	return;
}

std::wstring CiSCSIConnectManager::GetUserSourceIPList(const CiSCSISession_ptr& pSession, DWORD dwImageID, DWORD64 dwLastImageWriteTime, BOOL& bIncludeServer)
{
	std::wstring sSourceIPlist(_T(""));
	auto p2pmode = pSession->GetiScsiParameter()->m_nUpdateP2pMode;
	auto aSourceIPList = SplitStrings2Set(pSession->GetiScsiParameter()->m_sUpdateServerIPs);
	std::list<std::wstring> aOutIPList;
	boost::mutex::scoped_lock lock(m_mLockSession);
	auto funFilterSource = [this, &aSourceIPList, &pSession, &aOutIPList]() {
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if (aSourceIPList.count((*iter)->GetIpAddress()) > 0
				&& pSession->GetIpAddress().compare((*iter)->GetIpAddress())
				&& (*iter)->IsClientOnline()) {
				aOutIPList.push_back((*iter)->GetIpAddress());
			}
		}
		return JoinStrings(aOutIPList);
		};
	auto funFilerAllComputer = [this, &pSession, &aOutIPList]() {
		std::list<CiSCSISession_ptr> lSessionList;
		for (auto iter = m_cSessions.begin(); iter != m_cSessions.end(); ++iter) {
			if ((*iter)->IsClientOnline()
				&& pSession->GetIpAddress().compare((*iter)->GetIpAddress())) {
				lSessionList.push_back(*iter);
			}
		}
		//a<b会产生升序排序,若改为>,则变为降序
		lSessionList.sort([&pSession](const CiSCSISession_ptr& a, const CiSCSISession_ptr& b) {
			if (pSession->GetTypeID()) {
				//同类别的优先
				if (a->GetTypeID() == pSession->GetTypeID() && b->GetTypeID() != pSession->GetTypeID()) {
					return true;
				}
				else if (a->GetTypeID() != pSession->GetTypeID() && b->GetTypeID() == pSession->GetTypeID()) {
					return false;
				}
			}
			//IP距离短的优先
			auto disA = (std::max)(a->GetIpAddressU(), pSession->GetIpAddressU()) - \
				(std::min)(a->GetIpAddressU(), pSession->GetIpAddressU());
			auto disB = (std::max)(b->GetIpAddressU(), pSession->GetIpAddressU()) - \
				(std::min)(b->GetIpAddressU(), pSession->GetIpAddressU());
			return disA < disB;
			});
		for (auto iter = lSessionList.begin(); iter != lSessionList.end(); iter++) {
			aOutIPList.push_back((*iter)->GetIpAddress());
			if (aOutIPList.size() > 100) break; //不超过100个
		}
		return JoinStrings(aOutIPList);
		};
	switch (p2pmode) {
	case VHD_P2P_SERVER_ONLY:
	default:
	{
		bIncludeServer = TRUE;
		sSourceIPlist = _T("");
	}
	break;
	case VHD_P2P_SOURCE_ONLY:
	{
		bIncludeServer = FALSE;
		sSourceIPlist = funFilterSource();
	}
	break;
	case VHD_P2P_SERVER_SOURCE_AUTO:
	{
		bIncludeServer = TRUE;
		sSourceIPlist = funFilterSource();
	}
	break;
	case VHD_P2P_ALL_COMPUTER_AUTO:
	{
		bIncludeServer = FALSE;
		sSourceIPlist = funFilerAllComputer();
	}
	break;
	case VHD_P2P_SERVER_ALL_COMPUTER_AUTO:
	{
		bIncludeServer = TRUE;
		sSourceIPlist = funFilerAllComputer();
	}
	break;
	}
	return sSourceIPlist;
}

bool CiSCSIConnectManager::PowerOnClient(const std::set<CMac>& lMacs)
{
	if (!lMacs.empty()) {
		return CDHCPserver::PowerOnClient((m_pServer.m_DhcpServer && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_ENABLEDHCPTFTP))
			? m_pServer.m_DhcpServer->get_io_service() : get_io_service(), lMacs);
	}
	return false;
}

bool CiSCSIConnectManager::PowerOnClient(const CMac& sMac)
{
	return CDHCPserver::PowerOnClient((m_pServer.m_DhcpServer && m_pServer.m_pOption.GetOptionVal(ISHAREDISK_OPTION_ENABLEDHCPTFTP))
		? m_pServer.m_DhcpServer->get_io_service() : get_io_service(), sMac);
}