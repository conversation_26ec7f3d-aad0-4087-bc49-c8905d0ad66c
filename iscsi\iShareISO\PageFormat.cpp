﻿#include "stdafx.h"
#include <Dbt.h>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <boost/thread.hpp>
#include "../iShareDll/stringsystem.h"
#include "../iShareDll/filesystem.h"
#include "../iShareDll/regsystem.h"
#include "../iShareDll/FormatDisk.h"
#include "../iShareDll/WindowsFunc.h"
#include "resource.h"
#include "iShareISO.h"
#include "Wizards.h"
#include "PageTask.h"
#include "PageFormat.h"

FORMATDISKPARAALL m_pFormatPara;  //格式化自定义参数
//int m_FormatSuccessTimes = 0;	//格式化成功的次数
bool m_FormatFinished = false;
std::wstring sAllUsedLetters = _T("");

//https://blog.csdn.net/liangyuannao/article/details/7905368
PVOID g_hNotifyDevNodeFormat = NULL;

void EnablePartitionControls(HWND hWnd, BOOL bEnable)
{
	EnableWindow(GetDlgItem(hWnd, IDC_EDIT_CAPACITY), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_EDIT_CAPACITY2), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_COMBO_CLIENTLETTER2), bEnable);
}

void EnableFormatControls(HWND hWnd, BOOL bEnable)
{
	EnablePartitionControls(hWnd, bEnable);
	//EnablePartitionControls(hWnd, bEnable && (BST_CHECKED == SendDlgItemMessage(hWnd, IDC_CHECK_PARTITION2, BM_GETCHECK, 0, 0)));
	EnableWindow(GetDlgItem(hWnd, IDC_COMBO_VOLUME), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_COMBO_CLIENTLETTER), bEnable);
	//EnableWindow(GetDlgItem(hWnd, IDC_CHECK_PARTITION2), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_CHECK_LOG), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_CHECK_GPT), bEnable);
	PostMessage(g_hwndPropSheet, PSM_SETWIZBUTTONS, 0, bEnable ? PSWIZB_NEXT : 0);
}

void FormatDisk(HWND hdlg, DWORD nDiskID, DWORD dwCapacityMB, PFORMATDISKPARAALL pFormatPara, BOOL bShowLog)
{
	auto sFormatScript = FormatDiskScript(nDiskID, dwCapacityMB, pFormatPara);
	std::wstring sNewDiskLetter;//新格式化的磁盘盘符
	bool result(false);
	//格式化十次
	for (auto i = 0; i < 3; i++) {
		result = RunDiskpartScript(sFormatScript, bShowLog);
		if (result) {
			//检查是否正确格式化，防止磁盘服务超时导致的失败
			sNewDiskLetter = GetDiskVolumeLetters(nDiskID);
			if (sNewDiskLetter.size() >= pFormatPara->nPartitionCount) {
				HideDiskAfterFormat(sNewDiskLetter, pFormatPara);
				break;
			}
			else {
				Sleep(1000 * (i + 1));//暂停1秒后再试试
			}
		}
	}
	m_FormatFinished = true;
	EnableFormatControls(hdlg, TRUE);
	InitDiskComboVolumeList(hdlg);
	MessageBox(hdlg, LoadStringFromID(result ? MSG_FORMAT_OK : MSG_FORMAT_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), result ? MB_OK : MB_ICONERROR);
	if (result) {
		SendMessage(g_hwndPropSheet, PSM_PRESSBUTTON, PSBTN_NEXT, 0);
	}
}

void UpdateFormatProgress(HWND hdlg)
{
	DWORD dwPercent(0);
	while (!m_FormatFinished) {
		SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, dwPercent, 0);
		if (dwPercent < 100)
			dwPercent += 10;
		else
			dwPercent = 0;
		Sleep_Second();;    // 这种更好用
	}
	SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, 100, 0);
}

void CheckCapacity(HWND hdlg, DWORD* pCapacityGB = NULL, DWORD* pFirstPartGB = NULL, DWORD* pSecondPartGB = NULL)
{
	DWORD dwCapacityGB(0), dwFirstPartGB(0), dwSecondPartGB(0);
	auto dwVolumeCurSel = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETCURSEL, 0, 0);
	if (CB_ERR != dwVolumeCurSel) {
		dwCapacityGB = (g_lDiskLists[dwVolumeCurSel].get<1>()) / 1024 / 1024 / 1024; //总磁盘大小
		std::wstring sCapacity;
		GetDlgItemText(hdlg, IDC_EDIT_CAPACITY, tmpstr(sCapacity, MAX_LOADSTRING), MAX_LOADSTRING);
		dwFirstPartGB = wsting2DWORD(sCapacity);
		GetDlgItemText(hdlg, IDC_EDIT_CAPACITY2, tmpstr(sCapacity, MAX_LOADSTRING), MAX_LOADSTRING);
		dwSecondPartGB = wsting2DWORD(sCapacity);
		if (dwCapacityGB > 0) {
			if (dwFirstPartGB + dwSecondPartGB > dwCapacityGB) {
				dwFirstPartGB = dwCapacityGB;
				dwSecondPartGB = 0;
			}
			else if (dwFirstPartGB || dwSecondPartGB) {
				if (dwFirstPartGB == 0) {
					dwFirstPartGB = dwCapacityGB - dwSecondPartGB;
				}
				else if (dwSecondPartGB == 0) {
					dwSecondPartGB = dwCapacityGB - dwFirstPartGB;
				}
			}
		}
		else {
			dwFirstPartGB = dwSecondPartGB = 0;
		}
		SetDlgItemText(hdlg, IDC_EDIT_CAPACITY, DWORD2wstring(dwFirstPartGB).c_str());
		SetDlgItemText(hdlg, IDC_EDIT_CAPACITY2, DWORD2wstring(dwSecondPartGB).c_str());
	}
	if (pCapacityGB) *pCapacityGB = dwCapacityGB;
	if (pFirstPartGB) *pFirstPartGB = dwFirstPartGB;
	if (pSecondPartGB) *pSecondPartGB = dwSecondPartGB;
}

INT_PTR CALLBACK FormatDlgProc(HWND hdlg, UINT uMessage, WPARAM wParam, LPARAM lParam)
{
	int wmId, wmEvent;
	LPNMHDR     lpnmhdr;
	switch (uMessage)
	{
	case WM_INITDIALOG:
	{
		InitDiskComboVolumeList(hdlg);
		SetDlgItemText(hdlg, IDC_EDIT_CAPACITY, _T("0")); //分区容量1
		SetDlgItemText(hdlg, IDC_EDIT_CAPACITY2, _T("0")); //分区容量2
		InitLetterCombo(hdlg, IDC_COMBO_CLIENTLETTER, 0);
		InitLetterCombo(hdlg, IDC_COMBO_CLIENTLETTER2, 0);
		//EnablePartitionControls(hdlg, BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_PARTITION2, BM_GETCHECK, 0, 0));
		SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, 0, 0);
		RegisterForDevChange(hdlg, &g_hNotifyDevNodeFormat);
		TranlateCWnd(hdlg);
		EnumChildWindows(hdlg, EnumChildProc, (LPARAM)hdlg);
	}
	break;
	case WM_COMMAND:
	{
		PropSheet_Changed(GetParent(hdlg), hdlg);
		wmId = LOWORD(wParam);
		wmEvent = HIWORD(wParam);
		switch (wmId)
		{
		case IDC_EDIT_CAPACITY:
			if (EN_KILLFOCUS == wmEvent)
			{
				CheckCapacity(hdlg);
			}
			break;
		case IDC_EDIT_CAPACITY2:
			if (EN_KILLFOCUS == wmEvent)
			{
				CheckCapacity(hdlg);
			}
			break;
			//case IDC_CHECK_PARTITION2:
			//	if (BN_CLICKED == wmEvent) {
			//		EnablePartitionControls(hdlg, (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_PARTITION2, BM_GETCHECK, 0, 0)) ? TRUE : FALSE);
			//	}
			//	break;
		default:
			break;
		}
	}
	break;
	case WM_NOTIFY:
	{
		lpnmhdr = (NMHDR FAR*)lParam;
		switch (lpnmhdr->code)
		{
		case PSN_SETACTIVE:
			//this will be ignored if the property sheet is not a wizard
			PropSheet_SetWizButtons(GetParent(hdlg), PSWIZB_BACK | PSWIZB_NEXT);
			m_FormatFinished = false;
			SendMessage(hdlg, WM_INITDIALOG, 0, 0);//重新初始化一次
			break;
		case PSN_RESET:
		{
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, IDD_PAGE_RESET_INDEX);
			return IDD_PAGE_RESET_INDEX;
		}
		break;
		case PSN_WIZBACK:
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, IDD_PAGETASK);
			return (IDD_PAGETASK);
			break;
		case PSN_WIZNEXT:
		{
			bool bGoToNextPage(false);
			if (!m_FormatFinished) {
				auto clickbutton = MessageBox(hdlg, LoadStringFromID(MSG_FORMATDISK).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_YESNOCANCEL);
				bGoToNextPage = (IDNO == clickbutton);
				if (IDYES == clickbutton) {
					auto dwVolumeCurSel = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETCURSEL, 0, 0);
					auto dwLetterSel = SendDlgItemMessage(hdlg, IDC_COMBO_CLIENTLETTER, CB_GETCURSEL, 0, 0);
					auto dwLetterSel2 = SendDlgItemMessage(hdlg, IDC_COMBO_CLIENTLETTER2, CB_GETCURSEL, 0, 0);
					DWORD dwCapacityGB(0), dwFirstPartGB(0), dwSecondPartGB(0);
					CheckCapacity(hdlg, &dwCapacityGB, &dwFirstPartGB, &dwSecondPartGB);
					UINT errormsg(0);
					if (CB_ERR == dwVolumeCurSel) {
						errormsg = MESSAGE_VOLUME_ERROR;
					}
					else if (CB_ERR == dwLetterSel || CB_ERR == dwLetterSel2) {
						errormsg = MESSAGE_LETTER_ERROR;
					}
					else if (dwFirstPartGB == 0 || dwSecondPartGB == 0) {
						errormsg = MESSAGE_CAPACITY_ERROR;
					}

					if (errormsg) {
						//有参数错误显示
						MessageBox(hdlg, LoadStringFromID(errormsg).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_ICONERROR);
					}
					else {
						DWORD nDiskID = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETITEMDATA, dwVolumeCurSel, 0);
						DWORD dwCapacityMB = (g_lDiskLists[dwVolumeCurSel].get<1>()) / 1024 / 1024;
						WCHAR cLetter = SendDlgItemMessage(hdlg, IDC_COMBO_CLIENTLETTER, CB_GETITEMDATA, dwLetterSel, 0);
						WCHAR cLetter2 = SendDlgItemMessage(hdlg, IDC_COMBO_CLIENTLETTER2, CB_GETITEMDATA, dwLetterSel2, 0);
						EnableFormatControls(hdlg, FALSE);
						m_pFormatPara.bGPT = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_GPT, BM_GETCHECK, 0, 0)) ? FORMAT_PARA_UEFI_ONLY : FORMAT_PARA_LEGACY_UEFI;
						m_pFormatPara.bFormatGB = TRUE; //格式化时按照容量计算(GB)
						m_pFormatPara.nPartitionCount = 2;
						m_pFormatPara.aDiskPartitions[0].nPercent = dwFirstPartGB;
						m_pFormatPara.aDiskPartitions[0].nHideDiskMode = FORMATDISK_HIDEDISK;
						m_pFormatPara.aDiskPartitions[0].cClientLetter = cLetter;
						m_pFormatPara.aDiskPartitions[0].bVHDBootPartition = TRUE;
						m_pFormatPara.aDiskPartitions[1].nPercent = dwSecondPartGB;
						m_pFormatPara.aDiskPartitions[1].bUserDataPartition = TRUE;
						m_pFormatPara.aDiskPartitions[1].bWritebackPartition = TRUE;
						m_pFormatPara.aDiskPartitions[1].cClientLetter = cLetter2;
						auto thread1 = boost::thread(boost::bind(&FormatDisk, hdlg, nDiskID, dwCapacityMB, &m_pFormatPara,
							BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_LOG, BM_GETCHECK, 0, 0)));
						auto thread2 = boost::thread(boost::bind(&UpdateFormatProgress, hdlg));
					}
				}
			}
			else {
				bGoToNextPage = true;//上传完成
			}
			if (bGoToNextPage) {
				g_NextStepPage = PAGE_DOWNLOAD;
				UnregisterDeviceNotification(g_hNotifyDevNodeFormat);
			}
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
			return (bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
		}
		break;
		default:
			break;
		}
	}
	break;

	case WM_DEVICECHANGE:
	{
		switch (wParam)
		{
		case DBT_DEVICEARRIVAL:
			// Handle device arrival
			if (IsWindowEnabled(GetDlgItem(hdlg, IDC_COMBO_VOLUME))) {
				InitDiskComboVolumeList(hdlg);//重新初始化一次
			}
			break;

		case DBT_DEVICEQUERYREMOVE:
			// Handle device removal request
			break;

		case DBT_DEVICEREMOVECOMPLETE:
			// Handle device removal
			if (IsWindowEnabled(GetDlgItem(hdlg, IDC_COMBO_VOLUME))) {
				InitDiskComboVolumeList(hdlg);//重新初始化一次
			}
			break;
		}
	}
	break;

	default:
		break;
	}
	return FALSE;
}