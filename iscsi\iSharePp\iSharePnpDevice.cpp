﻿#include "stdafx.h"
#include <Ntddvol.h>
#include <mountdev.h>
#include <srb.h>
#include <scsi.h>
#include <Ntddkbd.h>
#include "../iShareDll/OEM.h"
#include "../iShareDll/UsbBootDef.h"
#include "../iShareDriverLib/File.h"
#include "iSharePnp.h"
#include "../iShareDriverLib/time.hpp"
#include "iSharePnpDevice.h"
#include "OpenLibSys.h"

// #define IPXE_ISO_MARK "isolinux.bin"

iSharePnpDevice::iSharePnpDevice(CWDMDriver* pWDMDriver, PCWSTR pName)
	: CWDMDevice(pWDMDriver, pName)
{
}

iSharePnpDevice::~iSharePnpDevice(void)
{
}

NTSTATUS iSharePnpDevice::DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP pIrp)
{
	if (m_DeviceObject == DeviceObject)
	{
		NTSTATUS status(STATUS_UNSUCCESSFUL);
		ULONG uBytesReturn(0);
		auto pIrpStack = IoGetCurrentIrpStackLocation(pIrp);
		switch (pIrpStack->Parameters.DeviceIoControl.IoControlCode) {
		case IOCTL_SANBOOTCONF_IBFT:
		{
			status = (static_cast<CiSharePnp*>(m_pWDMDriver))->GetiBFT(pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn);
			break;
		}
		case IOCTL_SANBOOTCONF_CONFIGRELOAD:
		{
			status = (static_cast<CiSharePnp*>(m_pWDMDriver))->EnterWindows();
			break;
		}
		case IOCTL_SANBOOTCONF_CONFIGQUERY:
		{
			status = (static_cast<CiSharePnp*>(m_pWDMDriver))->OnWindowsShutdown();
			break;
		}
		case IOCTL_SANBOOTCONF_CONFIGSET:
		{
			status = (static_cast<CiSharePnp*>(m_pWDMDriver))->FixUsbPrinter();
			break;
		}
		case IOCTL_OLS_READ_MSR:
			status = ReadMsr(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_WRITE_MSR:
			status = WriteMsr(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_READ_PMC:
			status = ReadPmc(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_HALT:
			HaltSystem();
			status = STATUS_SUCCESS;
			break;

		case IOCTL_OLS_READ_IO_PORT:
		case IOCTL_OLS_READ_IO_PORT_BYTE:
		case IOCTL_OLS_READ_IO_PORT_WORD:
		case IOCTL_OLS_READ_IO_PORT_DWORD:
			status = ReadIoPort(
				pIrpStack->Parameters.DeviceIoControl.IoControlCode,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_WRITE_IO_PORT:
		case IOCTL_OLS_WRITE_IO_PORT_BYTE:
		case IOCTL_OLS_WRITE_IO_PORT_WORD:
		case IOCTL_OLS_WRITE_IO_PORT_DWORD:
			status = WriteIoPort(
				pIrpStack->Parameters.DeviceIoControl.IoControlCode,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;

		case IOCTL_OLS_READ_PCI_CONFIG:
			status = ReadPciConfig(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_WRITE_PCI_CONFIG:
			status = WritePciConfig(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;

		case IOCTL_OLS_READ_MEMORY:
			status = ReadMemory(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		case IOCTL_OLS_WRITE_MEMORY:
			status = WriteMemory(
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.InputBufferLength,
				pIrp->AssociatedIrp.SystemBuffer,
				pIrpStack->Parameters.DeviceIoControl.OutputBufferLength,
				&uBytesReturn
			);
			break;
		default:
		{
			KdPrint(("Unrecognised IoControl %x\n", pIrpStack->Parameters.DeviceIoControl.IoControlCode));
			status = STATUS_INVALID_DEVICE_REQUEST;
			break;
		}
		}
		return DiskPerfCompleteRequest(pIrp, status, uBytesReturn, IO_NO_INCREMENT);
	}
	else
	{
		return CWDMDevice::DeviceControl(DeviceObject, pIrp);
	}
}

NTSTATUS iSharePnpDevice::Shutdown(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	(static_cast<CiSharePnp*>(m_pWDMDriver))->OnWindowsShutdown();
	return Dispatch(DeviceObject, Irp);
}

NetDevice::NetDevice(CWDMDriver* pWDMDriver)
	: CWDMDeviceFlt(pWDMDriver)
{
}

NetDevice::~NetDevice(void)
{
}

NTSTATUS NetDevice::PnP(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	PIO_STACK_LOCATION  irpSp = IoGetCurrentIrpStackLocation(Irp);
	//拦截查询移除请求
	if (irpSp->MinorFunction == IRP_MN_QUERY_REMOVE_DEVICE)
		// 		|| irpSp->MinorFunction == IRP_MN_REMOVE_DEVICE)
		return DiskPerfCompleteRequest(Irp, STATUS_UNSUCCESSFUL);
	return  CWDMDeviceFlt::PnP(DeviceObject, Irp);
}

// ULONG   DiskDevice::g_dwDiskMark = 0; //全局磁盘编号
DiskDevice::DiskDevice(CWDMDriver* pWDMDriver, BOOLEAN bUsbStorage, const DWORD& bDenyWriteUsbDisk, const DWORD& bClearSerialNumber)
	: CWDMDeviceFlt(pWDMDriver)
	, m_bUsbStorage(bUsbStorage)
	, m_bDenyWriteUsbDisk(bDenyWriteUsbDisk)
	, m_bClearSerialNumber(bClearSerialNumber)
{
	//初始化硬盘签名为随机值,避免母盘里的值为1时和本地硬盘的签名重复,而导致蓝屏.
// 	if (g_dwDiskMark == 0) {
// 		g_dwDiskMark = (ULONG)GetSystemTime();
// 	}
// 	m_dwDiskMark = ++g_dwDiskMark;
}

DiskDevice::~DiskDevice(void)
{
}

// VOID DiskDevice::OnTargetDeviceAdded()
// {
// 	m_TargetDeviceSystem.Attach(GetTargetDevice());
// }

// VOID DiskDevice::OnTargetDeviceBeforeRemove(PDEVICE_OBJECT DeviceObject)
// {
// 	UNREFERENCED_PARAMETER(DeviceObject);
// 	m_TargetDeviceSystem.Detach();
// }

NTSTATUS DiskDevice::SCSI(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	if (m_bUsbStorage && m_bDenyWriteUsbDisk) {
		return  ForwardIrpAsynchronous(Irp);
	}
	else {
		return CWDMDeviceFlt::SCSI(DeviceObject, Irp);
	}
}

//http://www.it610.com/article/4783259.htm
NTSTATUS DiskDevice::OnAsynIRPComplete(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	auto irpStack = IoGetCurrentIrpStackLocation(Irp);
	auto CurSrb = irpStack->Parameters.Scsi.Srb;
	if (CurSrb) {
		auto cdb = (PCDB)CurSrb->Cdb;
		if (cdb) {
			if (m_bUsbStorage) {
				if (m_bDenyWriteUsbDisk &&
					cdb->CDB6GENERIC.OperationCode == SCSIOP_MODE_SENSE && CurSrb->DataBuffer
					&& CurSrb->DataTransferLength >= sizeof(MODE_PARAMETER_HEADER)) {
					auto modeData = (PMODE_PARAMETER_HEADER)CurSrb->DataBuffer;
					if (modeData) {
						modeData->DeviceSpecificParameter |= MODE_DSP_WRITE_PROTECT;
					}
				}
			}
			//else {
			   // //https://github.com/gregkh/ddrdrive/blob/master/docs/ddrdrivex1_serial_number.cpp
			   // if (m_bClearSerialNumber &&
			   //	 cdb->CDB6INQUIRY3.OperationCode == SCSIOP_INQUIRY && cdb->CDB6INQUIRY3.PageCode == 0x80) {
			   //	 if (CurSrb->DataBuffer && CurSrb->DataTransferLength >= 4) {
			   //		 //修改硬盘序列号
			   //		 PUCHAR pByteBuffer = (PUCHAR)CurSrb->DataBuffer;
			   //		 if (pByteBuffer) {
			   //			 pByteBuffer[3] = 0;			// Byte03:  PAGE LENGTH[7:0]=(n-3)
			   //			 CurSrb->DataTransferLength = 4;
			   //			 CurSrb->SenseInfoBufferLength = 0;
			   //			 CurSrb->SrbStatus = SRB_STATUS_SUCCESS;
			   //			 CurSrb->ScsiStatus = SCSISTAT_GOOD;
			   //		 }
			   //	 }
			   // }
			//}
		}
	}
	if (Irp->PendingReturned) {
		IoMarkIrpPending(Irp);
	}
	return Irp->IoStatus.Status;
}

NTSTATUS DiskDevice::OnAsynIRPComplete1(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	auto irpsp = IoGetCurrentIrpStackLocation(Irp);
	if (NT_SUCCESS(Irp->IoStatus.Status)
		&& irpsp->Parameters.DeviceIoControl.OutputBufferLength >= sizeof(STORAGE_PROPERTY_QUERY)) {
		// Check the output buffer for StorageDeviceDescriptor
		auto sdd = (PSTORAGE_DEVICE_DESCRIPTOR)Irp->AssociatedIrp.SystemBuffer;
		if (sdd && sdd->SerialNumberOffset > sizeof(STORAGE_PROPERTY_QUERY) && sdd->SerialNumberOffset < sdd->Size) {
			// Get the serial number pointer and modify it as needed
			//auto serialNumber = (PCHAR)sdd + sdd->SerialNumberOffset;
			//strcpy(serialNumber, "");
			sdd->SerialNumberOffset = 0;
		}
	}
	if (Irp->PendingReturned) {
		IoMarkIrpPending(Irp);
	}
	return Irp->IoStatus.Status;
}

NTSTATUS DiskDevice::DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	if (!m_bUsbStorage && m_bClearSerialNumber) {
		auto irpsp = IoGetCurrentIrpStackLocation(Irp);
		if (IOCTL_STORAGE_QUERY_PROPERTY == irpsp->Parameters.DeviceIoControl.IoControlCode
			&& irpsp->Parameters.DeviceIoControl.InputBufferLength >= sizeof(STORAGE_PROPERTY_QUERY)) {
			auto spq = (PSTORAGE_PROPERTY_QUERY)Irp->AssociatedIrp.SystemBuffer;
			if (spq && spq->PropertyId == StorageDeviceProperty && spq->QueryType == PropertyStandardQuery) {
				return  ForwardIrpAsynchronous1(Irp);
			}
		}
	}
	return CWDMDeviceFlt::DeviceControl(DeviceObject, Irp);
}

// NTSTATUS DiskDevice::Read(PDEVICE_OBJECT DeviceObject, PIRP Irp)
// {
// // 	if (m_bDenyWriteUsbDisk<2){
// 		auto irpSp = IoGetCurrentIrpStackLocation(Irp);
// 		//拦截读取MBR的请求
// 		if (IRP_MJ_READ == irpSp->MajorFunction && irpSp->Parameters.Read.ByteOffset.QuadPart == 0 && irpSp->Parameters.Read.Length >= SECSIZE){
// 			PVOID Buffer(NULL);//irp中包括的数据地址
// 			//获取这个irp其中包含的缓存地址，这个地址可能来自mdl，也可能就是直接的缓冲，这取决于我们当前设备的io方式是buffer还是direct方式
// 			//buffer模式下取SystemBuffer, Direct模式下取MdlAddress,混合模式下去userbuffer(需要在用户线程下读写数据)
// 			if (GetTargetDevice()->Flags & DO_DIRECT_IO && Irp->MdlAddress){//mdl模式
// 				//http://www.cnblogs.com/fanzi2009/archive/2010/12/20/1911451.html 有可能返回NULL
// 				//使用后不要用MmUnmapLockedPages释放，这个mdl的虚拟地址由IoCompleteRequest释放
// 				for (int i = 0; i < 10 && Buffer == NULL; ++i)
// 					Buffer = MmGetSystemAddressForMdlSafe(Irp->MdlAddress, NormalPagePriority);
// 			}
// 			else if (GetTargetDevice()->Flags & DO_BUFFERED_IO && Irp->AssociatedIrp.SystemBuffer){
// 				Buffer = Irp->AssociatedIrp.SystemBuffer;
// 			}
// 			NTSTATUS status(STATUS_UNSUCCESSFUL);
// 			ULONG BytesReaded(0);
// 			if (Buffer != NULL){
// 				status = m_TargetDeviceSystem.Read(Buffer, irpSp->Parameters.Read.Length, &irpSp->Parameters.Read.ByteOffset, &BytesReaded);
// 				if (NT_SUCCESS(status) && BOOT_RECORD_SIGNATURE == ((PPACKED_BOOT_SECTOR_MBR)Buffer)->bsSignature){
// 					// 系统启动时磁盘的选择是根据 MBR里的磁盘签名和注册表里的MountedDevices的\DosDevices\C:值里的签名一致才行
// 					//如果本地磁盘MBR里的签名和无盘里的MountedDevices的\DosDevices\C:值里的签名一致,就会导致无盘启动到本地磁盘上
// 					((PPACKED_BOOT_SECTOR_MBR)Buffer)->dwDiskMark = m_dwDiskMark; //清除磁盘签名,windows7以此为标准
// 					for (int i = 0; i < NUM_PARTITION_TABLE_ENTRIES; i++) {
// 						if (((PPACKED_BOOT_SECTOR_MBR)Buffer)->bsPartEnts[i].ActiveFlag == PARTITION_ACTIVE_FLAG) {//把激活的可启动分区撤销,xp以此为标准
// 							((PPACKED_BOOT_SECTOR_MBR)Buffer)->bsPartEnts[i].ActiveFlag = 0;
// 						}
// 					}
// 				}
// 			}
// 			return DiskPerfCompleteRequest(Irp, status, BytesReaded);
// 		}
// 		else{
// 			return CWDMDeviceFlt::Read(DeviceObject, Irp);
// 		}
// // 	}
// // 	else{
// // 		return DiskPerfCompleteRequest(Irp, STATUS_ACCESS_DENIED);
// // 	}
// }

// NTSTATUS DiskDevice::Write(PDEVICE_OBJECT DeviceObject, PIRP Irp)
// {
// 	auto irpSp = IoGetCurrentIrpStackLocation(Irp);
// 	//拦截读取MBR的请求
// 	if (IRP_MJ_WRITE == irpSp->MajorFunction
// 		&& irpSp->Parameters.Write.ByteOffset.QuadPart >= 0
// 		&& irpSp->Parameters.Write.ByteOffset.QuadPart < 8192   //grub mbr length
// 		/*&& irpSp->Parameters.Write.Length >= SECSIZE*/) {
// 		return DiskPerfCompleteRequest(Irp, STATUS_ACCESS_DENIED);
// 	}
// 	else {
// 		return CWDMDeviceFlt::Write(DeviceObject, Irp);
// 	}
// }

VolumeDevice::VolumeDevice(CWDMDriver* pWDMDriver, const DWORD& bDenyWriteUsbDisk, const NIC_PARA& NicPara, const CHAR& cDiskLetterLocalFirst, const CHAR& cDiskLetterUsbLast)
	: CWDMDeviceFlt(pWDMDriver)
	, m_cDiskLetterLocalFirst(cDiskLetterLocalFirst)
	, m_cDiskLetterUsbLast(cDiskLetterUsbLast)
	, m_bDenyWriteUsbDisk(bDenyWriteUsbDisk)
	, m_bLocalDisk(FALSE)
	, m_bUsbDisk(FALSE)
	, m_cDiskLetterCur(0)
	, m_cDiskLetterSet(0)
	, m_NicPara(NicPara)
{
}

VolumeDevice::~VolumeDevice(void)
{
}

VOID VolumeDevice::OnTargetDeviceAdded()
{
	m_TargetDeviceSystem.Attach(GetTargetDevice());
}

VOID VolumeDevice::OnTargetDeviceBeforeRemove(PDEVICE_OBJECT DeviceObject)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	m_TargetDeviceSystem.Detach();
}

NTSTATUS VolumeDevice::DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	PIO_STACK_LOCATION irpsp = IoGetCurrentIrpStackLocation(Irp);
	if (IOCTL_VOLUME_ONLINE == irpsp->Parameters.DeviceIoControl.IoControlCode) {
		NTSTATUS status = DiskPerfForwardIrpSynchronous(Irp);//向下转发成功
		//因为返回值 status 可能不成功, 故意不检查, 直接读取设备信息
		//磁盘上线时,读取所有的磁盘信息,包括设置
		//新磁盘，xp只会通知上线一次，win7之后会通知上线两次，
		//第一次都是无盘符，第二次就是系统自动分配的盘符
		OnVolumeOnline();
		DiskPerfCompleteRequest(Irp, status);
		return status;
	}
	return CWDMDeviceFlt::DeviceControl(DeviceObject, Irp);
}

VOID VolumeDevice::OnVolumeOnline() {
	QueryName();
	QueryProduct();
	ReCheckDiskLetter();
	((CiSharePnp*)GetWDMDriver())->OnVolumeOnline(m_sProductName, m_cDiskLetterCur);
	return;
}

VOID VolumeDevice::QueryName()
{
	if (m_sDiskName.GetLength() == 0) {
		NTSTATUS status(STATUS_UNSUCCESSFUL);
		ULONG uBytesReturned(0);
		CSimpleBuffer buffer;
		buffer.resize(sizeof(MOUNTDEV_NAME));
		status = m_TargetDeviceSystem.DeviceIoControl(IOCTL_MOUNTDEV_QUERY_DEVICE_NAME, NULL, 0, buffer.data(), buffer.size(), &uBytesReturned);
		if (status == STATUS_BUFFER_OVERFLOW) {
			buffer.resize(sizeof(MOUNTDEV_NAME) + ((PMOUNTDEV_NAME)buffer.data())->NameLength);
			status = m_TargetDeviceSystem.DeviceIoControl(IOCTL_MOUNTDEV_QUERY_DEVICE_NAME, NULL, 0, buffer.data(), buffer.size(), &uBytesReturned);
		}
		if (NT_SUCCESS(status)) {
			m_sDiskName.AppendBuffer((PCHAR)(((PMOUNTDEV_NAME)buffer.data())->Name), min(((PMOUNTDEV_NAME)buffer.data())->NameLength, (USHORT)-1));
		}
	}
}

VOID VolumeDevice::QueryProduct()
{
	if (m_sProductName.GetLength() == 0) {
		QueryProductName(m_TargetDeviceSystem, &m_sProductName, &m_bLocalDisk, &m_bUsbDisk);
	}
}

NTSTATUS VolumeDevice::QueryProductName(CDeviceSystem& TargetDeviceSystem, PCStringW sProductName, PBOOLEAN bLocalDisk, PBOOLEAN bUsbDisk)
{
	STORAGE_PROPERTY_QUERY   Query;  //   查询输入参数
	Query.PropertyId = StorageDeviceProperty;  //   指定查询方式
	Query.QueryType = PropertyStandardQuery;
	CSimpleBuffer buffer;
	buffer.resize(sizeof(STORAGE_DEVICE_DESCRIPTOR));
	ULONG uBytesReturned(0);
	auto status = TargetDeviceSystem.DeviceIoControl(IOCTL_STORAGE_QUERY_PROPERTY,
		&Query, sizeof(STORAGE_PROPERTY_QUERY), //   输入数据缓冲区
		buffer.data(), buffer.size(),			  //   输出数据缓冲区
		&uBytesReturned);   //   输出数据长度
	if (NT_SUCCESS(status) && buffer.size() != ((PSTORAGE_DEVICE_DESCRIPTOR)buffer.data())->Size) {
		buffer.resize(((PSTORAGE_DEVICE_DESCRIPTOR)buffer.data())->Size);
		status = TargetDeviceSystem.DeviceIoControl(IOCTL_STORAGE_QUERY_PROPERTY,
			&Query, sizeof(STORAGE_PROPERTY_QUERY), //   输入数据缓冲区
			buffer.data(), buffer.size(),			  //   输出数据缓冲区
			&uBytesReturned);   //   输出数据长度
	}
	if (NT_SUCCESS(status)) {
		CStringW sDProductName((LPCSTR)buffer.data() + ((PSTORAGE_DEVICE_DESCRIPTOR)buffer.data())->ProductIdOffset);
		if (sProductName) {
			*sProductName = sDProductName;
		}
		auto nBusType = ((PSTORAGE_DEVICE_DESCRIPTOR)buffer.data())->BusType;
		if (bLocalDisk) {
			// microsoft vhd 虚拟盘总线是 BusTypeFileBackedVirtual
			*bLocalDisk = (BusTypeiScsi != nBusType && BusTypeFileBackedVirtual != nBusType && BusTypeUsb != nBusType
				&& sDProductName.CompareNoCase(MICROSOFT_VHD_DISK_PRODUCTID, _countof(MICROSOFT_VHD_DISK_PRODUCTID) - 1) != 0
				&& sDProductName.CompareNoCase(VBOOT_XP_VHD_DISK_PRODUCTID, _countof(VBOOT_XP_VHD_DISK_PRODUCTID) - 1) != 0) ? TRUE : FALSE;
			//*bLocalDisk = (sDProductName.CompareNoCase(ISHARE_SCSI_DISK_PRODUCTID, _countof(ISHARE_SCSI_DISK_PRODUCTID) - 1) != 0
			//	&& sDProductName.CompareNoCase(ISHARE_SCSI_PERSONALDISK_PRODUCTID, _countof(ISHARE_SCSI_PERSONALDISK_PRODUCTID) - 1) != 0
			//	&& sDProductName.CompareNoCase(COMMON_SCSI_DISK_PRODUCTID, _countof(COMMON_SCSI_DISK_PRODUCTID) - 1) != 0
			//	&& sDProductName.CompareNoCase(SOULLON_SCSI_DISK_PRODUCTID, _countof(SOULLON_SCSI_DISK_PRODUCTID) - 1) != 0
			//	&& sDProductName.CompareNoCase(MICROSOFT_VHD_DISK_PRODUCTID, _countof(MICROSOFT_VHD_DISK_PRODUCTID) - 1) != 0
			//	&& sDProductName.CompareNoCase(VBOOT_XP_VHD_DISK_PRODUCTID, _countof(VBOOT_XP_VHD_DISK_PRODUCTID) - 1) != 0);
		}
		if (bUsbDisk) {
			*bUsbDisk = (BusTypeUsb == nBusType) ? TRUE : FALSE;
		}
	}
	return status;
}

NTSTATUS VolumeDevice::Read(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	if (/*m_bLocalDisk &&*/ m_bUsbDisk && m_bDenyWriteUsbDisk == USBMODE_DENYALL) {
		return DiskPerfCompleteRequest(Irp, STATUS_ACCESS_DENIED);
	}
	else {
		return CWDMDeviceFlt::Read(DeviceObject, Irp);
	}
}

// NTSTATUS VolumeDevice::Write(PDEVICE_OBJECT DeviceObject, PIRP Irp)
// {
// 	if (m_bLocalDisk && m_bUsbDisk && m_bDenyWriteUsbDisk) {
// 		return DiskPerfCompleteRequest(Irp, STATUS_ACCESS_DENIED);
// 	}
// 	else {
// 		return CWDMDeviceFlt::Write(DeviceObject, Irp);
// 	}
// }

//https://blog.csdn.net/bruce135lee/article/details/77461089
//http://blog.chinaunix.net/uid-21323988-id-1827852.html
//https://blog.csdn.net/itcastcpp/article/details/4679301
//http://www.codeweblog.com/c-c-%E5%BA%95%E5%B1%82%E5%AE%9E%E7%8E%B0%E6%8C%87%E5%AE%9A%E7%A3%81%E7%9B%98%E5%8F%AA%E8%AF%BB/
//“带写保护功能”的U盘, 燕狂徒写的驱动挂钩，限制磁盘只读
/*
NTSTATUS VolumeDevice::SCSI(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	if (m_bLocalDisk && m_bUsbDisk && m_bDenyWriteUsbDisk) {
		NTSTATUS status = DiskPerfForwardIrpSynchronous(Irp);//开始底层的设备
		if (NT_SUCCESS(status)){
			auto irpStack = IoGetCurrentIrpStackLocation(Irp);
			auto CurSrb = irpStack->Parameters.Scsi.Srb;
			auto cdb = (PCDB)CurSrb->Cdb;
			auto opCode = cdb->CDB6GENERIC.OperationCode;
			if (opCode == SCSIOP_MODE_SENSE && CurSrb->DataBuffer
				&& CurSrb->DataTransferLength >= sizeof(MODE_PARAMETER_HEADER)){
				auto modeData = (PMODE_PARAMETER_HEADER)CurSrb->DataBuffer;
				modeData->DeviceSpecificParameter |= MODE_DSP_WRITE_PROTECT;
			}
		}
		DiskPerfCompleteRequest(Irp, status);
		return status;
	}
	else {
		return CWDMDeviceFlt::SCSI(DeviceObject, Irp);
	}
}
*/

VOID VolumeDevice::QueryLetter()
{
	UNICODE_STRING	DosName = { 0 };
	NTSTATUS status = IoVolumeDeviceToDosName(m_TargetDeviceSystem, &DosName);
	if (NT_SUCCESS(status) && DosName.Buffer) {
		//隐藏盘符形式 \\?\Volume{8ba88944-fcf7-11e8-b3f2-806e6f6e6963}
		//正常盘符 E:
		//装载点 E:\mountfolder
		WCHAR DosLetter = RtlUpcaseUnicodeChar(DosName.Buffer[0]);
		if (UNICODE_STRING_LENGTH(DosName) == 2 && DosLetter >= L'A' && DosLetter <= L'Z') {
			m_cDiskLetterCur = (CHAR)DosLetter;
		}
		else {
			//第一次上线也可能无盘符， win7之后会通知两次，第二次是系统自动分配的盘符
			m_cDiskLetterCur = HIDN_DISK_LETTER1;
		}
		ExFreePool(DosName.Buffer);
	}
}

VOID VolumeDevice::ReadDriverLetterSet()
{
	//禁止usb或者禁止所有的本地盘，只能删除盘符，系统才不会卡住
	//if (m_bLocalDisk && ( m_bUsbDisk ? (m_bDenyWriteUsbDisk == USBMODE_DENYALL) : ((CiSharePnp*)GetWDMDriver())->GetHideLocalDisk()) ) {
	if ((m_bLocalDisk && ((CiSharePnp*)GetWDMDriver())->GetHideLocalDisk())
		|| (m_bUsbDisk && m_bDenyWriteUsbDisk == USBMODE_DENYALL)) {
		m_cDiskLetterSet = HIDN_DISK_LETTER1;
	}
	else {
		if (!NT_SUCCESS(ReadDriverLetter())) {
			((CiSharePnp*)GetWDMDriver())->ReadDiskLetterSet(m_sDiskName, &m_cDiskLetterSet);
		}
	}
}

NTSTATUS VolumeDevice::ReadDriverLetter()
{
	CSimpleBuffer partition_dbr;
	if (!partition_dbr.resize(DBR_SIZE))
		return STATUS_INSUFFICIENT_RESOURCES;

	LARGE_INTEGER offset; // = RtlConvertLongToLargeInteger(0);
	offset.QuadPart = 0;
	ULONG bytereturned(0);
	NTSTATUS status = m_TargetDeviceSystem.Read(partition_dbr.data(), partition_dbr.size(), &offset, &bytereturned);
	if (NT_SUCCESS(status)) {
		CHAR dosletter = ((PCHAR)partition_dbr.data())[CLIENTLETTER_OFFSET];
		//不允许修改系统盘//如果本地磁盘MBR里的签名和无盘里的MountedDevices的\DosDevices\C:值里的签名一致,就会导致无盘启动到本地磁盘上
		if (bytereturned == DBR_SIZE && IsGoodDiskletterSet(dosletter)) {
			m_cDiskLetterSet = dosletter;
		}
		else {
			status = STATUS_UNSUCCESSFUL;
		}
	}
	return status;
}

VOID VolumeDevice::CheckDriverLetter()
{
	if (m_cDiskLetterCur != 0 && m_cDiskLetterSet != 0 && m_cDiskLetterCur != m_cDiskLetterSet) {
		if (m_cDiskLetterCur != HIDN_DISK_LETTER1) {
			//非隐藏有盘符的盘，需要隐藏 或者 与设置的盘符也不一致，需要卸掉盘符
			if (NeedHideDisk() || IsGoodDiskletter(m_cDiskLetterSet)) {
				//卸掉盘符,隐藏起来
				if (NT_SUCCESS(Unmount(m_cDiskLetterCur))) {
					m_cDiskLetterCur = HIDN_DISK_LETTER1;
				}
			}
		}
		else {
			//隐藏的盘，盘符设置为非隐藏，但是不是正确的盘符，就自动分配个
			if (!NeedHideDisk() && !IsGoodDiskletter(m_cDiskLetterSet)) {
				//也没有设置盘符，自动装载一个
				MountNextDriverLetter();
			}
		}
		if (IsGoodDiskletter(m_cDiskLetterSet)) {
			//装载正确的盘符
			for (auto i = 0; i < 3; i++) {
				if (NT_SUCCESS(Mount(m_sDiskName, m_cDiskLetterSet))) {
					m_cDiskLetterCur = m_cDiskLetterSet;
					break;
				}
				//可能盘符冲突，卸掉盘符先
				if (NT_SUCCESS(Unmount(m_cDiskLetterSet))) {
					list< CWDMDevice_ptr >& lDeviceObjects(m_pWDMDriver->GetWDMDevices());
					for (auto iter = lDeviceObjects.begin(); iter != lDeviceObjects.end(); iter++) {
						if ((*iter)->IsFilterDevice() == WDMDEVICE_VOLUME
							&& (static_cast<VolumeDevice*>((*iter).get()))->GetDiskLetterCur() == m_cDiskLetterSet) {
							(static_cast<VolumeDevice*>((*iter).get()))->SetDiskLetterCur(HIDN_DISK_LETTER1);
							break;
						}
					}
				}
				else {
					break;
				}
			}
		}
	}
	else if (m_cDiskLetterCur == HIDN_DISK_LETTER1 && m_cDiskLetterSet == 0 &&
		m_bUsbDisk && m_bDenyWriteUsbDisk != USBMODE_DENYALL) {
		//当前U盘没有盘符，磁盘管理里也无法加入盘符，就自动添加个
		MountNextDriverLetter();
	}
}

VOID VolumeDevice::MountNextDriverLetter()
{
	list< CWDMDevice_ptr >& lDeviceObjects(m_pWDMDriver->GetWDMDevices());
	auto CheckMountFun = [&lDeviceObjects, this](CHAR dosletter) {
		bool bFound(false);
		for (auto iter = lDeviceObjects.begin(); iter != lDeviceObjects.end(); iter++) {
			if ((*iter)->IsFilterDevice() == WDMDEVICE_VOLUME
				&& (static_cast<VolumeDevice*>((*iter).get()))->GetDiskLetterCur() == dosletter) {
				bFound = true;
				break;
			}
		}
		//有可能被光驱占用
		if (!bFound && NT_SUCCESS(Mount(m_sDiskName, dosletter))) {
			m_cDiskLetterCur = dosletter;
			return TRUE;
		}
		return FALSE;
	};
	if (m_bUsbDisk) {
		//U盘从后往前挂载盘符
		for (CHAR dosletter = m_cDiskLetterUsbLast; dosletter >= SYSTEMLETTER_CHAR + 1; dosletter--) {
			if (CheckMountFun(dosletter))
				break;
		}
	}
	else {
		CHAR dosletter = SYSTEMLETTER_CHAR + 1;
		if (m_bLocalDisk) {
			//本地盘从指定盘符开始挂载
			dosletter = m_cDiskLetterLocalFirst;
		}
		for (; dosletter <= LASTLETTER_CHAR; dosletter++) {
			if (CheckMountFun(dosletter))
				break;
		}
	}
}

NTSTATUS VolumeDevice::Unmount(CHAR dosletter)
{
	NTSTATUS status(STATUS_UNSUCCESSFUL);
	CFile hMountPoint;
	if (IsGoodDiskletter(dosletter) && NT_SUCCESS(hMountPoint.OpenAsy(MOUNTMGR_DEVICE_NAME))) {
		CStringW SymbolicLinkName;
		SymbolicLinkName.Format(DOSDEVICE_FORMAT, dosletter);
		CSimpleBuffer inputbuffer;
		if (!inputbuffer.resize(sizeof(MOUNTMGR_MOUNT_POINT) + SymbolicLinkName.GetLengthByte()))
			return STATUS_INSUFFICIENT_RESOURCES;
		inputbuffer.ZeroMemory();
		PMOUNTMGR_MOUNT_POINT buffer = (PMOUNTMGR_MOUNT_POINT)inputbuffer.data();
		buffer->SymbolicLinkNameOffset = sizeof(MOUNTMGR_MOUNT_POINT);
		buffer->SymbolicLinkNameLength = SymbolicLinkName.GetLengthByte();
		RtlCopyMemory((PCHAR)buffer + buffer->SymbolicLinkNameOffset, (LPCWSTR)SymbolicLinkName, buffer->SymbolicLinkNameLength);
		CSimpleBuffer outputbuffer;
		if (!outputbuffer.resize(sizeof(MOUNTMGR_MOUNT_POINTS)))
			return STATUS_INSUFFICIENT_RESOURCES;
		ULONG bytesreturned(0);
		status = hMountPoint.DeviceIoControl(IOCTL_MOUNTMGR_DELETE_POINTS, inputbuffer.data(), inputbuffer.size(), outputbuffer.data(), outputbuffer.size(), &bytesreturned);
		if (status == STATUS_BUFFER_OVERFLOW) {
			if (!outputbuffer.resize(((PMOUNTMGR_MOUNT_POINTS)outputbuffer.data())->Size))
				return STATUS_INSUFFICIENT_RESOURCES;
			status = hMountPoint.DeviceIoControl(IOCTL_MOUNTMGR_DELETE_POINTS, inputbuffer.data(), inputbuffer.size(), outputbuffer.data(), outputbuffer.size(), &bytesreturned);
		}
	}
	return status;
}

NTSTATUS VolumeDevice::Mount(CStringW sDeviceName, CHAR dosletter)
{
	NTSTATUS status(STATUS_UNSUCCESSFUL);
	CFile hMountPoint;
	if (sDeviceName.GetLength() > 0 && IsGoodDiskletter(dosletter) && NT_SUCCESS(hMountPoint.OpenAsy(MOUNTMGR_DEVICE_NAME))) {
		CStringW SymbolicLinkName;
		SymbolicLinkName.Format(DOSDEVICE_FORMAT, dosletter);
		CSimpleBuffer inputbuffer;
		if (!inputbuffer.resize(sizeof(MOUNTMGR_CREATE_POINT_INPUT) + SymbolicLinkName.GetLengthByte() + sDeviceName.GetLengthByte()))
			return STATUS_INSUFFICIENT_RESOURCES;
		PMOUNTMGR_CREATE_POINT_INPUT buffer = (PMOUNTMGR_CREATE_POINT_INPUT)inputbuffer.data();
		buffer->SymbolicLinkNameOffset = sizeof(MOUNTMGR_CREATE_POINT_INPUT);
		buffer->SymbolicLinkNameLength = SymbolicLinkName.GetLengthByte();
		buffer->DeviceNameOffset = buffer->SymbolicLinkNameOffset + buffer->SymbolicLinkNameLength;
		buffer->DeviceNameLength = sDeviceName.GetLengthByte();
		RtlCopyMemory((PCHAR)buffer + buffer->SymbolicLinkNameOffset, (LPCWSTR)SymbolicLinkName, buffer->SymbolicLinkNameLength);
		RtlCopyMemory((PCHAR)buffer + buffer->DeviceNameOffset, (LPCWSTR)sDeviceName, buffer->DeviceNameLength);
		ULONG bytesreturned(0);
		status = hMountPoint.DeviceIoControl(IOCTL_MOUNTMGR_CREATE_POINT, inputbuffer.data(), inputbuffer.size(), NULL, 0, &bytesreturned);
	}
	return status;
}

KeyBoardDevice::KeyBoardDevice(CWDMDriver* pWDMDriver, const BOOLEAN& bDisableCtrlAtlDel,
	const BOOLEAN& bDisableKeyboard, const UINT* nEnableKey, const BOOLEAN& bMonitorKeyboard)
	: CWDMDeviceFlt(pWDMDriver)
	, m_bDisableCtrlAtlDel(bDisableCtrlAtlDel)
	, m_bDisableKeyboard(bDisableKeyboard)
	, m_nEnableKey(nEnableKey)
	, m_bMonitorKeyboard(bMonitorKeyboard)
	, m_bLeftCtrl(FALSE)
	, m_bRightCtrl(FALSE)
	, m_bLeftAtl(FALSE)
	, m_bRightAtl(FALSE)
	, m_bHookDel(FALSE)
	, m_bHookRightDel(FALSE)
{
}

KeyBoardDevice::~KeyBoardDevice(void)
{
}

//https://wenku.baidu.com/view/3c27ee681eb91a37f1115c6f.html
//因为键盘的read irp 是异步工作的，所以不能等待， 直接设置回调读取结果。不然系统会被挂起。
NTSTATUS KeyBoardDevice::Read(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	if (m_bMonitorKeyboard || m_bDisableCtrlAtlDel || m_bDisableKeyboard || m_bHookDel || m_bHookRightDel) {
		//需要禁止组合键或者有组合键没有按起来
		NTSTATUS status = STATUS_SUCCESS;
		if (Irp->CurrentLocation == 1) {
			ULONG ReturnedInformation = 0;
			status = STATUS_INVALID_DEVICE_REQUEST;
			Irp->IoStatus.Status = status;
			Irp->IoStatus.Information = ReturnedInformation;
			IoCompleteRequest(Irp, IO_NO_INCREMENT);
			return(status);
		}
		return  ForwardIrpAsynchronous(Irp);
	}
	else {
		return CWDMDeviceFlt::SCSI(DeviceObject, Irp);
	}
}

//键盘扫描码 https://blog.csdn.net/qq_37232329/article/details/79926440
NTSTATUS KeyBoardDevice::OnAsynIRPComplete(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);
	auto IrpSp = IoGetCurrentIrpStackLocation(Irp);
	UNREFERENCED_PARAMETER(IrpSp);
	//  如果这个请求是成功的。很显然，如果请求失败了，这么获取
	//   进一步的信息是没意义的。
	if (NT_SUCCESS(Irp->IoStatus.Status)) {
		// 获得读请求完成后输出的缓冲区
		auto buf = (PKEYBOARD_INPUT_DATA)Irp->AssociatedIrp.SystemBuffer;
		// 获得这个缓冲区的长度。一般的说返回值有多长都保存在
		// Information中。
		auto buf_len = Irp->IoStatus.Information / sizeof(KEYBOARD_INPUT_DATA);
		//… 这里可以做进一步的处理。我这里很简单的打印出所有的扫
		// 描码。
		for (size_t i = 0; i < buf_len; ++i) {
			//https://wenku.baidu.com/view/3c27ee681eb91a37f1115c6f.html
			USHORT KeyCode(0);
			switch (buf[i].Flags) {
			case KEY_MAKE:		//等于扫描码， 按下
			case KEY_BREAK:		//等于扫描码，松开
				KeyCode = buf[i].MakeCode;
				break;
			case KEY_E0:		//扫描码的低字节， 按下
			case 3:				//扫描码的低字节， 松开
				KeyCode = 0xE000 + buf[i].MakeCode;
				break;
			case KEY_E1:		//扫描码的低字节， 按下
			case 5:				//扫描码的低字节， 松开
				KeyCode = 0xE100 + buf[i].MakeCode;
				break;
			}
			//https://baike.baidu.com/item/%E9%94%AE%E7%9B%98%E6%89%AB%E6%8F%8F%E7%A0%81/7360710?fr=aladdin
			switch (KeyCode) {
			case 0x001D:
				m_bLeftCtrl = (buf[i].Flags == KEY_MAKE) ? TRUE : FALSE;
				break;
			case 0xE01D:
				m_bRightCtrl = (buf[i].Flags == KEY_E0) ? TRUE : FALSE;
				break;
			case 0x0038:
				m_bLeftAtl = (buf[i].Flags == KEY_MAKE) ? TRUE : FALSE;
				break;
			case 0xE038:
				m_bRightAtl = (buf[i].Flags == KEY_E0) ? TRUE : FALSE;
				break;
			}

			if (m_bDisableKeyboard) {
				auto nOldMakeCode = buf[i].MakeCode;
				buf[i].MakeCode = 0;
				for (auto j = 0; j < MAX_ENABLE_KEYBOARD_KEY_COUNT; j++) {
					if (m_nEnableKey[j]) {
						if (nOldMakeCode == m_nEnableKey[j] || nOldMakeCode == m_nEnableKey[j] + 0x80) {
							//排除
							buf[i].MakeCode = nOldMakeCode;
							break;
						}
					}
					else {
						break;
					}
				}
			}
			else if (m_bDisableCtrlAtlDel) {
				//hook ctrl + atl + del
				if ((m_bLeftCtrl || m_bRightCtrl) && (m_bLeftAtl || m_bRightAtl)
					&& KeyCode == 0xE053 && buf[i].Flags == KEY_E0) {
					buf[i].MakeCode = 0;
					m_bHookDel = TRUE;
				}
				else if ((m_bLeftCtrl || m_bRightCtrl) && (m_bLeftAtl || m_bRightAtl)
					&& KeyCode == 0x0053 && buf[i].Flags == KEY_MAKE) {
					buf[i].MakeCode = 0;
					m_bHookRightDel = TRUE;
				}
			}
			if (m_bHookDel && KeyCode == 0xE053 && buf[i].Flags == 3) {
				buf[i].MakeCode = 0;
				m_bHookDel = FALSE;
			}
			else if (m_bHookRightDel && KeyCode == 0x0053 && buf[i].Flags == KEY_BREAK) {
				buf[i].MakeCode = 0;
				m_bHookRightDel = FALSE;
			}
		}
	}
	if (Irp->PendingReturned) {
		IoMarkIrpPending(Irp);
	}
	return Irp->IoStatus.Status;
}

UsbPrinterDevice::UsbPrinterDevice(CWDMDriver* pWDMDriver, BOOLEAN bUnknowPrinter)
	: CWDMDeviceFlt(pWDMDriver)
	, m_bUnknowPrinter(bUnknowPrinter)
{
}

UsbPrinterDevice::~UsbPrinterDevice(void)
{
}