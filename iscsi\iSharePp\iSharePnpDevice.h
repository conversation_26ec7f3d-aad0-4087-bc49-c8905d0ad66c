﻿#pragma once
#include "mbr.h"
#include "../iShareDriverLib/DeviceSystem.h"
#include "../iShareDriverLib/wdmdeviceflt.h"

//卷过滤，为了修正盘符和禁止访问usb磁盘
enum usb_mode { USBMODE_ALLOW, USBMODE_DENYALL, USBMODE_DENYWRITE };
enum { WDMDEVICE_NET = WDMDEVICE_END_, WDMDEVICE_DISK, WDMDEVICE_VOLUME, WDMDEVICE_KEYBOARD, WDMDEVICE_USBPRINTER };
class iSharePnpDevice : public CWDMDevice
{
public:
	explicit iSharePnpDevice(CWDMDriver* pWDMDriver, PCWSTR pName);
	virtual ~iSharePnpDevice(void);
	virtual NTSTATUS DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual NTSTATUS Shutdown(PDEVICE_OBJECT DeviceObject, PIRP Irp);
};

//只拦截查询移除请求,并拒绝.避免网卡被卸载
class NetDevice : public CWDMDeviceFlt
{
public:
	explicit NetDevice(CWDMDriver* pWDMDriver);
	virtual ~NetDevice(void);
	virtual NTSTATUS PnP(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual BOOLEAN IsFilterDevice() { return WDMDEVICE_NET; };
};

//只拦截MBR的读取,并把激活分区撤销
class DiskDevice : public CWDMDeviceFlt
{
public:
	explicit DiskDevice(CWDMDriver* pWDMDriver, BOOLEAN bUsbStorage, const DWORD& bDenyWriteUsbDisk, const DWORD& bClearSerialNumber);
	virtual ~DiskDevice(void);
	// 	virtual VOID OnTargetDeviceAdded();
	// 	virtual VOID OnTargetDeviceBeforeRemove(PDEVICE_OBJECT DeviceObject);//当删除磁盘时调用,适用于U盘
	virtual NTSTATUS SCSI(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual NTSTATUS OnAsynIRPComplete(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp);
	// 	virtual NTSTATUS Read(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	//  virtual NTSTATUS Write(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual BOOLEAN IsFilterDevice() { return WDMDEVICE_DISK; };
	virtual NTSTATUS OnAsynIRPComplete1(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp);
	virtual NTSTATUS DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);

protected:
	// 	static ULONG   g_dwDiskMark;		//磁盘签名
	// 	ULONG   m_dwDiskMark;		//磁盘签名
	BOOLEAN m_bUsbStorage = FALSE;
	const DWORD& m_bDenyWriteUsbDisk;		//是否禁止写入USB 0 - 允许 1 - 禁止读写， 2 - 禁止写
	const DWORD& m_bClearSerialNumber;		//0 - 不清除序列号 1 - 清除序列号
// 	CDeviceSystem m_TargetDeviceSystem;
};

class VolumeDevice : public CWDMDeviceFlt
{
public:
	explicit VolumeDevice(CWDMDriver* pWDMDriver, const DWORD& bDenyWriteUsbDisk, const NIC_PARA& NicPara, const CHAR& cDiskLetterLocalFirst, const CHAR& cDiskLetterUsbLast);
	virtual ~VolumeDevice(void);
	virtual VOID OnTargetDeviceAdded();//当添加目标设备是调用
	virtual VOID OnTargetDeviceBeforeRemove(PDEVICE_OBJECT DeviceObject);//当删除磁盘时调用,适用于U盘
	virtual NTSTATUS DeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);//转发磁盘IO,并拦截磁盘上线IRP
	static NTSTATUS QueryProductName(CDeviceSystem& TargetDeviceSystem, PCStringW sProductName, PBOOLEAN bLocalDisk, PBOOLEAN bUsbDisk);
	virtual NTSTATUS Read(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	// 	virtual NTSTATUS Write(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	//	virtual NTSTATUS SCSI(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual BOOLEAN IsFilterDevice() { return WDMDEVICE_VOLUME; };
	FORCEINLINE BOOLEAN GetUsbDisk() { return m_bUsbDisk; };//是否是usb
	FORCEINLINE CHAR GetDiskLetterCur() { return m_cDiskLetterCur; };
	FORCEINLINE VOID SetDiskLetterCur(CHAR cDiskLetterCur) { m_cDiskLetterCur = cDiskLetterCur; };
	FORCEINLINE CStringW GetRealDiskName() { return m_sDiskName; };
	FORCEINLINE VOID ReCheckDiskLetter() {
		QueryLetter();
		ReadDriverLetterSet();
		CheckDriverLetter();
		return;
	};
	VOID OnVolumeOnline();

protected:
	VOID QueryName();//查询磁盘号
	VOID QueryProduct();//查询磁盘标签名
	VOID QueryLetter();
	VOID ReadDriverLetterSet();
	NTSTATUS ReadDriverLetter();
	VOID CheckDriverLetter();
	VOID MountNextDriverLetter();	//自动装载下一个盘符
	NTSTATUS Unmount(CHAR dosletter);
	NTSTATUS Mount(CStringW sDeviceName, CHAR dosletter);
	inline BOOLEAN NeedHideDisk() {
		return (HIDN_DISK_LETTER1 == m_cDiskLetterSet
			|| (HIDN_DISK_LETTER2 == m_cDiskLetterSet && BOOT_IBFT == m_NicPara.m_nBootMode)
			|| (HIDN_DISK_LETTER3 == m_cDiskLetterSet && BOOT_GRUB4DOS == m_NicPara.m_nBootMode)) ? TRUE : FALSE;
	}

	BOOLEAN m_bLocalDisk;				//是否是本地磁盘
	BOOLEAN m_bUsbDisk;					//是否是usb
	CHAR m_cDiskLetterCur;				//当前磁盘号 0 - 未读取 0xFF - 无盘符 A-Z 正常盘符
	CHAR m_cDiskLetterSet;				//当前需要设置的磁盘号 0 - 未读取 0xFF - 无盘符 0xFE - 无盘启动时无盘符 0xFD - 本地启动时无盘符 A-Z 正常盘符
	const CHAR& m_cDiskLetterLocalFirst;  //本地磁盘自动盘符
	const CHAR& m_cDiskLetterUsbLast;		//USB自动盘符
	const DWORD& m_bDenyWriteUsbDisk;		//是否禁止写入USB 0 - 允许 1 - 禁止读写， 2 - 禁止写
	CDeviceSystem m_TargetDeviceSystem;
	CStringW m_sDiskName;				//磁盘名，形如 \Device\DiskVolume1
	CStringW m_sProductName;
	const NIC_PARA& m_NicPara;
};

class KeyBoardDevice : public CWDMDeviceFlt
{
public:
	explicit KeyBoardDevice(CWDMDriver* pWDMDriver, const BOOLEAN& bDisableCtrlAtlDel,
		const BOOLEAN& bDisableKeyboard, const UINT* nEnableKey, const BOOLEAN& bMonitorKeyboard);
	virtual ~KeyBoardDevice(void);
	virtual NTSTATUS Read(PDEVICE_OBJECT DeviceObject, PIRP Irp);
	virtual NTSTATUS OnAsynIRPComplete(IN PDEVICE_OBJECT DeviceObject, IN PIRP Irp);
	virtual BOOLEAN IsFilterDevice() { return WDMDEVICE_KEYBOARD; };
protected:
	const BOOLEAN& m_bDisableCtrlAtlDel;	//禁止锁屏
	const BOOLEAN& m_bDisableKeyboard;	//禁止键盘
	const BOOLEAN& m_bMonitorKeyboard; //监控键盘
	const UINT* m_nEnableKey;
	BOOLEAN m_bLeftCtrl;
	BOOLEAN m_bRightCtrl;
	BOOLEAN m_bLeftAtl;
	BOOLEAN m_bRightAtl;
	BOOLEAN m_bHookDel;
	BOOLEAN m_bHookRightDel;
};

//https://yhsnlkm.github.io/2019/12/12/USB%E7%9B%B8%E5%85%B3/%E5%86%85%E6%A0%B8%E9%A9%B1%E5%8A%A8%E4%B8%AD%E6%8B%A6%E6%88%AAUSB%E8%AE%BE%E5%A4%87%E6%8E%A5%E5%85%A5-1/
//https://yhsnlkm.github.io/2019/08/14/USB%E7%9B%B8%E5%85%B3/%E5%BA%94%E7%94%A8%E5%B1%82%E9%81%8D%E5%8E%86%E6%89%80%E6%9C%89%E6%8E%A5%E5%85%A5%E7%9A%84USB%E8%AE%BE%E5%A4%87-1/
class UsbPrinterDevice : public CWDMDeviceFlt
{
public:
	explicit UsbPrinterDevice(CWDMDriver* pWDMDriver, BOOLEAN bUnknowPrinter);
	virtual ~UsbPrinterDevice(void);
	virtual BOOLEAN IsFilterDevice() { return WDMDEVICE_USBPRINTER; };
	FORCEINLINE BOOLEAN IsUnknowPrinter() { return m_bUnknowPrinter; };
protected:
	BOOLEAN m_bUnknowPrinter = FALSE;
};