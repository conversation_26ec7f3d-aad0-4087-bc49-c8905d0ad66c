﻿#include "stdafx.h"
#include <Dbt.h>
#include "../iShareDll/OEM.h"
#if defined(__x86_64__) || defined(_M_X64) || defined(__i386__) || defined(_M_IX86)
#if defined(USE_GRUB2_BOOT_LOADER)
#include "loader/grub2.zip.h"
#else
#include "loader/grub4dos.zip.h"
#include "loader/grub4dosoem.zip.h"
#include "loader/win7loader.zip.h"
#endif
#endif
#include "../iShareDll/OEMFunc.h"
#include "../iShareDll/UsbBootISO.h"
#include "../iShareDll/AdapterInfo.h"
#include "../iShareDll/VHDInstall.h"
#include "../iShareDll/VHDBootMng.h"
#include "../iShareDll/FormatDisk.h"
#include "resource.h"
#include "iShareISO.h"
#include "Wizards.h"
#include "PageDownload.h"
#include "PageTask.h"
#include "PageIP.h"
#include "PageSet.h"

//https://blog.csdn.net/liangyuannao/article/details/7905368
static PVOID g_hNotifyDevNodeSet = NULL;  //设备变化通知句柄
static bool m_SetInstallFinished = false; //是否完成
static std::string g_sDHCPIP = "";   //DHCP IP
static std::vector<boost::filesystem::path> g_systemloaders; //外挂的引导列表补丁

void EnableKeepModeControls(HWND hWnd, BOOL bEnable)
{
	EnableWindow(GetDlgItem(hWnd, IDC_COMBO_VOLUME), bEnable);
	EnableWindow(GetDlgItem(hWnd, IDC_COMBO_IMAGE), bEnable);
}

void EnableSetControls(HWND hdlg, BOOL bEnable)
{
	EnableWindow(GetDlgItem(hdlg, IDC_IPADDRESS_SERVER), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_IPADDRESS_SERVER2), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_EDIT_NAME), bEnable);
	if (!IsOEMICloud()) {
		EnableWindow(GetDlgItem(hdlg, IDC_CHECK_HIDE), bEnable);
	}
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_KEEP), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_DHCP), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_SETIPONLY), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_COMBO_SYSTEM), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_LIST_LOADER), bEnable);
	EnableKeepModeControls(hdlg, (bEnable && (BST_CHECKED != SendDlgItemMessage(hdlg, IDC_CHECK_KEEP, BM_GETCHECK, 0, 0))) ? TRUE : FALSE);
	EnableIPControls(hdlg, (bEnable && (BST_CHECKED != SendDlgItemMessage(hdlg, IDC_CHECK_DHCP, BM_GETCHECK, 0, 0))) ? TRUE : FALSE);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_WINPE), bEnable);
	PostMessage(g_hwndPropSheet, PSM_SETWIZBUTTONS, 0, bEnable ? PSWIZB_NEXT : 0);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_TESTSIGNING), bEnable);
	if (SendMessage(GetDlgItem(hdlg, IDC_COMBO_MAC), CB_GETCOUNT, 0, 0) >= 2) {
		EnableWindow(GetDlgItem(hdlg, IDC_COMBO_MAC), bEnable);
	}
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_TEXTMENU), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_HIDEMENU), bEnable);
}

bool MakeInstallVHDList(int dwCurSelImage, std::vector<INSTALL_VHD_FILE>& lVhdFiles)
{
	if (0 == dwCurSelImage) {
		//安装全部
		g_pUploadClient->GetInstallVHDPara(g_pBootVhdPath, lVhdFiles);
	}
	else if (CB_ERR != dwCurSelImage && dwCurSelImage - 1 < g_pBootVhdPath.size()) {
		//安装一个
		std::vector<boost::filesystem::path> pBootVhdPath;
		pBootVhdPath.push_back(g_pBootVhdPath[dwCurSelImage - 1]);
		g_pUploadClient->GetInstallVHDPara(pBootVhdPath, lVhdFiles);
	}
	return !lVhdFiles.empty();
}

void InstallGrubDisk(HWND hdlg, boost::filesystem::path dstBootFolder, ULONG uDiskNumber, std::wstring dstVolume)
{
	std::vector<INSTALL_VHD_FILE> lVhdFiles;
	auto dwCurSelImage = SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_GETCURSEL, 0, 0);
	auto ret = MakeInstallVHDList(dwCurSelImage, lVhdFiles);
	if (ret) {
		DWORD dwInstallError(0);
		auto bWinPETools = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_WINPE, BM_GETCHECK, 0, 0)) ? true : false;
		auto bWindows7Only = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_COMBO_SYSTEM, BM_GETCHECK, 0, 0)) ? true : false;
		std::vector<boost::filesystem::path> lSystemLoaders;
		auto nCount = SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_GETSELCOUNT, 0, 0);
		if (nCount > 0) {
			auto pSelItems = std::make_unique<int[]>(nCount);
			SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_GETSELITEMS, nCount, (LPARAM)pSelItems.get());
			for (auto i = 0; i < nCount; i++) {
				lSystemLoaders.push_back(g_systemloaders[pSelItems[i]]);
			}
		}
		auto InstallPara = std::make_shared<INSTALL_VHD_PARA>();
		if (!InstallPara) return;
#if defined(USE_GRUB2_BOOT_LOADER)
		InstallPara->idResource = grub2_zip;
		InstallPara->idResourceLen = sizeof(grub2_zip);
		InstallPara->idResourceOEM = nullptr;
		InstallPara->idResourceOEMLen = 0;
#else
		InstallPara->idResource = grub4dos_zip;
		InstallPara->idResourceLen = sizeof(grub4dos_zip);
		if (IsPXEHavenotLogo()) {
			InstallPara->idResourceOEM = grub4dosoem_zip;
			InstallPara->idResourceOEMLen = sizeof(grub4dosoem_zip);
		}
		else {
			InstallPara->idResourceOEM = nullptr;
			InstallPara->idResourceOEMLen = 0;
		}
#endif
		InstallPara->idResourceWin7loader = bWindows7Only ? win7loader_zip : nullptr;
		InstallPara->idResourceWin7loaderLen = bWindows7Only ? sizeof(win7loader_zip) : 0;

		InstallPara->uDiskNumber = uDiskNumber;
		InstallPara->dstVolume = dstVolume;
		//InstallPara->bOperationSystem = lSystemLoaders;
		InstallPara->bKeepMode = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_KEEP, BM_GETCHECK, 0, 0));
		InstallPara->bDisklessMode = false;
		InstallPara->dwErrorBit = &dwInstallError;
		InstallPara->bUpdateOnly = false;
		//InstallPara->bWinPE = bWinPETools;
		if (IsVHDBootLogoMenuHide()) {
			//隐藏菜单
			InstallPara->bHideMenu = true;
		}
		else {
			InstallPara->bHideMenu = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_HIDEMENU, BM_GETCHECK, 0, 0));
		}
		InstallPara->bTestSigning = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_TESTSIGNING, BM_GETCHECK, 0, 0));
		InstallPara->bTextMenu = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_TEXTMENU, BM_GETCHECK, 0, 0));
		InstallPara->lVhdFiles = lVhdFiles;
		ret = InstallVHDbootDiskFat32(InstallPara.get());
		if (ret) {
			auto bHideVolume = IsOEMICloud() ? true : (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_HIDE, BM_GETCHECK, 0, 0));
			HideDisk(hdlg, dstVolume[0], bHideVolume);
			CVHDBootMng::HideVHDBootVolume_MBR(dstVolume.c_str(), bHideVolume);
			auto dstBootFolder = boost::filesystem::absolute(VHD_BOOT_FOLDER, dstVolume);//安装分区
			auto ipxefile = boost::filesystem::absolute(IP_IPXE_FILE_NAME, dstBootFolder);
			if (IsFile(ipxefile)) {
				Copy_File(ipxefile, boost::filesystem::absolute(IP_IPXE_FILE_NAME, InstallPara->sBootVolume), true);
			}
		}
		switch (dwInstallError)
		{
		case VHDINSTALLERROR_UNZIPIMAGETOOLS:
			MessageBox(hdlg, LoadStringFromID(IDS_UNZIP_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_INSTALLGRUBMBR:
			MessageBox(hdlg, LoadStringFromID(IDS_INSTALLMBR_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_KEEPMODE:
			MessageBox(hdlg, LoadStringFromID(IDS_KEEP_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_COMPATIBLEMODE:
			MessageBox(hdlg, LoadStringFromID(IDS_COMPATIBLE_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_XPVBOOTMODE:
			MessageBox(hdlg, LoadStringFromID(IDS_XPVBOOT_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_COPYMENU:
			MessageBox(hdlg, LoadStringFromID(IDS_COPYMENU_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_COPYMESSAGE:
			MessageBox(hdlg, LoadStringFromID(IDS_COPYMESSAGE_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_COPYBOOTMGR:
			MessageBox(hdlg, LoadStringFromID(IDS_COPYBOOT_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_COPYVBOOT:
			MessageBox(hdlg, LoadStringFromID(IDS_COPYVBOOT_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_CREATECHILDVHD:
			MessageBox(hdlg, LoadStringFromID(IDS_VHDCHILD_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		case VHDINSTALLERROR_CREATESUPERVHD:
			MessageBox(hdlg, LoadStringFromID(IDS_SUPERVHD_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
			break;
		default:
		{
			if (ret) {
				auto clickbutton = MessageBox(hdlg, LoadStringFromID(MSG_INSTALLGRUB_OK).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_YESNO);
				if (IDYES == clickbutton) {
					PostMessage(g_hwndPropSheet, WM_QUIT, 0, 0);
				}
			}
			else {
				MessageBox(hdlg, LoadStringFromID(MSG_INSTALLGRUB_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_ICONERROR);
			}
		}
		//MessageBox(hdlg, LoadStringFromID(ret ? MSG_INSTALLGRUB_OK : MSG_INSTALLGRUB_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
		break;
		}
	}
	else {
		MessageBox(hdlg, LoadStringFromID(IDS_VHDFILE_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
	}
	EnableSetControls(hdlg, TRUE);
	if (ret) {
		m_SetInstallFinished = true; //是否完成
		SendMessage(g_hwndPropSheet, PSM_PRESSBUTTON, PSBTN_NEXT, 0);
	}
}

void RefreshImageCombo(HWND hdlg)
{
	SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_RESETCONTENT, 0, 0);
	if (!g_pBootVhdPath.empty()) {
		SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_ADDSTRING, 0, (LPARAM)LoadStringFromID(IDS_ALL).c_str());
		for (auto iter = g_pBootVhdPath.begin(); iter != g_pBootVhdPath.end(); iter++) {
			SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_ADDSTRING, 0, (LPARAM)(*iter).filename().c_str());
		}
		SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_SETCURSEL, 0, 0);
	}
}

INT_PTR CALLBACK SetDlgProc(HWND hdlg,
	UINT uMessage,
	WPARAM wParam,
	LPARAM lParam)
{
	LPNMHDR     lpnmhdr;
	switch (uMessage)
	{
	case WM_INITDIALOG:
	{
		if (!g_pUploadClient) {
			g_pUploadClient = boost::make_shared<CUploadClient>();
		}
		g_systemloaders.clear();
		SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_RESETCONTENT, 0, 0);
		Find_Directory(boost::filesystem::absolute(_T("linuxloader"), GetModuleFolder()), [&hdlg](const boost::filesystem::path& findfile)->bool {
			if (findfile.extension().compare(_T(".zip")) == 0) {
				g_systemloaders.push_back(findfile);
				SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_ADDSTRING, 0, (LPARAM)findfile.stem().c_str());
			}
			return true;
			});
		auto nCount = SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_GETCOUNT, 0, 0);
		if (nCount > 0) {
			// 遍历所有的条目
			for (int i = 0; i < nCount; i++) {
				// 设置条目为被选择状态
				SendDlgItemMessage(hdlg, IDC_LIST_LOADER, LB_SETSEL, TRUE, i);
			}
		}

		InitVolumeComboList(hdlg, IDC_COMBO_VOLUME, m_lVolumeList);
		auto funLoadDiskAndIP = [](const VOLUME_CAPACITY_LIST::value_type& value, bool bRefreshVhdPath) {
			auto pBootFolder = boost::filesystem::absolute(VHD_BOOT_FOLDER, value.get<0>());
			auto ret1 = g_pUploadClient->LoadDiskList(pBootFolder);
			auto ret2 = IsUsbBootISO(boost::filesystem::absolute(IP_IPXE_FILE_NAME, pBootFolder));
			//先在磁盘下寻找
			if (bRefreshVhdPath) {
				g_pBootVhdPath = CVHDBootMng::FindVHDImage(pBootFolder);
			}
			if ((ret1 || ret2) && g_ptrlogsystem) {
				g_ptrlogsystem->RestartLog(pBootFolder);
			}
			return ret1 || ret2;
			};
		DWORD dwCurSel(0);//列表序列号
		if (m_DownloadSuccessTimes && CB_ERR != g_dwVolumeCurSel && g_dwVolumeCurSel < m_lVolumeList.size() && !g_pBootVhdPath.empty()) {
			dwCurSel = g_dwVolumeCurSel;
			funLoadDiskAndIP(m_lVolumeList[g_dwVolumeCurSel], false);
		}
		else {
			DWORD dwIndex(0);
			for (auto iter = m_lVolumeList.begin(); iter != m_lVolumeList.end(); ++iter, ++dwIndex) {
				if (funLoadDiskAndIP(*iter, true)) {
					dwCurSel = dwIndex;
					break;
				}
			}
		}
		auto  bKeepMode = IsKeepMode(boost::filesystem::absolute(VHD_BOOT_FOLDER, m_lVolumeList[dwCurSel].get<0>()));
		SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_SETCURSEL, dwCurSel, 0);
		if (IsOEMICloud()) {
			SendDlgItemMessage(hdlg, IDC_CHECK_HIDE, BM_SETCHECK, BST_CHECKED, 0);
			EnableWindow(GetDlgItem(hdlg, IDC_CHECK_HIDE), FALSE);
		}
		else {
			SendDlgItemMessage(hdlg, IDC_CHECK_HIDE, BM_SETCHECK, DiskHided(m_lVolumeList[dwCurSel].get<0>()[0]) ? BST_CHECKED : BST_UNCHECKED, 0);
		}
		if (IsOEMOpenDriverTestMode()) {
			::ShowWindow(GetDlgItem(hdlg, IDC_CHECK_TESTSIGNING), SW_SHOW); //显示控件，可见
			SendDlgItemMessage(hdlg, IDC_CHECK_TESTSIGNING, BM_SETCHECK, BST_CHECKED, 0);
		}
		SendDlgItemMessage(hdlg, IDC_CHECK_KEEP, BM_SETCHECK, bKeepMode ? BST_CHECKED : BST_UNCHECKED, 0);
		RefreshImageCombo(hdlg);
		EnableKeepModeControls(hdlg, !bKeepMode);
		if (!FindSrcWimFilePath(true).empty()) {
			SendDlgItemMessage(hdlg, IDC_CHECK_WINPE, BM_SETCHECK, BST_CHECKED, 0);
		}

		//ip信息
		CAdapterInfo adapterinfo;
		adapterinfo.Init();
		std::vector<std::wstring> lMacList;
		adapterinfo.ForEach([&lMacList](const AdapterNetwork* iter) {
			auto sMac = iter->GetMacAddress(TRUE, TRUE);
			if (!sMac.empty()) {
				lMacList.push_back(sMac);
			}
			});
		SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_RESETCONTENT, 0, 0);
		SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_ADDSTRING, 0, (LPARAM)LoadStringFromID(IDS_AUTO).c_str());
		std::for_each(lMacList.begin(), lMacList.end(), [&hdlg](auto iter) {
			SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_ADDSTRING, 0, (LPARAM)iter.c_str());
			});
		SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_SETCURSEL, 0, 0);
		EnableWindow(GetDlgItem(hdlg, IDC_COMBO_MAC), lMacList.size() >= 2);
		CUsbBootISO pUsbBootISO;
		if (pUsbBootISO.GetComputers().size() > 0) {
			auto iter = pUsbBootISO.GetComputers().begin();
			//设置界面
			if ((*iter).m_siScsiIP.size() > 0) {
				SetDlgItemText(hdlg, IDC_IPADDRESS_SERVER, s2ws((*iter).m_siScsiIP).c_str()); //服务器IP
			}
			else {
				if (pUsbBootISO.GetServers().size() > 0)
					SetDlgItemText(hdlg, IDC_IPADDRESS_SERVER, s2ws(pUsbBootISO.GetServers()[0]).c_str()); //服务器IP
				if (pUsbBootISO.GetServers().size() > 1)
					SetDlgItemText(hdlg, IDC_IPADDRESS_SERVER2, s2ws(pUsbBootISO.GetServers()[1]).c_str()); //服务器IP
			}
			SetDlgItemText(hdlg, IDC_EDIT_NAME, s2ws((*iter).m_sName).c_str()); //客户机名字
			SetDlgItemText(hdlg, IDC_IPADDRESS_IP, s2ws((*iter).m_sIP).c_str()); //客户机IP
			SetDlgItemText(hdlg, IDC_IPADDRESS_MASK, s2ws((*iter).m_sMask).c_str()); //客户机掩码
			SetDlgItemText(hdlg, IDC_IPADDRESS_GATEWAY, s2ws((*iter).m_sGateway).c_str()); //客户机网关
			SetDlgItemText(hdlg, IDC_IPADDRESS_DNS1, s2ws((*iter).m_sDns).c_str()); //客户机DNS
			SetDlgItemText(hdlg, IDC_IPADDRESS_DNS2, s2ws((*iter).m_sDns2).c_str()); //客户机DNS2
			SendDlgItemMessage(hdlg, IDC_CHECK_DHCP, BM_SETCHECK, ((*iter).IsLocalDHCP()) ? BST_CHECKED : BST_UNCHECKED, 0);
			EnableIPControls(hdlg, ((*iter).IsLocalDHCP()) ? FALSE : TRUE);
			g_sDHCPIP = (*iter).IsLocalDHCP() ? (*iter).m_sDHCP : "";//保存临时变量

			if (lMacList.size() > 1) {
				for (int i = 0; i < lMacList.size(); i++) {
					if (wstring2icmp(lMacList[i], s2ws((*iter).m_sMAC)) == 0) {
						SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_SETCURSEL, i + 1, 0);
						break;
					}
				}
			}
		}
		else {
			SetDlgItemText(hdlg, IDC_IPADDRESS_SERVER, g_sServerIP.c_str()); //服务器IP
			SetDlgItemText(hdlg, IDC_EDIT_NAME, _T("PC")); //客户机名字
			if (adapterinfo.AdapterCount() > 0) {
				auto pAdapterNetwork = adapterinfo.GetAdapter(0);
				if (pAdapterNetwork) {
					SetDlgItemText(hdlg, IDC_IPADDRESS_IP, s2ws(pAdapterNetwork->IpAddress.String).c_str()); //客户机IP
					SetDlgItemText(hdlg, IDC_IPADDRESS_MASK, s2ws(pAdapterNetwork->IpMask.String).c_str()); //客户机掩码
					SetDlgItemText(hdlg, IDC_IPADDRESS_GATEWAY, s2ws(pAdapterNetwork->GatewayAddress.String).c_str()); //客户机网关
					SetDlgItemText(hdlg, IDC_IPADDRESS_DNS1, s2ws(pAdapterNetwork->DNSAddress1.String).c_str()); //客户机DNS
					SetDlgItemText(hdlg, IDC_IPADDRESS_DNS2, s2ws(pAdapterNetwork->DNSAddress2.String).c_str()); //客户机DNS2
					if (lMacList.size() > 1 && !pAdapterNetwork->GetMacAddress(TRUE, TRUE).empty()) {
						SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_SETCURSEL, 1, 0);
					}
				}
			}
		}
		RegisterForDevChange(hdlg, &g_hNotifyDevNodeSet);
		TranlateCWnd(hdlg);
		EnumChildWindows(hdlg, EnumChildProc, (LPARAM)hdlg);
	}
	break;
	// on any command notification, tell the property sheet to enable the Apply button
	case WM_COMMAND:
	{
		PropSheet_Changed(GetParent(hdlg), hdlg);
		int wmId = LOWORD(wParam);
		int wmEvent = HIWORD(wParam);
		if (IDC_COMBO_VOLUME == wmId) {
			//改变了选择的磁盘 http://wenku.baidu.com/link?url=Kb_qKiH_DvsbqcOBl4a2aHySMhF_gY2cdwDGwJyEF9GPWyRPVlsA7o1HwX5Scd-CLaQjWM2hGQgI19QvXzCDZ1z39h_IcTxhjFUhrvl7Zz7
			if (CBN_SELCHANGE == wmEvent) {
				auto dwCurSel = SendMessage((HWND)lParam, CB_GETCURSEL, (WORD)0, 0L);
				if (dwCurSel < m_lVolumeList.size()) {
					//先在磁盘下寻找
					auto pBootVhdFolder = boost::filesystem::absolute(VHD_BOOT_FOLDER, m_lVolumeList[dwCurSel].get<0>());
					auto ret3 = g_pUploadClient->LoadDiskList(pBootVhdFolder);
					if (ret3 && g_ptrlogsystem) {
						g_ptrlogsystem->RestartLog(pBootVhdFolder);
					}
					g_pBootVhdPath = CVHDBootMng::FindVHDImage(pBootVhdFolder);
					RefreshImageCombo(hdlg);
					if (IsOEMICloud()) {
						SendDlgItemMessage(hdlg, IDC_CHECK_HIDE, BM_SETCHECK, BST_CHECKED, 0);
						EnableWindow(GetDlgItem(hdlg, IDC_CHECK_HIDE), FALSE);
					}
					else {
						SendDlgItemMessage(hdlg, IDC_CHECK_HIDE, BM_SETCHECK, DiskHided(m_lVolumeList[dwCurSel].get<0>()[0]) ? BST_CHECKED : BST_UNCHECKED, 0);
					}
					SendDlgItemMessage(hdlg, IDC_CHECK_KEEP, BM_SETCHECK, IsKeepMode(pBootVhdFolder) ? BST_CHECKED : BST_UNCHECKED, 0);
				}
			}
		}
		else if (IDC_CHECK_DHCP == wmId) {
			if (BN_CLICKED == wmEvent) {
				EnableIPControls(hdlg, (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_DHCP, BM_GETCHECK, 0, 0)) ? FALSE : TRUE);
			}
		}
		else if (IDC_CHECK_KEEP == wmId) {
			if (BN_CLICKED == wmEvent) {
				EnableKeepModeControls(hdlg, (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_KEEP, BM_GETCHECK, 0, 0)) ? FALSE : TRUE);
			}
		}
		else if (IDC_CHECK_WINPE == wmId) {
			if (BN_CLICKED == wmEvent) {
				if ((BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_WINPE, BM_GETCHECK, 0, 0))
					&& FindSrcWimFilePath(true).empty()) {
					SendDlgItemMessage(hdlg, IDC_CHECK_WINPE, BM_SETCHECK, BST_UNCHECKED, 0);
				}
			}
		}
	}
	break;

	case WM_NOTIFY:
		lpnmhdr = (NMHDR FAR*)lParam;

		switch (lpnmhdr->code)
		{
		case PSN_SETACTIVE:
			//this will be ignored if the property sheet is not a wizard
			PropSheet_SetWizButtons(GetParent(hdlg), PSWIZB_BACK | PSWIZB_NEXT);
			m_SetInstallFinished = false; //是否完成
			SendMessage(hdlg, WM_INITDIALOG, 0, 0);//重新初始化一次
			break;
			// 		case PSN_APPLY:   //sent when OK or Apply button pressed
			// 			break;
			// 		case PSN_RESET:   //sent when Cancel button pressed
			// 			break;
		case PSN_WIZBACK:
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, IDD_PAGETASK);
			return (IDD_PAGETASK);
			break;
		case PSN_WIZNEXT:
		{
			bool bGoToNextPage(false);
			if (!m_SetInstallFinished) {
				std::wstring dstVolume(_T("C:\\"));//目的磁盘
				auto dwCurSel = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETCURSEL, 0, 0);
				if (dwCurSel < m_lVolumeList.size()) {
					dstVolume = m_lVolumeList[dwCurSel].get<0>();
				}
				auto dstBootFolder = boost::filesystem::absolute(VHD_BOOT_FOLDER, dstVolume);//安装分区
				auto bInstalledVHDBoot = CVHDBootMng::IsInstalledVHDBoot(dstBootFolder);//是否已经安装vhd启动
				auto bKeepMode = IsKeepMode(dstBootFolder);//是否是保留模式
				auto clickbutton = MessageBox(hdlg, LoadStringFromID((!bInstalledVHDBoot || !bKeepMode) ? IDS_MAKELOCALBOOT : MSG_SETIP_QUEST).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_YESNOCANCEL);
				bGoToNextPage = (IDNO == clickbutton);
				if (IDYES == clickbutton) {
					CUsbBootISO pUsbBootISO;
					USBBOOTISO_COMPUTER sNewComputer;
					std::wstring szTemp;
					GetDlgItemText(hdlg, IDC_IPADDRESS_SERVER, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_siScsiIP = ws2s(szTemp);
					GetDlgItemText(hdlg, IDC_IPADDRESS_SERVER2, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					if (!szTemp.empty() && wstringicmp(szTemp, _T(BOOTISO_DHCP_ANY_IP)) != 0) {//有数据，多服务器
						pUsbBootISO.AddServer(sNewComputer.m_siScsiIP);
						sNewComputer.m_siScsiIP = "";
						pUsbBootISO.AddServer(ws2s(szTemp));
					}
					GetDlgItemText(hdlg, IDC_EDIT_NAME, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sName = string_trim(ws2s(szTemp));
					GetDlgItemText(hdlg, IDC_IPADDRESS_IP, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sIP = ws2s(szTemp);
					GetDlgItemText(hdlg, IDC_IPADDRESS_MASK, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sMask = ws2s(szTemp);
					GetDlgItemText(hdlg, IDC_IPADDRESS_GATEWAY, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sGateway = ws2s(szTemp);
					GetDlgItemText(hdlg, IDC_IPADDRESS_DNS1, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sDns = ws2s(szTemp);
					GetDlgItemText(hdlg, IDC_IPADDRESS_DNS2, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
					sNewComputer.m_sDns2 = ws2s(szTemp);
					if (SendDlgItemMessage(hdlg, IDC_COMBO_MAC, CB_GETCURSEL, 0, 0) > 0) {
						//有mac地址
						GetDlgItemText(hdlg, IDC_COMBO_MAC, tmpstr(szTemp, MAX_LOADSTRING), MAX_LOADSTRING);
						sNewComputer.m_sMAC = ws2s(szTemp);
					}
					sNewComputer.m_bDHCP = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_DHCP, BM_GETCHECK, 0, 0)) ? TRUE : FALSE;
					sNewComputer.m_sDHCP = g_sDHCPIP;

					pUsbBootISO.AddComputer(sNewComputer);

					UINT errormsg(0);
					ULONG uDiskNumber(0);
					if ((sNewComputer.m_siScsiIP.empty() || sNewComputer.m_siScsiIP == BOOTISO_DHCP_ANY_IP)
						&& pUsbBootISO.GetServers().size() == 0) {
						errormsg = IDS_SERVERIP_ERROR;
					}
					else if (sNewComputer.m_sName.empty()) {
						errormsg = IDS_NAME_ERROR2;
					}
					else if (sNewComputer.m_sName.size() > GetComputerNameMaxLength() || sNewComputer.m_sName.find_first_of(GetComputerNameErrorChar()) != std::string::npos) {
						errormsg = IDS_NAME_ERROR3;
					}
					else if (!sNewComputer.m_bDHCP && (sNewComputer.m_sIP.empty() || sNewComputer.m_sIP == BOOTISO_DHCP_ANY_IP)) {
						errormsg = IDS_IP_ERROR;
					}
					else if (!sNewComputer.m_bDHCP && (sNewComputer.m_sMask.empty() || sNewComputer.m_sMask == BOOTISO_DHCP_ANY_IP)) {
						errormsg = IDS_MASK_ERROR;
					}
					else if (IsWindowsDisk(dstVolume[0], &uDiskNumber)) {
						errormsg = IDS_MBR_ERROR;
					}

					if (errormsg) {
						MessageBox(hdlg, LoadStringFromID(errormsg).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_ICONERROR);
					}
					else {
						//设置IP
						auto ret = (MakeDirectory(dstBootFolder) \
							&& pUsbBootISO.SaveScript(boost::filesystem::absolute(IP_IPXE_FILE_NAME, dstBootFolder)));
						auto bSetIPOnly = (BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_SETIPONLY, BM_GETCHECK, 0, 0)) ? TRUE : FALSE;
						if (!bSetIPOnly && ret && (!bInstalledVHDBoot || !bKeepMode
							|| IDYES == MessageBox(hdlg, LoadStringFromID(IDS_OVERWRITE_QUEST).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_YESNOCANCEL))) {
							EnableSetControls(hdlg, FALSE);
							auto installthread = boost::thread(boost::bind(&InstallGrubDisk, hdlg, dstBootFolder, uDiskNumber, dstVolume));
						}
						else {
							MessageBox(hdlg, LoadStringFromID(ret ? MSG_SETIP_OK : MSG_SETIP_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), ret ? MB_OK : MB_ICONERROR);
							if (ret) {
								//设置成功前往下一页
								if (!bInstalledVHDBoot || !bKeepMode) {
									//更新所有的 child.vhd 文件
									auto dwCurSelImage = SendDlgItemMessage(hdlg, IDC_COMBO_IMAGE, CB_GETCURSEL, 0, 0);
									if (CB_ERR != dwCurSelImage) {
										auto installthread = boost::thread([dwCurSelImage, uDiskNumber, dstVolume]() {
											//启动分区，包含BCD
											auto sBootVolume = CVHDBootMng::FindBCDBootVolume(uDiskNumber, dstVolume, false);
											if (!sBootVolume.empty()) {
												//开始安装启动childvhd
												std::vector<INSTALL_VHD_FILE> lVhdFiles;
												if (MakeInstallVHDList(dwCurSelImage, lVhdFiles)) {
													InstallVHDbootFiles(sBootVolume, lVhdFiles);
												}
											}
											});
									}
								}
								bGoToNextPage = true;
							}
						}
					}
				}
			}
			else {
				bGoToNextPage = true;//上传完成
			}
			if (bGoToNextPage) {
				g_NextStepPage = PAGE_FORMAT;
				UnregisterDeviceNotification(g_hNotifyDevNodeSet);
			}
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
			return (bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
		}
		break;
		default:
			break;
		}
		break;

	case WM_DEVICECHANGE:
	{
		switch (wParam)
		{
		case DBT_DEVICEARRIVAL:
			// Handle device arrival
			SendMessage(hdlg, WM_INITDIALOG, 0, 0);//重新初始化一次
			break;

		case DBT_DEVICEQUERYREMOVE:
			// Handle device removal request
			break;

		case DBT_DEVICEREMOVECOMPLETE:
			// Handle device removal
			SendMessage(hdlg, WM_INITDIALOG, 0, 0);//重新初始化一次
			break;
		}
	}
	break;

	default:
		break;
	}

	return FALSE;
}