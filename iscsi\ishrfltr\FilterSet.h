﻿#pragma once
#include "../iShareDll/basevardef.h"
#include "../iShareDriverLib/SimpleBuffer.h"
#include "../iShareDriverLib/Path.h"
#include "../iShareDriverLib/IniFile.h"
#include "../iShareDriverLib/StringSystem.h"

// 注册表前缀严格匹配宏
#ifndef REGPATH_STARTWITH_EXACT
#define REGPATH_STARTWITH_EXACT(str, prefix) \
	((str).StartWith(prefix) && ((str).size() <= (_countof_const_string(prefix)) || (str).At((_countof_const_string(prefix))) == SLASH_WCHAR))
#endif

/**
 * @brief 检查两个路径是否匹配（childPath 是 parentPath 的子路径或完全匹配）
 * @param childPath 子路径
 * @param parentPath 父路径
 * @return TRUE 匹配，FALSE 不匹配
 */
BOOLEAN IsPathMatched(const CStringW& childPath, LPCWSTR parentPath, USHORT parentPathLen = 0);
/**
 * @brief 检查两个路径是否匹配（childPath 是 parentPath 的子路径或完全匹配）
 * @param childPath 子路径
 * @param parentPath 父路径
 * @return TRUE 匹配，FALSE 不匹配
 */
BOOLEAN IsPathMatched(const CStringW& childPath, const CStringW& parentPath);

/**
 * @brief CFilterSet 类用于管理过滤器集合。
 * @details 提供加载、检查和解析过滤器路径的功能。
 */
class CFilterSet
{
public:
	/**
	 * @brief 构造函数，初始化过滤器集合。
	 */
	CFilterSet(void);

	/**
	 * @brief 析构函数，释放资源。
	 */
	~CFilterSet(void);
	/**
	 * @brief 相等比较操作符。
	 * @param other 另一个 CFilterSet 对象。
	 * @return bool 如果两个过滤器集合相等返回 true，否则返回 false。
	 */
	bool operator==(const CFilterSet& other) const;

	bool IsChangedFileSet(const CFilterSet& other) const;

	bool IsChangedRegisterSet(const CFilterSet& other) const;

	/**
	 * @brief 不等比较操作符。
	 * @param other 另一个 CFilterSet 对象。
	 * @return bool 如果两个过滤器集合不相等返回 true，否则返回 false。
	 */
	bool operator!=(const CFilterSet& other) const;

	/**
	 * @brief 赋值操作符。
	 * @param other 另一个 CFilterSet 对象。
	 * @return CFilterSet& 当前对象的引用。
	 */
	CFilterSet& operator=(const CFilterSet& other);

	/**
	 * @brief 交换两个 CFilterSet 对象的内容。
	 * @param other 另一个 CFilterSet 对象。
	 */
	void swap(CFilterSet& other) noexcept;

	/**
	 * @brief 检查是否启用文件过滤。
	 * @return TRUE 启用，FALSE 未启用
	 */
	inline BOOLEAN IsEnableFilterFile() const {
		return m_bEnableFilterFile;
	};

	/**
	 * @brief 检查是否启用注册表过滤。
	 * @return TRUE 启用，FALSE 未启用
	 */
	inline BOOLEAN IsEnableFilterRegister() const {
		return !m_bDisableFilterRegister;
	};

	inline BOOLEAN IsEnableFilterSuperUser() const {
		return m_bEnableFilterSuperUser;
	};
	/**
	 * @brief 获取环境路径。
	 * @return INIDATA 环境路径数据
	 */
	inline const INIDATA& GetEnvPath() const {
		return m_EnvPath;
	};

	/**
	 * @brief 获取注册表文件列表。
	 * @return INIDATA 注册表文件列表数据
	 */
	inline const INIDATA& GetHiveFileList() const {
		return m_HiveFileList;
	};
	/**
	* @brief 获取文件路径黑名单。
	* @return const CStringW& 文件路径黑名单字符串
	*/
	inline const CStringW& GetFilePathBlack() const {
		return m_FilePathBlack;
	};
	/**
	* @brief 获取文件路径白名单。
	* @return const CStringW& 文件路径白名单字符串
	*/
	inline const CStringW& GetFilePathWhite() const {
		return m_FilePathWhite;
	};

	inline const CStringW& GetRegPathBlack() const {
		return m_RegPathBlackString;
	};

	inline const CStringW& GetRegPathWhite() const {
		return m_RegPathWhiteString;
	};

	//inline CStringList GetParsedRegPathBlack() const {
	//	return GetParsedRegPath(m_RegPathBlackString);
	//};

	//inline CStringList GetParsedRegPathWhite() const {
	//	return GetParsedRegPath(m_RegPathWhiteString);
	//};

	inline CStringList GetParsedRegPath(const CStringW& regPath) const {
		CStringList regPathList;
		if (regPath.IsMultiFormat())
			MultiStrings2List(regPath, regPathList);
		else
			SplitStrings2List(regPath, regPathList, WSPLIT_STRING, FALSE);
		return ParseRegistryPath(regPathList);
	};

	/**
	 * @brief 加载过滤器集合。
	 * @param buffer 包含过滤器集合的缓冲区
	 * @return BOOLEAN 是否加载成功
	 */
	BOOLEAN LoadFilterSet(const CSimpleBuffer& buffer);

	/**
	 * @brief 获取所有的注册表路径。
	 * @param lHiveFileList 注册表路径列表
	 */
	 //VOID GetAllHiveFileList(CStringList& lHiveFileList);

	CStringW GetWindowsRoot();

	/**
	 * @brief 获取所有的临时文件夹路径。
	 * @param lTempFolderList 临时文件夹路径列表
	 */
	VOID GetAllTempFolderList(CStringList& lTempFolderList);

protected:
	/**
	 * @brief 重置过滤器集合状态。
	 */
	VOID Reset();

	/**
	 * @brief 加载当前值。
	 */
	VOID LoadCurrentValue();

	/**
	 * @brief 解析注册表路径。
	 * @param lRegPathList 注册表路径列表
	 */
	CStringList ParseRegistryPath(const CStringList& lRegPathList) const;

	/**
	 * @brief 查询注册表映射路径。
	 * @param sRegCompletePath 完整的注册表路径
	 * @param sMappedPath 映射路径
	 * @return NTSTATUS 操作状态码
	 */
	NTSTATUS QueryRegistryMappedPath(PCWSTR sRegCompletePath, CStringW& sMappedPath);

	BYTE m_bEnableFilterFile = FALSE; //是否启用文件过滤
	BYTE m_bEnableFilterSuperUser = FALSE;	// 超级用户下也启动过滤
	BYTE m_bDisableFilterRegister = FALSE; //是否启用注册表过滤
	BYTE m_bReserved = FALSE;
	ULONG m_CurrentControlSet = (ULONG)(-1); //当前控制集
	ULONG m_CurrentHardwareConfig = (ULONG)(-1); //当前硬件配置文件
	CStringW m_CurrentUserSid = L""; //当前用户SID
	CStringW m_RegPathBlackString = L""; //要转移的注册表路径-黑名单字符串
	CStringW m_RegPathWhiteString = L""; //要转移的注册表路径-白名单字符串
	CStringW m_FilePathBlack = L""; //要转移的文件路径-黑名单
	CStringW m_FilePathWhite = L""; //要转移的文件路径-白名单
	INIDATA m_EnvPath;					//环境变量路径
	INIDATA m_HiveFileList;				//  注册表文件列表
};
