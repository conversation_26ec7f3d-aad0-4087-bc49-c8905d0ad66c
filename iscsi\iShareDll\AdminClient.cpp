﻿#include "stdafx.h"
#include "OEMFunc.h"
#include "DataInclude.hpp"
#include "MarkupDef.h"
#include "systemfunction.hpp"
#include "ListDataStatus.h"
#include "ListDataUser.h"
#include "UsbBootISO.h"
#include "OptionMain.h"
#include "md5.h"
#include "IniFile.h"
#include "SystemVersion.h"

#if (defined(_WIN32) || defined(_WIN64))
#include <Dbt.h>   //DEV_BROADCAST_DEVICEINTERFACE
#include "SCManager.h"
#include "regsystem.h"
#include "FormatDisk.h"
#include "iScsiFunc.h"
extern "C" {
#include "d3des.h"
}
#else
//#define _SVID_SOURCE /*如果你使用的是glib2的话，必须先定义这个宏才能使用*/
#include <time.h>
#endif // defined
#include "AdminClient.h"

CAdminClient::CAdminClient(BOOL bService /*= TRUE*/)
	: CAdminClientBase(bService)
	, m_pSeactorPool(boost::make_shared<CSector4KPool>())
	, m_pAdminAcceptor(boost::make_shared<CAdminAcceptor>(m_ioservice.get_io_service(), m_pSeactorPool, *this))
{
	Reset();
}

CAdminClient::~CAdminClient()
{
}

//开始
bool CAdminClient::Start(BOOL bService)
{
	boost::mutex::scoped_lock lock(m_mLock);
	if (bService) {
		m_PublicStatus.Start();
		m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE_DISK);
		if (m_pSeactorPool) {
			m_pSeactorPool->Load(DISKCACHE_PERSECOND_WRITE_OVERFLOW);
		}
		//会自动装载 所有分区， 也是唯一的一次，避免格式化的时候装载出错
		ReadiSCSIParameter(&GetIscsiParameter(), false, true);
		if (IsPEMode()) {
			//有无安装本地启动分区GetbVHDVolume()，都设置为PE模式
			GetIscsiParameter().m_bWinPETools = WINPE_MODE_LINUXPE;
			GetIscsiParameter().SetLinuxPEBoot();
			GetbSuperUser() = VHD_BOOT_MODE_PE;
		}
#if (defined(linux) || defined(__linux) || defined(__linux__))
		SetHostNameBySystemCmd(GetIscsiParameter().m_sHostname);
#endif
		m_ioservice.Start(10);//开始IO
		//初始化硬件信息
		if (CLIENT_RULE_PE == static_cast<CLIENT_RULE>(bService) || IsPEMode()) {
			InitHardwareInfo();
		}
		else {
			InitHardwareInfo_safe();
		}
		return true;
	}
	return false;
}

bool CAdminClient::Stop()
{
	boost::mutex::scoped_lock lock(m_mLock);
	if (m_tUdpTimer) {
		m_tUdpTimer->Close();//将包含的回调函数里的智能指针清除掉
	}
	if (m_pAdminAcceptor) {
		m_pAdminAcceptor->Stop();//前台监听停止
	}
	//连接停止
	std::for_each(m_lAdminConnects.begin(), m_lAdminConnects.end(), [](std::list<CAdminConnection_ptr>::value_type iter) {
		iter->Stop();
		iter->CloseDiskImage();
		});
	m_pRemoteConnect.reset();
	ChangeStatus_After_AllCloseDiskImage();
	StopMulticast();
	m_ioservice.Stop();
	if (m_pSeactorPool) {
		m_pSeactorPool->Unload();
	}
	return true;
}

BOOL CAdminClient::OnShutdown()
{
	return TRUE;//可以关机
}

//开始一个新的客户端连接
bool CAdminClient::StartAdminConnect(CAdminConnection_ptr pAdminConnect, bool bOnAccept)
{
	if (bOnAccept) {
		//链接进来的
		if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_unknow) {
			boost::mutex::scoped_lock lock(m_mLock);
			//从这里判断客户端来自哪里
			pAdminConnect->SetAdminRule((pAdminConnect->GetRemoteIpAddress() == INADDR_LOOPBACK) ? \
				CAdminConnection::admin_local_client : CAdminConnection::admin_remote_client);
			if (m_bStopUpdateVHD
				&& pAdminConnect->GetAdminRule() == CAdminConnection::admin_remote_client) {
				//停止上传
				pAdminConnect->Close();
				return false;
			}
			m_lAdminConnects.push_back(pAdminConnect);
		}
	}
	else {
		if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_connect_server) {
			if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_IPFROMINI)
				&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
				&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PINGGATEWAY)) {
				//必须设置了网卡IP，并连接上了服务器，才会从网卡参数里校验 iSCSIParameter
				auto pAdapterNetwork = GetLoginAdapterNetwork(pAdminConnect.get());
				if (pAdapterNetwork && IsValidIP(GetIscsiParameter().m_dwClientIP)
					&& GetIscsiParameter().m_dwClientIP == pAdapterNetwork->GetIPAddress()) {
					//要防止断线重连接时，网卡网关不正确而复位了上一次连接得到的正确参数，进而导致setgateway函数设置网关失败。。。
					//必须检查是否IP相等，避免网卡连接太早, 其上错误的参数污染了IscsiParameter正确的参数，
					ReadAdapterNetwork(pAdapterNetwork, &GetIscsiParameter());
				}
				else {
					WRITE_ISCSIFILELOG(_T("StartAdminConnect,  warning: can not get ip information to server, find adapter error by IP : ") << ip2wstring(pAdminConnect->GetLocalIpAddress()));
				}
			}
		}
	}
	return pAdminConnect->Start();
}

void CAdminClient::CloseAdminConnect(CAdminConnection_ptr pAdminConnect)
{
	if (pAdminConnect) {
		boost::mutex::scoped_lock lock(m_mLock);
		if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_connect_server) {
			//发起到服务器的远程连接，用于镜像更新，主动
			m_dwLastOffline = GetTickCountAuto();
			//远程连接断开，延时10秒关闭磁盘，等待重新连接
//			CloseDiskImage();
//			m_bUpdatingVHD = FALSE;
			pAdminConnect->SetLogined(FALSE);
			WRITE_ISCSIFILELOG(_T("CloseAdminConnect, start SendUserStatusInfo to interface , bLogined : ") << pAdminConnect->GetLogined());
			OnRemoteServerLogin(pAdminConnect->GetLogined());
		}
		else if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_connect_client) {
			//发起到其他客户机的连接，主动, 用于下载镜像p2p
			auto remoteconnect = boost::dynamic_pointer_cast<CAdminAutoReconnect>(pAdminConnect);
			if (remoteconnect) {
				//发起到其他客户机的连接，主动, 用于下载镜像p2p,设置为未登录，不清理缓存
				remoteconnect->SetLogined(FALSE);
				remoteconnect->ResetDiskImage();
				if (remoteconnect->GetNextAction() == AUTORECONNECT_NEXT_REMOVE) {
					//关掉该连接
					m_lAdminConnects.remove(pAdminConnect);
					WRITE_ISCSIFILELOG(_T("CloseAdminConnect, p2p client connect download close and removed. "));
				}
				else {
					if (!remoteconnect->GetDiskImage()) {
						WRITE_ISCSIFILELOG(_T("CloseAdminConnect, p2p client connect download close, wait reconnect. "));
					}
				}
			}
		}
		else {
			CloseDiskImage(pAdminConnect.get());
			WRITE_ISCSIFILELOG(_T("CloseAdminConnect, p2p client removed,  remote ip: ") << pAdminConnect->GetRemoteAddressW() \
				<< _T(", rule: ") << pAdminConnect->GetAdminRule());
			m_lAdminConnects.remove(pAdminConnect);
		}
	}
}
bool CAdminClient::handle_message(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect)
{
	bool bResult(false);
	switch (pAdminConnect->GetAdminRule())
	{
		//发起到服务器的远程连接，用于镜像更新，主动
	case  CAdminConnection::admin_connect_server:
		bResult = handle_message_connect_server(pReceiveMsg, pAdminConnect);
		break;
		//从本机发来的连接，用于界面显示，被动
	case  CAdminConnection::admin_local_client:
		bResult = handle_message_local_client(pReceiveMsg, pAdminConnect);
		break;
		//从其他客户机发动的远程连接，被动, 用于镜像上传p2p
	case  CAdminConnection::admin_remote_client:
		bResult = handle_message_remote_client(pReceiveMsg, pAdminConnect);
		break;
		//发起到其他客户机的连接，主动, 用于下载镜像p2p
	case  CAdminConnection::admin_connect_client:
		bResult = handle_message_connect_client(pReceiveMsg, pAdminConnect);
		break;
	default:
		break;
	}
	return bResult;
}

bool CAdminClient::handle_message_connect_server(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect)
{
	if (!pAdminConnect->GetLogined() && pReceiveMsg->GetComType() != iCOMMAND_LOGIN_RESPONSE)
		return false; //未登录

	bool bResult(false);
	switch (pReceiveMsg->GetComType())
	{
	case iCOMMAND_EDITUSER:
	{
		//用户信息(系统磁盘)有变化
		bResult = true;
		if (pReceiveMsg->GetComID1()) {
			//计算机设置有变化，需要更新
		}
		else {
			//用户信息(系统磁盘)有变化
			pAdminConnect->RefreshAllDiskCommand(!m_bUpdatingVHD, &bResult);
		}
		bResult = pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, bResult, ISHAREDISKCLIENTCMD_ONEDITUSER);
	}
	break;
	case iCOMMAND_LOGIN_RESPONSE:
	{
		//得到登陆结果
		pAdminConnect->SetLogined(pReceiveMsg->GetComID1());
		//是否登录成功,登录失败,断开
		bResult = pAdminConnect->GetLogined();
		if (bResult) {
			{
				//更新服务器密码md5
				auto pDataItem = boost::make_shared<CListDataCommon>();
				auto xml = pReceiveMsg->GetXml();
				if (pDataItem && xml) {
					pDataItem->SetData(*xml);
					if (UpdatePassword(pDataItem->GetItemDataS(LISTDATASTATUS_STRING1))) {
						SaveSetToFile();
						WRITE_ISCSIFILELOG(_T("remote connect logined, save admin password! "));
					}
				}
			}

			//登录成功
			auto pDiskImage = pAdminConnect->GetDiskImage();
			if (pDiskImage && !m_bDownloadByP2P) {
				//断线续传
				if (m_bUpdatingVHD) {
					//正在更新下载
					bResult = pAdminConnect->ContinueDownloadOnLogin();
				}
				else if (pDiskImage->GetCurDiskOffset()) {
					//正在续传，上传
					WRITE_ISCSIFILELOG(_T("remote connect logined, continue upload restore point! "));
					bResult = pAdminConnect->FirstUploadImageData();
				}
				else {
					//出现了续传错误
					StopP2pClient(pDiskImage->GetID());
					pAdminConnect->CloseDiskImage();
					ChangeStatus_After_AllCloseDiskImage();//关闭更新，等待下一次自动开始
					bResult = pAdminConnect->RefreshAllDiskCommand(true, &bResult);
				}
			}
			else {
				if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER)) {
					//服务器发来的配置信息处理完毕后才能设置已登录，避免断线重连接不能设置网关
					//仅仅第一次登陆需要刷新状态
					WRITE_ISCSIFILELOG(_T("remote connect logined, start SendUserStatusInfo "));
					bResult = SendHardwareInfo(pAdminConnect, false, FALSE);
				}
				else {
					WRITE_ISCSIFILELOG(_T("remote connect logined, last offline time: ") << m_dwLastOffline);
				}
				pAdminConnect->RefreshAllDiskCommand(!m_bUpdatingVHD, &bResult);
			}
			//请求发送autobat文件
			m_ServerConnectVersion = pReceiveMsg->GetComValue();
			if (m_ServerConnectVersion >= LOGINRESPONSE_VERSION_V1) {
				pAdminConnect->SendCommand_(iCOMMAND_AUTOBAT);
			}
		}
		WRITE_ISCSIFILELOG(_T("remote connect logined, bLogined : ") << pAdminConnect->GetLogined() << _T(", server connect version: ") << m_ServerConnectVersion);
		//把当前状态发送给界面，可能因为界面未启动而失败
		OnRemoteServerLogin(pAdminConnect->GetLogined());
		//SendUserStatusInfo_safe_all(CAdminConnection::admin_local_client, pAdminConnect->GetLogined());
	}
	break;
	case iCOMMAND_HARDWARE_INORMATION_RESPONSE:
	{//提交硬件信息后的回复
		bResult = pReceiveMsg->GetComID();
	}
	break;
	case iCOMMAND_ALLDISK_RESPONSE:
	{
		auto xml = pReceiveMsg->GetXml();
		if (pReceiveMsg->GetComID() && xml) {
			if (m_bUpdatingVHD) {
				StopP2pClient();
				if (pAdminConnect) {
					pAdminConnect->CloseDiskImage();
				}
				ChangeStatus_After_AllCloseDiskImage();
			}
			ImportDiskList(*xml, GetRootFolder());
			SendUserDiskList_safe_all(CAdminConnection::admin_local_client);
			RefreshVhdList();
			//发送本地vhd更新信息，会导致继续更新
			bResult = SendVHDStatus(pAdminConnect);
			//检查是否需要上传
			if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_UPLOADIMAGE_REQUEST) && IsNeedUploadSuperImage()) {
				//询问是否上传超级用户的景象
				WRITE_ISCSIFILELOG(_T("iCOMMAND_ALLDISK_RESPONSE, show upload image messagebox"));
				FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_UPLOADIMAGE);
			}
			SendVHDStatusToDesktop_safe();
		}
	}
	break;
	case iCOMMAND_LOCALVHD_INORMATION_RESPONSE:
	{
		//提交本地镜像更新信息后的回复,检查时候需要手动继续更新或者自动更新
		bResult = true;
		//更新的间隙，阶段停顿后，考虑是否继续更新, m_bUpdatingVHD 也许是手动更新开始后设置的
		if (bResult && !m_bStopUpdateVHD && \
			(m_bUpdatingVHD || GetIscsiParameter().IsAutoVHDUpdateMode()
				|| GetIscsiParameter().IsUnfinishVHDUpdateMode(m_UpdateOption.tCommandTime))) {
			CheckAndContinueUpdateVHD_safe();
		}
	}
	break;
	case iCOMMAND_NETWORK_INORMATION:
	{
		//网卡配置信息变化时，在发送了硬件信息命令iCOMMAND_HARDWARE_INORMATION后，由服务器发过来的命令
		auto pDataItem = CListDataUser::GetTempListDataUser();
		auto xml = pReceiveMsg->GetXml();
		auto IscsiPara = boost::make_shared<ISCSI_PARAMETER>();
		auto pUserStatus = boost::make_shared<CLIENTUSERSTATUS>();
		if (pDataItem && xml && IscsiPara && pUserStatus) {
			pDataItem->SetData(*xml);
			CListDataUser::IscsiParaGetFromListData(pDataItem, IscsiPara.get());
			CListDataUser::UserStatusGetFromListData(pDataItem, pUserStatus.get());
			CListDataUser::RebootParaGetFromListData(pDataItem, m_RebootOption);
			CListDataUser::ShutdownParaGetFromListData(pDataItem, m_ShutdownOption);
			if (CListDataUser::LockScreenParaGetFromListData(pDataItem, m_LockScreenOption)) {
				m_PublicStatus.ResetFunction(ADMIN_RUNED_SENDUSERCLIENT);//需要上报给界面
			}
			//本地更新相关参数，由客户端连上服务器后，在线获得，包括手动更新的发出命令时间
			CListDataUser::UpdateParaGetFromListData(pDataItem, m_UpdateOption);
			auto ret1 = CListDataUser::ScheduledCmdOptionsGetFromListData(pDataItem, m_ScheduledCmdOption);
			auto ret2 = CListDataUser::ScheduledCmdGetFromListData(pDataItem, m_ScheduledCommand);
			if (ret1 || ret2) {
				if (m_ScheduledCommand.m_bRunUser == GetbService()) {
					m_PublicStatus.ResetFunction(ADMIN_RUNED_SCHEDULEDCMD);//重新运行计划任务
				}
				m_PublicStatus.ResetFunction(ADMIN_RUNED_SENDUSERCLIENT);//需要上报给界面
			}
			if (CListDataUser::PoweronCmdGetFromListData(pDataItem, m_PowerOnCommand)) {
				if (m_PowerOnCommand.m_bRunUser == GetbService()) {
					m_PublicStatus.ResetFunction(ADMIN_RUNED_AUTOBAT);//重新运行开机任务
				}
				m_PublicStatus.ResetFunction(ADMIN_RUNED_SENDUSERCLIENT);//需要上报给界面
			}
			if (!IsOEMICloud() && pUserStatus->m_tLastLoginTime) {
				//linux pe 环境下，设置时间会出现将参数当成本地时间对待的bug,并且影响到ontime的执行
				auto ret1 = SetBIOSUTCTime(pUserStatus->m_tLastLoginTime);
				WRITE_ISCSIFILELOG(_T("Change system Date :") << pUserStatus->m_tLastLoginTime << _T(", result: ") << ret1);
				if (ret1 && m_tUdpTimer) {
					auto ret2 = m_tUdpTimer->Start(GetDurationFromSeconds(1), boost::bind(&CAdminClient::OnTime, this));
					WRITE_ISCSIFILELOG(_T("Start OnTime restart: ") << ret2);
				}
			}
			//智能最后重启时间,临时数据，都是从服务器上获得
			if (!m_UserStatus.SetLastRebootTime(pUserStatus->m_dwLastRebootTime) && m_UserStatus.m_dwLastRebootTime) {
				pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_REBOOT, m_UserStatus.m_dwLastRebootTime);
			}
			//智能最后关机时间,临时数据
			if (!m_UserStatus.SetLastShutdownTime(pUserStatus->m_dwLastShutdownTime) && m_UserStatus.m_dwLastShutdownTime) {
				pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_POWEROFF, m_UserStatus.m_dwLastShutdownTime);
			}
			//智能最后执行任务时间,临时数据
			if (!m_UserStatus.SetLastScheduleRunTime(pUserStatus->m_dwLastScheduleRunTime) && m_UserStatus.m_dwLastScheduleRunTime) {
				pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_SCHEDULECMD, m_UserStatus.m_dwLastScheduleRunTime);
			}
			bResult = true;
			//处理服务器发来的配置信息, PE启动模式下，由于磁盘未格式化， IscsiParameter 可能为空， m_oVHDBootMng.SetIPXEBootIP 也会失败，所以必须强制更新
			SetIPXEBootIP(IscsiPara, pUserStatus, true, pAdminConnect->GetRemoteIP(), pAdminConnect->GetLogined(), IsPEMode() || IsDisklessBoot());
		}
		bResult = pAdminConnect->SendCommand_(iCOMMAND_NETWORK_INORMATION_RESPONSE, bResult);
		WRITE_ISCSIFILELOG(_T("remote connect receive network information, start SendUserStatusInfo , bLogined : ") << pAdminConnect->GetLogined()
			<< _T(", PersonalServerIP:") << m_UserStatus.GetPersonalServerIP().c_str());
		//转发到可视界面，执行其他的配置命令，可能因为界面未启动而失败
		SendUserStatusInfo_safe_all(CAdminConnection::admin_local_client, pAdminConnect->GetLogined());
	}
	break;
	case  iCOMMAND_START_UPDATE:
	{//开始更新本地vhd
		bResult = pAdminConnect->SendCommand_(iCOMMAND_START_UPDATE_RESPONSE, StartUpdateVHD(pAdminConnect, pReceiveMsg->GetComID(), pReceiveMsg->GetActualText_Utf8()));
	}
	break;
	case  iCOMMAND_STOP_UPDATE:
	{//开始更新本地vhd
		bResult = pAdminConnect->SendCommand_(iCOMMAND_STOP_UPDATE_RESPONSE, StopUpdateVHD(pAdminConnect));
	}
	break;
	case  iCOMMAND_DOWNLOADIMAGE_RESPONSE:
	{
		DWORD dwErrorBit = 0;
		bResult = pAdminConnect->OnDownloadImageResponse(pReceiveMsg, \
			boost::bind(&CVHDBootMng::CheckVHDVolumeFreeSpace, &m_oVHDBootMng, boost::placeholders::_1, boost::placeholders::_2, boost::placeholders::_3), &dwErrorBit);
		if (bResult) {
			m_bDownloadByP2P = FALSE;
			FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_STATUS_WARNING, iCOMMAND_DOWNLOADIMAGE);
		}
		else {
			auto pDiskImage = pAdminConnect->GetDiskImage();
			if (!pReceiveMsg->GetComID() && pDiskImage && pReceiveMsg->GetComID1() == pDiskImage->GetID()) {
				//也有可能限制同时下载个数限制，导致无法下载，稍后再尝试
				bResult = true;//避免发出closeimage命令，陷入无限尝试下载的循环
			}
			//写入失败，或者磁盘空间满， 请求关闭
			StopP2pClient(pAdminConnect->GetDiskImageID());
			pAdminConnect->CloseDiskImage();
			if (!bResult) {
				//写入失败，请求关闭
				bResult = pAdminConnect->SendCommand_(iCOMMAND_CLOSEIMAGE, pReceiveMsg->GetComID1());
				WRITE_ISCSIFILELOG(_T("CAdminClient::handle_message_connect_server iCOMMAND_DOWNLOADIMAGE_RESPONSE error, send iCOMMAND_CLOSEIMAGE,  disk id: ") << pReceiveMsg->GetComID1() << _T(" , error bit: ") << dwErrorBit);
				if (ERRORSTATUS_UPDATE_CLIENT_DISK_SPACE_LOW == dwErrorBit && CanSendStatusCommand()) {
					pAdminConnect->SendCommand_(iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwErrorBit, ISHAREDISKCLIENTCMD_STARTUPDATE);
				}
			}
		}
	}
	break;
	case iCOMMAND_READLOADER_RESPONSE:
	{
		bResult = pAdminConnect->OnReadLoaderResponse(pReceiveMsg);
	}
	break;
	case iCOMMAND_READLOADER_END:
	{
		bResult = pAdminConnect->OnReadLoaderEnd(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1_RESPONSE:
	{
		//收到镜像的校验信息
		bResult = pAdminConnect->OnReadImageSha1Response(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1_END:
	{
		//校验信息下载完成，开始下载文件内容
		bResult = pAdminConnect->OnReadImageSha1End(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_RESPONSE:
	{
		DWORD dwErrorBit = 0;
		bResult = pAdminConnect->OnReadImageResponse(pReceiveMsg, &dwErrorBit);
		if (!bResult) {
			if ((ERRORSTATUS_UPDATE_SHA1_CHECK_FAILED == dwErrorBit || ERRORSTATUS_UPDATE_WRITE_FILE_FAILED == dwErrorBit) && CanSendStatusCommand()) {
				pAdminConnect->SendCommand_(iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwErrorBit, ISHAREDISKCLIENTCMD_STARTUPDATE);
			}
		}
	}
	break;
	case iCOMMAND_CLOSEIMAGE_RESPONSE:
	{
		pAdminConnect->OnCloseImageResponseCheckSended();
		//服务器已关闭镜像
		auto pDiskImage = pAdminConnect->GetDiskImage();
		if (pDiskImage && pReceiveMsg->GetComID1() == pDiskImage->GetID()) {
			auto nPercent = pDiskImage->GetPercent();
			WRITE_ISCSIFILELOG(_T("End download , close image : ") << pDiskImage->GetImagePath().filename() \
				<< _T(", from server ip: ") << pAdminConnect->GetRemoteAddressW() \
				<< _T(", current offset: ") << pDiskImage->GetCurDiskOffset() \
				<< _T(", current request offset: ") << pDiskImage->GetCurRequestDiskOffset() \
				<< _T(", end percent:") << nPercent);
			StopP2pClient(pDiskImage->GetID());
			pAdminConnect->CloseDiskImage();
			if (100 == nPercent) {
				//完成下载
				RefreshVhdList();
				//发送本地vhd更新信息后，会导致继续更新
				bResult = SendVHDStatus(pAdminConnect);
				SendVHDStatusToDesktop_safe();
			}
			else {
				//中途下载中断
				ChangeStatus_After_AllCloseDiskImage();//关闭更新，等待下一次自动开始
				bResult = true;//保持连接
			}
		}
		else {
			//因为可能客户端发出多个关闭镜像消息，所以回应有多条，必须保证不关闭客户端连接
			//其他关闭命令忽略，保持连接
			bResult = true;
		}
	}
	break;
	case iCOMMAND_SUPERUSER_RESPONSE:
	{
		//收到服务器发来的超级用户切换请求结果
		bResult = pReceiveMsg->GetComID();//是否允许切换超级
		if (bResult && IsVHDBoot()) {
			if (pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_NORMAL || pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_SUPERUSER) {
				bResult = ChangeToSuperUser(pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_SUPERUSER);
			}
			else if (pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_RESTORE || pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_KEEP) {
				bResult = ChangeToKeepMode(pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_KEEP);
			}
			else if (pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_WINPE) {
				bResult = m_oVHDBootMng.ChangeToWinPE(true);
			}
		}
		//设置是否需要关机,只有在网络启动后，从超级变为普通用户需要关机，其他都是重启
		BOOL bNeedShutdown = (IsDisklessBoot() && pReceiveMsg->GetComID1() == iCLIENT_COMMAND::iCLIENT_COMMAND_NORMAL);
		pReceiveMsg->SetComID(bResult);//服务器允许操作后，返回操作结果
		pReceiveMsg->SetComValue(bNeedShutdown);
		FindConnectSendMsg_safe_all(CAdminConnection::admin_local_client, pReceiveMsg);
		bResult = true;
	}
	break;
	case iCOMMAND_UPLOADIMAGE_RESPONSE:
	{
		//服务器已准备好上传超级用户镜像，开始上传数据
		if (GetbSuperUser() == VHD_BOOT_MODE_NORMAL) {
			//开始上传还原点
			bResult = pAdminConnect->OnUploadImageResponse(pReceiveMsg);
			if (bResult) {
				FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_STATUS_WARNING, iCOMMAND_UPLOADIMAGE);
			}
		}
		if (!bResult) {
			//写入失败，请求关闭，临时文件，无ID
			bResult = pAdminConnect->SendCommand_(iCOMMAND_CLOSEIMAGE, 0);
			WRITE_ISCSIFILELOG(_T("CAdminClient::handle_message_connect_server iCOMMAND_UPLOADIMAGE_RESPONSE error, send iCOMMAND_CLOSEIMAGE,  disk id: 0"));
		}
	}
	break;
	case iCOMMAND_WRITEIMAGE_RESPONSE:
	{
		bResult = pAdminConnect->OnWriteImageResponse(pReceiveMsg);
	}
	break;
	case iCOMMAND_WRITEIMAGE_SHA1_RESPONSE:
	{
		bResult = pAdminConnect->OnWriteImageSha1Response(pReceiveMsg);
	}
	break;
	case iCOMMAND_UPLOADIMAGE_END_RESPONSE:
	{
		//有可能收到多个回应
		pAdminConnect->OnUploadImageEndResponseCheckSended();
		if (GetbSuperUser() == VHD_BOOT_MODE_NORMAL) {
			//将当前super.vhd转换为最后一个还原点
			boost::filesystem::path pNewVersionPath;
			bResult = pAdminConnect->OnUploadImageEndResponse(pReceiveMsg, pNewVersionPath);
			if (bResult) {
				if (!pNewVersionPath.empty()) {
					m_oVHDBootMng.SetVHDBootAfterUpload(pNewVersionPath, m_pSystemVHDfile, GetbLocalKeepMode());
				}
				//询问是否上传超级用户的景象,//通知界面上传结果
				FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_UPLOADIMAGE_END, bResult);
			}
		}
		if (!bResult) {
			//写入失败，请求关闭，临时文件，无ID
			bResult = pAdminConnect->SendCommand_(iCOMMAND_CLOSEIMAGE, 0);
			WRITE_ISCSIFILELOG(_T("CAdminClient::handle_message_connect_server iCOMMAND_UPLOADIMAGE_END_RESPONSE error, send iCOMMAND_CLOSEIMAGE,  disk id: 0"));
		}
	}
	break;
	case iCOMMAND_CLIENTCMD:
	{
		bool bSendedResponse = false;
		bResult = OnClientCmdFromServer(pReceiveMsg, pAdminConnect, bSendedResponse);
		if (!bSendedResponse) {
			bResult = pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, bResult ? TRUE : FALSE, pReceiveMsg->GetComID());
		}
	}
	break;
	case iCOMMAND_CLIENTCMD_RESPONSE:
	{
		//给服务器发关机提示后的回应
		bResult = true;//不处理
	}
	break;
	case iCOMMAND_HARDWARE_STATUS_RESPONSE:
	{
		if (pReceiveMsg->GetComID()) {
			m_PublicStatus.RunedFunction(ADMIN_CLIENT_STATS);//上报状态统计
		}
		else {
			m_PublicStatus.ResetFunction(ADMIN_CLIENT_STATS);//禁止上报状态统计
		}
		bResult = true;
	}
	break;
	case iCOMMAND_CHANGEIP:
	{
		//服务器主动修改IP
		if (GetbVHDVolume() || pReceiveMsg->GetComID1()) {
			bResult = SetIPXEBootIP(pReceiveMsg, IsVHDBoot() ? pAdminConnect->GetRemoteIP() : "", pAdminConnect->GetLogined());
			if (bResult && GetbVHDVolume() && pReceiveMsg->GetComID()) {
				//需要重启,重启前发送回复，避免服务器未收到导致重复发送，重复重启。。。。。
				bResult = pAdminConnect->SendCommand_(iCOMMAND_CHANGEIP_RESPONSE, bResult);
				pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_REBOOT);
				SafeRebootWindows();
			}
		}
		else {
			//网络启动不需要修改
			bResult = true;
		}
		bResult = pAdminConnect->SendCommand_(iCOMMAND_CHANGEIP_RESPONSE, bResult);
	}
	break;
	case iCOMMAND_CHANGEIP_RESPONSE:
	{
		//客户机修改IP的回复
		bResult = pReceiveMsg->GetComID();
		if (bResult && GetbVHDVolume()) {
			bResult = SetIPXEBootIP(pReceiveMsg, "", pAdminConnect->GetLogined());
		}
		pReceiveMsg->SetComID(bResult);//服务器允许操作后，返回操作结果给界面
		FindConnectSendMsg_safe_all(CAdminConnection::admin_local_client, pReceiveMsg);
		bResult = true;
	}
	break;
	case iCOMMAND_START_VNC:
	{
		bResult = pAdminConnect->SendCommand_(iCOMMAND_START_VNC_RESPONSE, StartVNCServer());
	}
	break;
	case iCOMMAND_MULTICAST_START:
	{
		bResult = StartMulticast(pReceiveMsg->GetComID());
		bResult = pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, bResult, ISHAREDISKCLIENTCMD_START_MULTICAST);
	}
	break;
	case iCOMMAND_MULTICAST_STOP:
	{
		m_ioservice.io_service_post(boost::bind(&CAdminClient::StopMulticast, this));
		bResult = true;
		bResult = pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, bResult, ISHAREDISKCLIENTCMD_STOP_MULTICAST);
	}
	break;
	case iCOMMAND_CHANGESYSTEMID_RESPONSE:
	case iCOMMAND_DELDISK_RESPONSE:
	{
		FindConnectSendMsg_safe_all(CAdminConnection::admin_local_client, pReceiveMsg);
		if (pReceiveMsg->GetComID()) {
			pAdminConnect->RefreshAllDiskCommand(!m_bUpdatingVHD, &bResult);
		}
		bResult = true;
	}
	break;
	case iCOMMAND_AUTOBAT_RESPONSE:
	{
		auto pDataItem = boost::dynamic_pointer_cast<CTreeListDataItem>(boost::make_shared<CListDataCommon>());
		auto xml = pReceiveMsg->GetXml();
		if (pDataItem && xml) {
			pDataItem->SetData(*xml);
#if (defined(_WIN32) || defined(_WIN64))
			if (!pDataItem->GetItemData(LISTDATASTATUS_STRING1).empty() && !pDataItem->GetItemData(LISTDATASTATUS_STRING2).empty()) {
				auto pAutoBatPath = boost::filesystem::absolute(pDataItem->GetItemData(LISTDATASTATUS_STRING1), GetModuleFolder());
				auto sAutoBatDoc = pDataItem->GetItemDataS(LISTDATASTATUS_STRING2);
				bResult = WriteBinaryFile(pAutoBatPath, sAutoBatDoc.c_str(), sAutoBatDoc.size());
			}
			else {
				RemoveFile(boost::filesystem::absolute(CLIENT_PUBLIC_AUTOBAT_FILENAME, GetModuleFolder()));
			}
#else
			if (!pDataItem->GetItemData(LISTDATASTATUS_STRING3).empty() && !pDataItem->GetItemData(LISTDATASTATUS_STRING4).empty()) {
				auto pAutoBatPath = boost::filesystem::absolute(pDataItem->GetItemDataUtf8(LISTDATASTATUS_STRING3), GetModuleFolder());
				auto sAutoBatDoc = pDataItem->GetItemDataUtf8(LISTDATASTATUS_STRING4);
				bResult = WriteBinaryFile(pAutoBatPath, sAutoBatDoc.c_str(), sAutoBatDoc.size());
				//WRITE_ISCSIFILELOG(_T("write autobat file: ") << pAutoBatPath.wstring() << _T(", length: ") << sAutoBatDoc.size() << _T(", result: ") << bResult );
			}
			else {
				RemoveFile(boost::filesystem::absolute(CLIENT_PUBLIC_AUTOBAT_FILENAME_LINUX_T, GetModuleFolder()));
			}
			//#ifdef HIVEOS_RIGCONF
			//			if (!pDataItem->GetItemData(LISTDATASTATUS_STRING5).empty() && !pDataItem->GetItemData(LISTDATASTATUS_STRING6).empty()) {
			//				auto pAutoBatPath = boost::filesystem::absolute(pDataItem->GetItemDataUtf8(LISTDATASTATUS_STRING5), _T("hive-config"));
			//				auto sAutoBatDoc = pDataItem->GetItemDataUtf8(LISTDATASTATUS_STRING6);
			//				bResult = WriteBinaryFile(pAutoBatPath, sAutoBatDoc.c_str(), sAutoBatDoc.size());
			//			}
			//#endif //HIVEOS_RIGCONF
#endif
		}
		bResult = true;
	}
	break;
	case iCOMMAND_P2PSOURCE_RESPONSE:
	{
		//得到p2p列表
		pAdminConnect->OnP2pSourceResponseCheckSended();
		if (pReceiveMsg->GetComID()) {
			auto pDataItem = boost::dynamic_pointer_cast<CTreeListDataItem>(boost::make_shared<CListDataCommon>());
			auto xml = pReceiveMsg->GetXml();
			if (pDataItem && xml) {
				pDataItem->SetData(*xml);
				auto sSourceIPList = pDataItem->GetItemData(LISTDATASTATUS_STRING1);
				if (!sSourceIPList.empty()) {
					m_ioservice.io_service_post([this, sSourceIPList]() {
						boost::mutex::scoped_lock lock(m_mLock);
						ConnectNextP2pClient(sSourceIPList);
						});
					bResult = true;
				}
				else if (pDataItem->GetItemDataVal(LISTDATASTATUS_NUMBER1)) {
					//直接从服务器上更新
					bResult = pAdminConnect->SendCommand_(iCOMMAND_DOWNLOADIMAGE, m_VHDUpdateStatus.m_dwLastImageID, 0, 0, 0, NULL, 0, TRUE, true);
				}
				else {
					//无更新源，关闭后等待导致无法下载，稍后再尝试
					StopP2pClient(pAdminConnect->GetDiskImageID());
					pAdminConnect->CloseDiskImage();
					ChangeStatus_After_AllCloseDiskImage();//停止更新, 稍后再自动开始
					bResult = true; //保持连接
				}
			}
		}
		else {
			//命令失败，直接从服务器上更新
			bResult = pAdminConnect->SendCommand_(iCOMMAND_DOWNLOADIMAGE, m_VHDUpdateStatus.m_dwLastImageID, 0, 0, 0, NULL, 0, TRUE, true);
		}
	}
	break;
	default:
	{
		WRITE_ISCSIFILELOG(_T("handle_message_connect_server, unknow command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	break;
	}
	if (!bResult) {
		WRITE_ISCSIFILELOG(_T("handle_message_connect_server, error command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	return bResult;
}

bool CAdminClient::ConnectNextP2pClient(std::wstring sSourceIPList)
{
	m_aSourceIPList = SplitStrings2List(sSourceIPList);
	if (!m_aSourceIPList.empty()) {
		auto remoteconnect = (dynamic_cast<CAdminAutoReconnect*>(FindRemoteConnect(CAdminConnection::admin_connect_client)));
		if (remoteconnect) {
			//继续下载
			auto remoteip = ip2wstring(remoteconnect->GetServerEndpoint().address().to_v4());
			auto finditer = std::find_if(m_aSourceIPList.begin(), m_aSourceIPList.end(), [remoteip](const std::wstring& val)->bool {
				return (wstring2icmp(remoteip, val) == 0) ? true : false;
				});
			if (finditer == m_aSourceIPList.end()) {
				//不在列表了，重新IP开始下载
				remoteconnect->SetServerEndpoint(boost::asio::ip::tcp::endpoint(string2ip(m_aSourceIPList.front()), ISHAREDISK_CLIENT_ADMIN_PORT_VAL));
			}
			remoteconnect->SetNextAction(AUTORECONNECT_NEXT_UNKOWN);
			remoteconnect->StartTimer();//开始连接p2p服务器
		}
		else {
			auto pAdminAutoReconnect = boost::make_shared<CAdminAutoReconnect>(m_ioservice.get_io_service(), m_pSeactorPool, *this,
				boost::asio::ip::tcp::endpoint(string2ip(m_aSourceIPList.front()), ISHAREDISK_CLIENT_ADMIN_PORT_VAL));
			if (pAdminAutoReconnect) {
				m_lAdminConnects.push_back(pAdminAutoReconnect);
				pAdminAutoReconnect->SetAdminRule(CAdminConnection::admin_connect_client);
				pAdminAutoReconnect->StartTimer();//开始连接p2p服务器
			}
		}
		return true;
	}
	return false;
}

bool CAdminClient::OnClientCmdFromServer(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect, bool& bSendedResponse)
{
	bool bResult(false);
	//服务器发来的客户端命令
	switch (pReceiveMsg->GetComID()) {
	case ISHAREDISKCLIENTCMD_REBOOT:
	{
		auto paraText = pReceiveMsg->GetActualText_Utf8();
		if (!paraText.empty()) {
			CListDataUser::ParseDelayRebootPara(paraText, m_RebootOption);
		}
#if (defined(_WIN32) || defined(_WIN64))
		if (paraText.empty() || !m_RebootOption.IsDelayReboot())
#else
		if (TRUE)
#endif
		{
			//因为重启后，无法再发送 iCOMMAND_CLIENTCMD_RESPONSE， 所以提前发送，不再等 OnClientCmdFromServer 执行完毕后发送
			pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, TRUE, pReceiveMsg->GetComID());
			bSendedResponse = true;
			bResult = SafeRebootWindows(pReceiveMsg->GetComID1());
		}
		else {
			//稍后执行
			bResult = true;
		}
		WRITE_ISCSIFILELOG(_T("run client command : reboot , result:") << bResult);
	}
	break;
	case ISHAREDISKCLIENTCMD_POWEROFF:
	{
		auto paraText = pReceiveMsg->GetActualText_Utf8();
		if (!paraText.empty()) {
			CListDataUser::ParseDelayShutdownPara(paraText, m_ShutdownOption);
		}
#if (defined(_WIN32) || defined(_WIN64))
		if (paraText.empty() || !m_ShutdownOption.IsDelayReboot())
#else
		if (TRUE)
#endif
		{
			//因为重启后，无法再发送 iCOMMAND_CLIENTCMD_RESPONSE， 所以提前发送，不再等 OnClientCmdFromServer 执行完毕后发送
			pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, TRUE, pReceiveMsg->GetComID());
			bSendedResponse = true;
			bResult = SafeShutDownWindows(pReceiveMsg->GetComID1());
		}
		else {
			//稍后执行
			bResult = true;
		}
		WRITE_ISCSIFILELOG(_T("run client command : poweroff , result:") << bResult);
	}
	break;
	case ISHAREDISKCLIENTCMD_STARTUPDATE:
		bResult = StartUpdateVHD(pAdminConnect, pReceiveMsg->GetComID1(), pReceiveMsg->GetActualText_Utf8());
		break;
	case ISHAREDISKCLIENTCMD_STOPUPDATE:
		StopUpdateVHD(pAdminConnect);
		bResult = true;
		break;
	case ISHAREDISKCLIENTCMD_INSTALLVHD:
	{
		DWORD dwAutoupdate = pReceiveMsg->GetComID1();
		std::string sFormatPara = pReceiveMsg->GetActualText_Utf8();
		WRITE_ISCSIFILELOG(_T("received ISHAREDISKCLIENTCMD_INSTALLVHD, post run InstallVHDCmd, auto update:  ") << dwAutoupdate);
		m_ioservice.io_service_post([this, dwAutoupdate, sFormatPara]() {
			DWORD dwError = VHDINSTALLERROR_NOERROR;
			auto ret = InstallVHDCmd(dwAutoupdate, sFormatPara, &dwError);
			if (CanSendStatusCommand()) {
				//由于 iCOMMAND_STATUS_NORMAL 已经作为 ping 在使用，所以改为使用 iCOMMAND_STATUS_WARNING
				m_pRemoteConnect->SendCommand(ret ? iCOMMAND_STATUS_WARNING : iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwError, ISHAREDISKCLIENTCMD_INSTALLVHD);
			}
			});
		bResult = true;
	}
	break;
	case ISHAREDISKCLIENTCMD_STARTVNC:
		bResult = StartVNCServer(pReceiveMsg->GetActualText_Utf8(), LODWORD(pReceiveMsg->GetComID1()), HIDWORD(pReceiveMsg->GetComID1()));
		break;
	case ISHAREDISKCLIENTCMD_CHANGEIP:
	{
		//服务器主动修改IP
		if (GetbVHDVolume() || pReceiveMsg->GetComValue()) {
			bResult = SetIPXEBootIP(pReceiveMsg->GetXml(), pReceiveMsg->GetComValue(), IsVHDBoot() ? pAdminConnect->GetRemoteIP() : "", pAdminConnect->GetLogined());
			if (bResult && GetbVHDVolume()) {
				if (pReceiveMsg->GetComID1()) {
					//因为重启后，无法再发送 iCOMMAND_CLIENTCMD_RESPONSE， 所以提前发送，不再等 OnClientCmdFromServer 执行完毕后发送
					//快速关机重启,导致回复命令无法发出
					pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD_RESPONSE, TRUE, pReceiveMsg->GetComID());
					bSendedResponse = true;
					pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_REBOOT);
					//需要重启
					SafeRebootWindows();
				}
				else {
					SendUserStatusInfo_safe_all(CAdminConnection::admin_local_client, pAdminConnect->GetLogined());
				}
			}
		}
		else {
			//网络启动不需要修改
			bResult = true;
		}
	}
	break;
	case ISHAREDISKCLIENTCMD_ONEDITUSER:
	{
		bResult = true;
		if (pReceiveMsg->GetComID1()) {
			//计算机设置有变化，需要更新
			WRITE_ISCSIFILELOG(_T("Edit user set, start SendUserStatusInfo "));
			bResult = SendHardwareInfo(pAdminConnect, false, FALSE);
		}
		else {
			//用户信息(系统磁盘)有变化
			pAdminConnect->RefreshAllDiskCommand(true, &bResult);
		}
	}
	break;
	case ISHAREDISKCLIENTCMD_START_MULTICAST:
		bResult = StartMulticast(pReceiveMsg->GetComID1());
		break;
	case ISHAREDISKCLIENTCMD_STOP_MULTICAST:
	{
		m_ioservice.io_service_post(boost::bind(&CAdminClient::StopMulticast, this));
		bResult = true;
	}
	break;
	case ISHAREDISKCLIENTCMD_UPDATE_MAINTANCETOOLS:
	{
		std::string sServerIP = pAdminConnect->GetRemoteIP();
		m_ioservice.io_service_post([this, sServerIP]() {
			auto funUpdateToolsCallback = [this](DWORD dwError) {
				if (CanSendStatusCommand()) {
					//由于 iCOMMAND_STATUS_NORMAL 已经作为 ping 在使用，所以改为使用 iCOMMAND_STATUS_WARNING
					m_pRemoteConnect->SendCommand(dwError ? iCOMMAND_STATUS_WARNING : iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwError, ISHAREDISKCLIENTCMD_UPDATE_MAINTANCETOOLS);
				}};
			//强制更新维护工具
			StartDownloadIsoParams startDownloadIsoParams(sServerIP, GetIShareDiskWebAdminPortS(), 3, true, GetHardwareInfo().GetLanguage(), funUpdateToolsCallback);
			m_oVHDBootMng.StartDownloadIso(m_ioservice.get_io_service(), startDownloadIsoParams);
			});
		bResult = true;
	}
	break;
	case ISHAREDISKCLIENTCMD_UPDATE_LOADDER:
	{
		//更新启动文件
		auto sServerIP = pAdminConnect->GetRemoteIP();
		m_ioservice.io_service_post([this, sServerIP]() {
			DWORD dwError = VHDINSTALLERROR_NOERROR;
			auto ret = OnMulticastDiskStart("", sServerIP, &dwError);
			if (CanSendStatusCommand()) {
				//由于 iCOMMAND_STATUS_NORMAL 已经作为 ping 在使用，所以改为使用 iCOMMAND_STATUS_WARNING
				m_pRemoteConnect->SendCommand(ret ? iCOMMAND_STATUS_WARNING : iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwError, ISHAREDISKCLIENTCMD_UPDATE_LOADDER);
			}
			});
		bResult = true;
	}
	break;
	case SERVERTOCLIENT_SAVECHECK:
	{
		if (IsOEMXima()) {
			bResult = pAdminConnect->SendCommand_(iCOMMAND_SUPERUSER, iCLIENT_COMMAND_SAVECHECKCONFIRM, pReceiveMsg->GetComID1());
		}
		else {
			bResult = FindConnectSendMsg_safe_all(CAdminConnection::admin_local_client, pReceiveMsg);
		}
	}
	break;
	case SERVERTOCLIENT_SAVEPERCENT:
	case SERVERTOCLIENT_SAVECOMPLETE:
	case SERVERTOCLIENT_MERGEPERCENT:
	case SERVERTOCLIENT_MERGECOMPLETE:
	case ISHAREDISKCLIENTCMD_UNLOCKSCREEN:
		bResult = FindConnectSendMsg_safe_all(CAdminConnection::admin_local_client, pReceiveMsg);
		break;
	default:
	{
		WRITE_ISCSIFILELOG(_T("OnClientCmdFromServer, unknow client command from server: ") << pReceiveMsg->GetComID());
		bResult = false;
	}
	break;
	}
	return bResult;
}

bool CAdminClient::handle_message_local_client(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect)
{
	if (!pAdminConnect->GetLogined() && pReceiveMsg->GetComType() != iCOMMAND_LOGIN)
		return false; //未登录

	bool bResult(false);
	switch (pReceiveMsg->GetComType())
	{
	case iCOMMAND_LOGIN:
	{
		//界面要求登陆
		if (!pAdminConnect->GetLogined() && pReceiveMsg->GetComID1() <= LOGIN_VERSION) {
			pAdminConnect->SetLogined(TRUE);//是否允许登陆
		}
		//发送登录命令,第一个ID为是否为重启服务后登录,第二个是否登陆成功
		pAdminConnect->SendCommand_(iCOMMAND_LOGIN_RESPONSE, pReceiveMsg->GetComID(), pAdminConnect->GetLogined());
		bResult = pAdminConnect->GetLogined();//发送后主动断开
		// 给服务器发送进入桌面命令
		FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_ENTERDESKTOP, TRUE);
		WRITE_ISCSIFILELOG(_T("local client will login, send login response , can bLogined : ") << pAdminConnect->GetLogined());
	}
	break;
	case iCOMMAND_HARDWARE_INORMATION:
	{
		//收到从界面发来的打印机信息
		//auto remoteconnect = FindRemoteConnect(CAdminConnection::admin_connect_server);
		auto pDataItem = boost::make_shared<CListDataCommon>();
		auto xml = pReceiveMsg->GetXml();
		if (pDataItem && xml) {
			pDataItem->SetData(*xml);
			if (GetHardwareInfo().GetPrinters().compare(pDataItem->GetItemData(LISTDATASTATUS_STRING1))
				|| GetHardwareInfo().GetIME().compare(pDataItem->GetItemData(LISTDATASTATUS_STRING3))) {
				GetHardwareInfo().SetPrinters(pDataItem->GetItemData(LISTDATASTATUS_STRING1));
				GetHardwareInfo().SetIME(pDataItem->GetItemData(LISTDATASTATUS_STRING3));
				SendHardwareInfo_safe(TRUE);//发送界面硬件变化到服务器
			}
#if (defined(_WIN32) || defined(_WIN64))
			if (IsDirectory(pDataItem->GetItemData(LISTDATASTATUS_STRING2)) && g_ptrlogsystem) {
				CreateJunctionPoint(boost::filesystem::absolute(GetLogSystemFileName1(), g_ptrlogsystem->LogFolder().parent_path()).c_str(), \
					pDataItem->GetItemData(LISTDATASTATUS_STRING2).c_str(), TRUE, TRUE);
			}
			if (IsDirectory(pDataItem->GetItemData(LISTDATASTATUS_STRING4)) && g_ptrlogsystem) {
				CreateJunctionPoint(boost::filesystem::absolute(MAKELINK_INI_FOLDER, g_ptrlogsystem->LogFolder().parent_path()).c_str(), \
					pDataItem->GetItemData(LISTDATASTATUS_STRING4).c_str(), TRUE, TRUE);
			}
#endif
			SetLanguage(pDataItem->GetItemData(LISTDATASTATUS_STRING5));
			bResult = true;
		}
		auto bLogined = GetRemoteServerLogined();
		WRITE_ISCSIFILELOG(_T("local client logined, start SendUserStatusInfo , bLogined : ") << bLogined);
		bResult = (pAdminConnect->SendCommand_(iCOMMAND_HARDWARE_INORMATION_RESPONSE, bResult)
			&& SendUserStatusInfo(pAdminConnect, false, bLogined)
			&& SendUserDiskList(pAdminConnect, false));
		if (bResult && bLogined) {
			//检查是否需要上传
			if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_UPLOADIMAGE_REQUEST) && IsNeedUploadSuperImage()) {
				//询问是否上传超级用户的景象
				WRITE_ISCSIFILELOG(_T("handle_message_local_client, show upload image messagebox"));
				pAdminConnect->SendCommand_(iCOMMAND_UPLOADIMAGE);
			}
			SendVHDStatusToDesktop(pAdminConnect, false);
		}
	}
	break;
	case iCOMMAND_NETWORK_INORMATION_RESPONSE:
	case iCOMMAND_ALLDISK_RESPONSE:
	case iCOMMAND_HARDWARE_STATUS_RESPONSE:
	{//给界面发送配置信息后的回应
		bResult = true;
	}
	break;
	case iCOMMAND_SUPERUSER:
	{//进入,退出超级用户
		bResult = FindConnectSendMsg_safe_all(CAdminConnection::admin_connect_server, pReceiveMsg);
		if (!bResult) {
			//转发命令到服务器失败，通知界面
			if (IsVHDBoot()) {
				if (pReceiveMsg->GetComID() == iCLIENT_COMMAND::iCLIENT_COMMAND_NORMAL) {
					//退出超级不用密码
					bResult = ChangeToSuperUser(pReceiveMsg->GetComID() == iCLIENT_COMMAND_SUPERUSER);
				}
				else {
					//本地启动时可以支持某些离线指令
					auto pDataItem = boost::make_shared<CListDataCommon>();
					auto xml = pReceiveMsg->GetXml();
					if (pDataItem && xml) {
						pDataItem->SetData(*xml);
						if (CheckLocalPassword(pDataItem->GetItemDataS(LISTDATASTATUS_STRING1))) {
							//密码相同，可以执行部分离线命令
							switch (pReceiveMsg->GetComID()) {
							case iCLIENT_COMMAND::iCLIENT_COMMAND_SUPERUSER:					//进入超级用户
								bResult = ChangeToSuperUser(pReceiveMsg->GetComID() == iCLIENT_COMMAND_SUPERUSER);
								break;
							case iCLIENT_COMMAND::iCLIENT_COMMAND_RESTORE:					//进入系统自动还原模式
								bResult = ChangeToKeepMode(pReceiveMsg->GetComID() == iCLIENT_COMMAND_KEEP);
								break;
							case iCLIENT_COMMAND::iCLIENT_COMMAND_KEEP:						//进入系统保留模式
								bResult = ChangeToKeepMode(pReceiveMsg->GetComID() == iCLIENT_COMMAND_KEEP);
								break;
							case iCLIENT_COMMAND::iCLIENT_COMMAND_OPTIONS:					//显示对话框，修改IP等参数
								bResult = true;
								break;
							case iCLIENT_COMMAND::iCLIENT_COMMAND_WINPE:					//进入winpe
								bResult = m_oVHDBootMng.ChangeToWinPE(true);
								break;
							}
						}
					}
				}
			}
#if (defined(_WIN32) || defined(_WIN64))  &&  (defined(DEBUG) || defined(_DEBUG))
			else {
				//虚拟机测试时，会产生本地启动的误报，测试用，不需要密码
				if (pReceiveMsg->GetComID() == iCLIENT_COMMAND::iCLIENT_COMMAND_OPTIONS) {
					bResult = true;
				}
			}
#endif
			bResult = pAdminConnect->SendCommand_(iCOMMAND_SUPERUSER_RESPONSE, bResult, pReceiveMsg->GetComID());
		}
	}
	break;
	case iCOMMAND_UPLOADIMAGE_RESPONSE:
	{
		//界面要求上传超级用户镜像
		bResult = IsNeedUploadSuperImage();
		if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_UPLOADIMAGE_REQUEST) && bResult) {
			WRITE_ISCSIFILELOG(_T("handle_message_local_client, received upload image response : ") << pReceiveMsg->GetComID());
			if (pReceiveMsg->GetComID()) {
				//开始上传
				UploadSuperVHD(pReceiveMsg);
			}
			else {
				m_oVHDBootMng.RestoreSuperVHD();
			}
			m_PublicStatus.RunedFunction(ADMIN_RUNED_UPLOADIMAGE_REQUEST);//只上传一次
		}
	}
	break;
	case iCOMMAND_UPLOADIMAGE_END_RESPONSE:
	{
		if (GetbSuperUser() == VHD_BOOT_MODE_NORMAL) {
			bResult = true;
		}
	}
	break;
	case iCOMMAND_CHANGEIP:
	{
		//从客户端修改IP，正常启动时直接修改到注册表
		auto funSetIscsiParaToRegister = [&pReceiveMsg, &bResult, this]() {
			auto pDataItem = CListDataUser::GetTempListDataUser();
			auto xml = pReceiveMsg->GetXml();
			auto IscsiPara = boost::make_shared<ISCSI_PARAMETER>();
			if (pDataItem && xml && IscsiPara) {
				pDataItem->SetData(*xml);
				CListDataUser::IscsiParaGetFromListData(pDataItem, IscsiPara.get());
				bResult = SetiSCSIParameter(IscsiPara.get());
			}
			};
		if (GetIscsiParameter().IsNormalBoot()) {
			//安装后启动，也可以修改服务器IP等基本参数
			funSetIscsiParaToRegister();
		}
		else {
			//修改本地启动的IP
			bResult = FindConnectSendMsg_safe_all(CAdminConnection::admin_connect_server, pReceiveMsg);
			if (!bResult) {
				//发送服务器失败后，本地强制修改
				bResult = SetIPXEBootIP(pReceiveMsg, "", false);
#if (defined(DEBUG) || defined(_DEBUG))
				if (!bResult) {
					funSetIscsiParaToRegister();
				}
#endif
			}
		}
		//转发命令到服务器失败，通知界面
		bResult = pAdminConnect->SendCommand_(iCOMMAND_CHANGEIP_RESPONSE, bResult);
	}
	break;
	case iCOMMAND_LOGIN_ISCSI_PERSIONAL_DISK:
	{
		//本地启动或者正常启动时，自动登录游戏盘，也可以进系统后手动登录个人漫游磁盘或者第三方磁盘。
		auto pDataItem = boost::make_shared<CListDataCommon>();
		auto xml = pReceiveMsg->GetXml();
		if (pDataItem && xml) {
			pDataItem->SetData(*xml);
			if (wstring2cmp(GetIscsiParameter().m_sChapName, pDataItem->GetItemData(LISTDATASTATUS_STRING1))
				|| wstring2cmp(GetIscsiParameter().m_sChapPassword, pDataItem->GetItemData(LISTDATASTATUS_STRING2))) {
				GetIscsiParameter().m_sChapName = pDataItem->GetItemData(LISTDATASTATUS_STRING1);
				GetIscsiParameter().m_sChapPassword = pDataItem->GetItemData(LISTDATASTATUS_STRING2);
				m_PublicStatus.ResetFunction(ADMIN_RUNED_GAMEDISK);
#if (defined(_WIN32) || defined(_WIN64))
				m_dwRetryLoginIscsiDisk = 0;
#endif
				bResult = pAdminConnect->SendCommand_(iCOMMAND_LOGIN_ISCSI_PERSIONAL_DISK_RESPONSE, LoginIscsiGameDisk());
			}
			else {
				//忽略掉重复的命令
				bResult = true;
			}
		}
	}
	break;
	case iCOMMAND_LOGOUT_ISCSI_PERSIONAL_DISK:
	{
		//登出个人磁盘，避免数据丢失
		m_ioservice.io_service_post([this]() {
			auto bResult = LogoutIscsiGameDisk();
			FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_LOGOUT_ISCSI_PERSIONAL_DISK_RESPONSE, bResult);
			});
		bResult = true;
	}
	break;
	case iCOMMAND_DELDISK:
	case iCOMMAND_CHANGESYSTEMID:
	{
		if (!IsVHDBoot()) {
			bResult = FindConnectSendMsg_safe_all(CAdminConnection::admin_connect_server, pReceiveMsg);
		}
		else {
			bResult = false;
		}
		if (!bResult) {
			bResult = pAdminConnect->SendCommand_((iCOMMAND_DELDISK == pReceiveMsg->GetComType()) ? \
				iCOMMAND_DELDISK_RESPONSE : iCOMMAND_CHANGESYSTEMID_RESPONSE, bResult);
		}
	}
	break;
	case iCOMMAND_CLIENTCMD:
	{
		//界面发来的客户端命令
		switch (pReceiveMsg->GetComID()) {
		case ISHAREDISKCLIENTCMD_REBOOT:
		{
			FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_REBOOT, 0, 0, 0, [this]() { SafeRebootWindows(); });
			bResult = true;
			WRITE_ISCSIFILELOG(_T("run client command : reboot , result:") << bResult);
		}
		break;
		case ISHAREDISKCLIENTCMD_POWEROFF:
		{
			FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_POWEROFF, 0, 0, 0, [this]() { SafeShutDownWindows(); });
			bResult = true;
			WRITE_ISCSIFILELOG(_T("run client command : poweroff , result:") << bResult);
		}
		break;
		case ISHAREDISKCLIENTCMD_SCHEDULECMD:
			FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_SCHEDULECMD);
			bResult = true;
			break;
		}
	}
	default:
	{
		WRITE_ISCSIFILELOG(_T("handle_message_local_client, unknow command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	break;
	}
	return bResult;
}

bool CAdminClient::UploadSuperVHD(CAdminMsg_Ptr pReceiveMsg)
{
	m_ioservice.io_service_post([this, pReceiveMsg]() {
		if (GetRemoteServerLogined()) {
			StopUpdateVHD();
			auto xml = pReceiveMsg->GetXml();
			auto dataitem = boost::make_shared<CListDataCommon>();
			if (dataitem && xml) {
				dataitem->SetData(*xml);
#if (defined(_WIN32) || defined(_WIN64))
				auto bFixWin7Pro = SystemVersionStringIsWin7Pro(GetHardwareInfo().GetSystemVersion());
				//如果是windows7专业版本，需要修复
				if (bFixWin7Pro) {
					m_oVHDBootMng.FixWin7Problem();
				}
#endif
				auto pNewDiskImage = boost::make_shared<CDiskImage>(GetBootVHDFile(VHD_FILE_TYPE_SUPER));
				if (pNewDiskImage && pNewDiskImage->Open(true)) {
					pNewDiskImage->CreateIndex();
					auto sNewRestoreFileName = dataitem->GetItemData(LISTDATASTATUS_STRING1);
					if (sNewRestoreFileName.empty() || CVHDBootMng::IsDefaultName(sNewRestoreFileName)) {
						//使用标准命名
						sNewRestoreFileName = NowTimeIsoWstring();
					}
					pNewDiskImage->SetName(sNewRestoreFileName + _T(VHDFILE_EXTEND));
					m_pRemoteConnect->SetDiskImage(pNewDiskImage);
					dataitem->SetItemData(LISTDATASTATUS_STRING1, sNewRestoreFileName);//镜像名字
					xml->SetDoc(_T(""));
					dataitem->GetData(*xml);
					//上传镜像，给予名字和磁盘大小的参数
					WRITE_ISCSIFILELOG(_T("handle_message_local_client,request upload image : ") << sNewRestoreFileName << _T(", parent disk id: ") << GetCurrentBootSystemID());
					m_pRemoteConnect->SendCommand(iCOMMAND_UPLOADIMAGE, *xml, GetCurrentBootSystemID(), TRUE, 0, pNewDiskImage->GetValidDataSize());
				}
			}
		}
		});
	return true;
}
//连接远程客户端的连接，用于下载镜像p2p
bool CAdminClient::handle_message_connect_client(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect)
{
	if (!pAdminConnect->GetLogined() && pReceiveMsg->GetComType() != iCOMMAND_LOGIN_RESPONSE)
		return false; //未登录

	bool bResult(false);
	switch (pReceiveMsg->GetComType())
	{
	case iCOMMAND_LOGIN_RESPONSE:
	{
		//得到登陆结果
		pAdminConnect->SetLogined(pReceiveMsg->GetComID1());
		//是否登录成功,登录失败,断开
		bResult = pAdminConnect->GetLogined();
		if (bResult) {
			//开始请求下载或者续传
			if (pAdminConnect->GetDiskImage()) {
				//断线续传
				if (m_bUpdatingVHD) {
					//正在更新下载
					bResult = pAdminConnect->ContinueDownloadOnLogin();
				}
				else {
					//出现了续传错误
					pAdminConnect->ResetDiskImage();
					(dynamic_cast<CAdminAutoReconnect*>(pAdminConnect))->SetNextAction(AUTORECONNECT_NEXT_REMOVE);
					bResult = false;//断开，删除该连接，
					WRITE_ISCSIFILELOG(_T("handle_message_connect_client, error continue download image: ") << pAdminConnect->GetDiskImage()->GetImagePath().filename()
						<< _T("m_bUpdatingVHD: ") << m_bUpdatingVHD);
				}
			}
			else {
				//开始请求下载
				auto pDiskImage = GetRemoteServerDiskImage();
				if (m_bUpdatingVHD && pDiskImage) {
					pAdminConnect->SetDiskImage(pDiskImage);
					//发送p2p请求
					auto dataitem = boost::make_shared<CListDataCommon>();
					auto xml = boost::make_shared<CMarkup>();
					if (dataitem && xml) {
						dataitem->SetItemData(LISTDATASTATUS_STRING1, m_pLocalVHDfile.pVHDfile.filename().wstring());//请求的文件名
						dataitem->SetItemData(LISTDATASTATUS_STRING2, m_pLocalVHDfile.sSystemVersion);
						dataitem->GetData(*xml);
						bResult = pAdminConnect->SendCommand_(iCOMMAND_DOWNLOADIMAGE, *xml, m_VHDUpdateStatus.m_dwLastImageID, \
							m_pLocalVHDfile.dwImageWriteTime, m_pLocalVHDfile.dwImageFileSize, \
							pDiskImage->GetCurDiskOffset(), true);
					}
				}
				else {
					//出现了设置错误
					WRITE_ISCSIFILELOG(_T("handle_message_connect_client, error start download image: ") << m_pLocalVHDfile.pVHDfile.filename()
						<< _T(", m_bUpdatingVHD: ") << m_bUpdatingVHD << _T(", pDiskImage: ") << (pDiskImage.get() ? pDiskImage->GetImagePath().wstring() : _T("")));
					pAdminConnect->ResetDiskImage();
					(dynamic_cast<CAdminAutoReconnect*>(pAdminConnect))->SetNextAction(AUTORECONNECT_NEXT_REMOVE);
					bResult = false;//断开，删除该连接，
				}
			}
		}
	}
	break;
	case  iCOMMAND_DOWNLOADIMAGE_RESPONSE:
	{
		DWORD dwErrorBit = 0;
		bResult = pAdminConnect->OnDownloadImageResponse(pReceiveMsg, \
			boost::bind(&CVHDBootMng::CheckVHDVolumeFreeSpace, &m_oVHDBootMng, boost::placeholders::_1, boost::placeholders::_2, boost::placeholders::_3), &dwErrorBit);
		if (bResult) {
			m_bDownloadByP2P = TRUE;
			//这个因为p2p断线会频繁发送消息给客户机界面
			FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_STATUS_WARNING, iCOMMAND_DOWNLOADIMAGE);
		}
		else {
			auto pDiskImage = pAdminConnect->GetDiskImage();
			if (pDiskImage) {
				WRITE_ISCSIFILELOG(_T("handle_message_connect_client, can not start download image: ") \
					<< pDiskImage->GetImagePath().filename()
					<< _T(", from client ip:") << pAdminConnect->GetRemoteAddressW());
			}
			if (!bResult) {
				//写入失败，请求关闭
				bResult = pAdminConnect->SendCommand_(iCOMMAND_CLOSEIMAGE, pReceiveMsg->GetComID1());
				WRITE_ISCSIFILELOG(_T("CAdminClient::handle_message_connect_client iCOMMAND_DOWNLOADIMAGE_RESPONSE error, send iCOMMAND_CLOSEIMAGE,  disk id: ") << pReceiveMsg->GetComID1() << _T(", error bit: ") << dwErrorBit);
				if (ERRORSTATUS_UPDATE_CLIENT_DISK_SPACE_LOW == dwErrorBit && CanSendStatusCommand()) {
					FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwErrorBit, ISHAREDISKCLIENTCMD_STARTUPDATE);
				}
			}
		}
	}
	break;
	case iCOMMAND_READLOADER_RESPONSE:
	{
		bResult = pAdminConnect->OnReadLoaderResponse(pReceiveMsg);
	}
	break;
	case iCOMMAND_READLOADER_END:
	{
		bResult = pAdminConnect->OnReadLoaderEnd(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1_RESPONSE:
	{
		//收到镜像的校验信息
		bResult = pAdminConnect->OnReadImageSha1Response(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1_END:
	{
		//校验信息下载完成，开始下载文件内容
		bResult = pAdminConnect->OnReadImageSha1End(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_RESPONSE:
	{
		DWORD dwErrorBit = 0;
		bResult = pAdminConnect->OnReadImageResponse(pReceiveMsg, &dwErrorBit);
		if (!bResult) {
			if ((ERRORSTATUS_UPDATE_SHA1_CHECK_FAILED == dwErrorBit || ERRORSTATUS_UPDATE_WRITE_FILE_FAILED == dwErrorBit) && CanSendStatusCommand()) {
				FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_STATUS_ERROR, iCOMMAND_CLIENTCMD, dwErrorBit, ISHAREDISKCLIENTCMD_STARTUPDATE);
			}
		}
	}
	break;
	case iCOMMAND_CLOSEIMAGE_RESPONSE:
	{
		pAdminConnect->OnCloseImageResponseCheckSended();
		//服务器已关闭镜像
		auto pDiskImage = pAdminConnect->GetDiskImage();
		if (pDiskImage && pReceiveMsg->GetComID1() == pDiskImage->GetID()) {
			auto nPercent = pDiskImage->GetPercent();
			WRITE_ISCSIFILELOG(_T("P2p end download , close image : ") << pDiskImage->GetImagePath().filename() \
				<< _T(", from client ip: ") << pAdminConnect->GetRemoteAddressW() \
				<< _T(", current offset: ") << pDiskImage->GetCurDiskOffset() \
				<< _T(", current request offset: ") << pDiskImage->GetCurRequestDiskOffset() \
				<< _T(", end percent: ") << nPercent);
			if (100 == nPercent) {
				if (m_pRemoteConnect) {
					StopP2pClient(m_pRemoteConnect->GetDiskImageID());
					m_pRemoteConnect->CloseDiskImage();
				}
				//完成下载
				RefreshVhdList();
				if (m_VHDUpdateStatus.IsUnfinish()) {
					//等待下一次连接指令
					(dynamic_cast<CAdminAutoReconnect*>(pAdminConnect))->SetNextAction(AUTORECONNECT_NEXT_STOPCONNECT);
				}
				else {
					//全部下载完毕后，标记为删除
					(dynamic_cast<CAdminAutoReconnect*>(pAdminConnect))->SetNextAction(AUTORECONNECT_NEXT_REMOVE);
				}
				//发送本地vhd更新信息后，会导致继续更新
				SendVHDStatus_safe();
				SendVHDStatusToDesktop_safe();
			}
		}
		pAdminConnect->ResetDiskImage();
		bResult = false;//下载完毕, 开始下一个循环
	}
	break;
	default:
	{
		WRITE_ISCSIFILELOG(_T("handle_message_connect_client, unknow command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	break;
	}
	if (!bResult && pReceiveMsg->GetComType() != iCOMMAND_CLOSEIMAGE_RESPONSE) {
		WRITE_ISCSIFILELOG(_T("handle_message_connect_client, error command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	return bResult;
}

//连接超时调用，true继续连接，false停止自动连接
bool CAdminClient::OnAdminConnectTimeout(CAdminConnection_ptr pAdminConnect, bool bConnectTimeout)
{
	//发起到其他客户机的连接，主动, 用于下载镜像p2p
	if (pAdminConnect) {
		auto remoteconnect = boost::dynamic_pointer_cast<CAdminAutoReconnect>(pAdminConnect);
		if (remoteconnect) {
			//WRITE_ISCSILOG(_T("OnAdminConnectTimeout, remote ip: ") << pAdminConnect->GetRemoteIPW()
			//	<< _T(", connect type: ") << pAdminConnect->GetAdminRule()
			//	<< _T(", connect times: ") << remoteconnect->GetReconnectTimes()
			//	<< _T(", connect is timeout: ") << bConnectTimeout);
			if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_connect_client) {
				if (remoteconnect->GetNextAction() != AUTORECONNECT_NEXT_UNKOWN) {
					//停止重新连接, 被删除或者等待下一次下载
					return false;
				}
				else if (!bConnectTimeout || remoteconnect->GetReconnectTimes() % 10 == 0) {
					//更换server ip 后再连接
					boost::mutex::scoped_lock lock(m_mLock);
					auto remoteip = ip2wstring(remoteconnect->GetServerEndpoint().address().to_v4());
					auto finditer = std::find_if(m_aSourceIPList.begin(), m_aSourceIPList.end(), [remoteip](const std::wstring& val)->bool {
						return (wstring2icmp(remoteip, val) == 0) ? true : false;
						});
					if (finditer != m_aSourceIPList.end() && ++finditer != m_aSourceIPList.end()) {
						//更换下一个IP，继续
						remoteconnect->ResetDiskImage();//可能提前关闭，清理缓存命令，关掉镜像
						remoteconnect->SetServerEndpoint(boost::asio::ip::tcp::endpoint(string2ip(*finditer), ISHAREDISK_CLIENT_ADMIN_PORT_VAL));
					}
					else if (IsIncludeServerP2pMode(GetIscsiParameter().m_nUpdateP2pMode)) {
						//完成了循环，如果允许调用服务器，就停止他
						remoteconnect->ResetDiskImage();//可能提前关闭，清理缓存命令，关掉镜像
						m_lAdminConnects.remove(pAdminConnect);
						FindConnectSendCommand(FindRemoteConnect(CAdminConnection::admin_connect_server), true, \
							iCOMMAND_DOWNLOADIMAGE, m_VHDUpdateStatus.m_dwLastImageID, 0, 0, 0, NULL, 0, TRUE, true);
						return false;
					}
					else {
						//完成了循环，不允许调用服务器，就不断的循环
						if (!m_aSourceIPList.empty()) {
							if (wstring2icmp(remoteip, m_aSourceIPList.front())) {
								//更换了IP
								remoteconnect->ResetDiskImage();//可能提前关闭，清理缓存命令，关掉镜像
							}
							remoteconnect->SetServerEndpoint(boost::asio::ip::tcp::endpoint(string2ip(m_aSourceIPList.front()), ISHAREDISK_CLIENT_ADMIN_PORT_VAL));
						}
					}
				}
			}
			else if (pAdminConnect->GetAdminRule() == CAdminConnection::admin_connect_server) {
				//连接服务器的连接，
				if (bConnectTimeout) {
					if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_IPFROMINI)
						|| !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
						|| !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PINGGATEWAY)) {
						remoteconnect->SetReconnectTimes();	//重新计时
					}
					else if (m_nOnTimeCount > 180 && remoteconnect->GetReconnectTimes() % 4 == 0) {
						ChangeTargetIP(remoteconnect.get(), (remoteconnect->GetReconnectTimes() % 8 == 0));
					}
					else {
						WRITE_ISCSILOG(_T("OnAdminConnectTimeout, connect server timeout, reconnect server times: ") << remoteconnect->GetReconnectTimes());
					}
				}
				//else {
				//	WRITE_ISCSILOG(_T("OnAdminConnectTimeout, connect server closed, reconnect server times: ") << remoteconnect->GetReconnectTimes());
				//}
			}
			else {
				WRITE_ISCSILOG(_T("OnAdminConnectTimeout, unknow connect type: ") << pAdminConnect->GetAdminRule());
			}
		}
		//else {
		//	WRITE_ISCSILOG(_T("OnAdminConnectTimeout, can not convert to CAdminAutoReconnect, remote ip: ") << pAdminConnect->GetRemoteIPW());
		//}
	}
	//else {
	//	WRITE_ISCSILOG(_T("OnAdminConnectTimeout, pAdminConnect is NULL"));
	//}
	return true;//保持定时器的调用
}

//离线时的定时器 - 优化后的主函数，分模块处理各种系统维护任务
bool CAdminClient::OnTime()
{
	m_nOnTimeCount++;	//ontime 定时器计次

	// 获取系统启动时间，避免多次调用
	auto dwSystemStartTime = GetTickCountAuto();

	// 系统启动初期任务处理
	HandleSystemStartupTasks(dwSystemStartTime);

	// 磁盘缓存和安全设置
	ProcessDiskCacheAndSecurity();

	// 网络和域处理
	HandleNetworkAndDomain(dwSystemStartTime);

	// 获取本地客户端连接，避免多次调用
	auto pLocalClientConnects = FindRemoteConnects(CAdminConnection::admin_local_client);

	// 个人磁盘和配置文件处理
	HandlePersonalDiskAndProfile(pLocalClientConnects);

	// 计划任务处理
	HandleScheduledTasks();

	// 客户端界面处理
	HandleClientInterface(pLocalClientConnects, dwSystemStartTime);

	// 磁盘空间监控
	HandleDiskSpaceMonitoring(pLocalClientConnects);

	// P2P连接处理
	CHARDWAREINFO_STATUS aHardwareInfoStatus;
	aHardwareInfoStatus.reset_speed();
	ProcessP2PConnections(aHardwareInfoStatus);

	// 离线连接处理
	ProcessOfflineConnections(aHardwareInfoStatus, dwSystemStartTime);

	// 系统监控和更新任务处理
	bool bCheckDalayReboot = false;
	ProcessSystemMonitoringAndUpdates(pLocalClientConnects, bCheckDalayReboot);

	// 延迟重启检查
	if (!pLocalClientConnects.empty() && !bCheckDalayReboot) {
		CheckDalayReboot(false);
	}

	// 设备管理处理
	HandleDeviceManagement();

	return true;//保持定时器的调用
}

// 处理系统启动初期任务 - 系统启动10分钟内的特殊处理
void CAdminClient::HandleSystemStartupTasks(DWORD dwSystemStartTime)
{
	if (dwSystemStartTime / 1000 < 60 * 10) {
#if (defined(_WIN32) || defined(_WIN64))
		EnumAndSetProcessPriority(_T("drvinst.exe"), IDLE_PRIORITY_CLASS);
#else
		SetHostNameBySystemCmd(GetIscsiParameter().m_sHostname);
#endif
		RunSetIPFromIni(FALSE);
		if (IsOEMXima()) {
#if (defined(_WIN32) || defined(_WIN64))
			RunAutoLogon();
#endif
			if (!GetbVHDVolume()) {
				//无盘模式下查找热插拔硬盘
				m_oVHDBootMng.FindVHDBootVolume();
				if (GetbVHDVolume()) {
					GetHardwareInfo().SetISOfile(GetbVHDVolume());
					if (LoadDiskList(GetRootFolder())) {
						RefreshVhdList();
						SendVHDStatusToDesktop_safe();
					}
					m_ioservice.io_service_post([this]() {
						boost::mutex::scoped_lock lock(m_mLock);
						if (GetRemoteServerLogined()) {
							//必须按照顺序发送，先报告客户机有硬盘后，再报告状态，再请求刷新
							SendHardwareInfo(m_pRemoteConnect.get(), true, FALSE);
							SendVHDStatus(m_pRemoteConnect.get(), true);
							//刷新磁盘//本地启动刷新磁盘得到镜像信息
							m_pRemoteConnect->SendCommand(iCOMMAND_ALLDISK, TRUE);
							WRITE_ISCSIFILELOG(_T("start Refresh All Disk when find local disk!"));
						}
						});
				}
			}
		}
	}
}

// 处理磁盘缓存和安全设置 - 设置磁盘缓存、USB安全和Windows注册
void CAdminClient::ProcessDiskCacheAndSecurity()
{
	SetDiskCache();
	SetDenyUsbDisk();
	RunSlmgr();//注册windows
#if (defined(_WIN32) || defined(_WIN64))
	CreatePageFile(); //创建页面文件
#else
	// 同步等待完成命令，刷新块设备缓存到磁盘
	if (!IsPEMode()) {
		CVHDBootMng::FlushVhdDiskWriteCache();
	}
#endif
}

// 处理网络和域 - 设置网关和域登录（仅启动10分钟内尝试）
void CAdminClient::HandleNetworkAndDomain(DWORD dwSystemStartTime)
{
	SetGateWay();//设置检查网关
	if (dwSystemStartTime / 1000 < 60 * 10) {
		LoginDomain();//登陆域,只尝试10分钟
	}
}

// 处理个人磁盘和配置文件 - 登录个人磁盘、MPIO和用户配置
void CAdminClient::HandlePersonalDiskAndProfile(const std::vector<CAdminConnection*>& pLocalClientConnects)
{
	//本地到界面的连接，自动登录游戏盘，也可以进系统后手动登录个人漫游磁盘或者第三方磁盘。
	if (LoginIscsiGameDisk() && !GetIscsiParameter().m_sChapName.empty()) {
		FindConnectSendCommand(pLocalClientConnects, true, iCOMMAND_LOGIN_ISCSI_PERSIONAL_DISK_RESPONSE, TRUE);
	}

	LoginIscsiMpio();
	SetUsersProfile();//设置用户个人目录
	CreateIscsiPersonalDisk();//无盘启动时自动登录计算机的个人磁盘
}

// 处理计划任务 - 执行自动批处理、计划任务和黑白名单域管理
void CAdminClient::HandleScheduledTasks()
{
	RunAutoBat();
	RunScheduleCmd();
	RunBlackWhiteListDomain();
}

// 处理客户端界面 - 启动桌面进程和锁屏处理
void CAdminClient::HandleClientInterface(const std::vector<CAdminConnection*>& pLocalClientConnects, DWORD dwSystemStartTime)
{
	//是否启动桌面进程
	if (pLocalClientConnects.empty()) {
		//避免因为客户机界面锁定长时间未登录，导致无法显示超级用户几个字
#if !(defined(DEBUG) || defined(_DEBUG))
		//启动3分钟后，每3分钟强制启动一次
		m_ServiceStatus.StartChildProcess((((m_nOnTimeCount % (60 * 3)) == 0) \
			&& ((dwSystemStartTime / 1000) > (60 * 3))) ? true : false);
#endif
	}
	else {
		//强制更新模式
		if ((!m_bStopUpdateVHD && m_bUpdatingVHD && VHD_UPDATE_MODE_FORCE == GetIscsiParameter().m_bVHDUpdateMode)
			|| m_oVHDBootMng.IsPnpVHDBooting()) {
			//正在更新，需要锁定
			FindConnectSendCommand(pLocalClientConnects, true, iCOMMAND_LOCK_SCREEN);
		}
	}
}

// 处理磁盘空间监控 - 检查磁盘剩余空间并发出警告
void CAdminClient::HandleDiskSpaceMonitoring(const std::vector<CAdminConnection*>& pLocalClientConnects)
{
	if (!pLocalClientConnects.empty()) {
		//客户机磁盘的最小空闲空间
		auto dwMinFreeSize = GetMinFreeSize();
		if (m_nOnTimeCount % 10 == 0 && GetbVHDVolume()) {
			DWORD64 dwDiskCapacity(0), dwFree(0);
			auto bNoWarning = m_oVHDBootMng.GetCheckVHDVolumeFreeSpace(dwDiskCapacity, dwFree, dwMinFreeSize);
			FindConnectSendCommand(pLocalClientConnects, true, iCOMMAND_REFRESHSTATUS, dwDiskCapacity, dwFree);
			if (!bNoWarning) {
				//磁盘剩余空间不足，发出警告
				FindConnectSendCommand(pLocalClientConnects, true, iCOMMAND_STATUS_WARNING, iCOMMAND_MAKEISO);
			}
		}
		SendVHDStatusToDesktop(pLocalClientConnects, true);
	}
}

// 处理P2P连接 - 管理各种连接类型的消息重发和状态统计
void CAdminClient::ProcessP2PConnections(CHARDWAREINFO_STATUS& aHardwareInfoStatus)
{
	//到服务器的连接，离线状态超过10秒，关闭磁盘镜像
	std::set<DWORD> aDiskIDs;
	if (m_pRemoteConnect) {
		auto pRemoteDiskImage = m_pRemoteConnect->GetDiskImage();
		if (pRemoteDiskImage) {
			//把下载镜像ID放入，避免重复统计
			aDiskIDs.insert(pRemoteDiskImage->GetID());
			if (GetRemoteServerLogined()) {
				auto lock = m_pRemoteConnect->GetLocker();
				m_pRemoteConnect->ResendMsgOnTimeout();
			}
		}
	}

	std::for_each(m_lAdminConnects.begin(), m_lAdminConnects.end(), [&aDiskIDs, &aHardwareInfoStatus, this](std::list<CAdminConnection_ptr>::value_type iter) {
		if (iter->GetAdminRule() == CAdminConnection::admin_connect_client) {
			//发起到其他客户机的连接，主动, 用于下载镜像p2p
			auto lock = iter->GetLocker();
			iter->ResendMsgOnTimeout();
		}
		else if (iter->GetAdminRule() == CAdminConnection::admin_remote_client) {
			//从其他客户机发动的远程连接，被动, 用于镜像上传p2p
			auto pDiskImage = iter->GetDiskImage();
			if (pDiskImage) {
				//查找镜像是否重复,避免统计错误,只统计上传的速度
				if (aDiskIDs.count(pDiskImage->GetID()) == 0) {
					pDiskImage->GetPercent(&aHardwareInfoStatus);
					aDiskIDs.insert(pDiskImage->GetID());
				}
			}
		}
		});
}

// 处理离线连接 - 处理断线重连和镜像下载进度统计
void CAdminClient::ProcessOfflineConnections(CHARDWAREINFO_STATUS& aHardwareInfoStatus, DWORD dwSystemStartTime)
{
	auto queryIsoProgress = [this, &aHardwareInfoStatus](bool otherCondition = false) {
		if (otherCondition || 0 == aHardwareInfoStatus.dwUpdatePercent) {
			m_oVHDBootMng.GetIsoPercent(&aHardwareInfoStatus);
		}
		GetHardwareInfoStatus()->CopySpeed(aHardwareInfoStatus);
		};

	if (m_pRemoteConnect) {
		auto lock = m_pRemoteConnect->GetLocker();
		if (!m_pRemoteConnect->GetLogined()) {
			auto pRemoteDiskImage = m_pRemoteConnect->GetDiskImage();
			if (pRemoteDiskImage &&
				m_dwLastOffline && dwSystemStartTime > m_dwLastOffline + 10000) {
				// 延时关闭磁盘，并清除待重新发送的命令
				StopP2pClient(pRemoteDiskImage->GetID());
				m_pRemoteConnect->CloseDiskImage();
				ChangeStatus_After_AllCloseDiskImage();
				WRITE_ISCSIFILELOG(_T("OnTime Close All DiskImage! "));
			}
			queryIsoProgress();
		}
		else {
			auto nPercent = GetImagePercent(&aHardwareInfoStatus);
			queryIsoProgress(-1 == nPercent || 0 == nPercent);
		}
	}
	else {
		queryIsoProgress();
	}
}

// 处理更新任务 - 检查并继续VHD更新，设置延迟重启标志
void CAdminClient::ProcessSystemMonitoringAndUpdates(const std::vector<CAdminConnection*>& pLocalClientConnects, bool& bCheckDalayReboot)
{
	if (m_pRemoteConnect) {
		auto lock = m_pRemoteConnect->GetLocker();
		if (m_pRemoteConnect->GetLogined()) {
			//检查是否需要自动更新，再定时尝试下,重新下载,  或者是手动更新未完成
			if (!m_bStopUpdateVHD && !m_VHDUpdateStatus.IsFinish() &&
				(GetIscsiParameter().IsAutoVHDUpdateMode()
					|| GetIscsiParameter().IsUnfinishVHDUpdateMode(m_UpdateOption.tCommandTime))) {
				//继续更新，直到 m_bUpdatingVHD 为 停止，
				//不检查 m_bUpdatingVHD 是为了避免 iCOMMAND_LOCALVHD_INORMATION_RESPONSE 未回复
				CheckAndContinueUpdateVHD(m_pRemoteConnect.get());
				bCheckDalayReboot = true;
			}
			HardwareInfoStatusUpdate();
			//报告自己的硬件状态温度等
			SendHardwareInfoStatus(m_pRemoteConnect.get(), false, pLocalClientConnects, true);
		}
	}
}

// 处理设备管理 - Windows下的即插即用设备安装完成检查
void CAdminClient::HandleDeviceManagement()
{
#if (defined(_WIN32) || defined(_WIN64))
	if (m_oVHDBootMng.IsPnpVHDBooting()) {
		//检查设备管理器的设备是否完成安装，完成或者超时后重启
		bool bDevHasProblem(false);
		ScanForHardwareChange([&bDevHasProblem](LPCTSTR instanceId, ULONG status, ULONG problem) -> bool {
			if (status == DN_HAS_PROBLEM) {
				//设备有问题，重启
				WRITE_ISCSIFILELOG(_T("OnTime Device Has Problem! Waiting ... ..., DeviceID: ") << instanceId);
				bDevHasProblem = true;
				return false;
			}
			return true;
			});
		if (!bDevHasProblem || m_nOnTimeCount > 60) {
			//设备安装完成，或者超过1分钟重启
			m_oVHDBootMng.FinishPnpAndReboot();
		}
	}
#endif
}
//远程连入的客户端，用于p2p上传镜像
bool CAdminClient::handle_message_remote_client(const CAdminMsg_Ptr& pReceiveMsg, CAdminConnection* pAdminConnect)
{
	if (!pAdminConnect->GetLogined() && pReceiveMsg->GetComType() != iCOMMAND_LOGIN)
		return false; //未登录

	bool bResult(false);
	switch (pReceiveMsg->GetComType())
	{
	case iCOMMAND_LOGIN:
	{
		pAdminConnect->SetLogined(TRUE);//是否允许登陆
		pAdminConnect->SendCommand_(iCOMMAND_LOGIN_RESPONSE, pReceiveMsg->GetComID(), pAdminConnect->GetLogined(), LOGINRESPONSE_VERSION);
		bResult = true;
	}
	break;
	case iCOMMAND_DOWNLOADIMAGE:
	{
		m_ioservice.io_service_post(boost::bind(&CAdminClient::OnUploadDiskImage, this, pReceiveMsg, pAdminConnect));
		bResult = true;
	}
	break;
	case iCOMMAND_READLOADER:
	{
		//读取loader文件
		bResult = pAdminConnect->OnReadLoader(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1:
	{
		//读取sha1索引文件
		bResult = pAdminConnect->OnReadImageSha1(pReceiveMsg);
	}
	break;
	case iCOMMAND_READIMAGE_SHA1_END_RESPONSE:
	{
		auto pDiskImage = pAdminConnect->GetDiskImage();
		bResult = (pReceiveMsg->GetComID() && pDiskImage && pDiskImage->GetID() == pReceiveMsg->GetComID1()) ? true : false;
	}
	break;
	case iCOMMAND_READIMAGE:
	{
		//读取文件
		bResult = pAdminConnect->OnReadImage(pReceiveMsg);
	}
	break;
	case iCOMMAND_CLOSEIMAGE:
	{
		auto pDiskImage = pAdminConnect->GetDiskImage();
		bResult = (pDiskImage && pDiskImage->GetID() == pReceiveMsg->GetComID());
		if (bResult) CloseDiskImage(pAdminConnect);
		pAdminConnect->SendCommand_(iCOMMAND_CLOSEIMAGE_RESPONSE, bResult, pReceiveMsg->GetComID());
		bResult = true; //不要关闭该上传连接，避免iCOMMAND_READIMAGE的回复命令和iCOMMAND_CLOSEIMAGE_RESPONSE命令未发送出去，导致客户端无法接收到
	}
	break;
	case iCOMMAND_ALLDISK:
	{
		//发送盘列表
		auto xml = boost::make_shared<CMarkup>();
		if (xml && ExportDiskList(*xml, FALSE)) {
			bResult = pAdminConnect->SendCommand_(iCOMMAND_ALLDISK_RESPONSE, *xml, TRUE);
		}
		else {
			bResult = pAdminConnect->SendCommand_(iCOMMAND_ALLDISK_RESPONSE, FALSE);
		}
	}
	break;
	default:
	{
		WRITE_ISCSIFILELOG(_T("handle_message_remote_client, unknow command: ") << GetiCommandDescript(pReceiveMsg->GetComType()) << _T(", Remote IP:") << pAdminConnect->GetRemoteAddress().c_str());
	}
	break;
	}
	if (!bResult && pReceiveMsg->GetComType() != iCOMMAND_CLOSEIMAGE) {
		WRITE_ISCSIFILELOG(_T("handle_message_remote_client, error command: ") << GetiCommandDescript(pReceiveMsg->GetComType()));
	}
	return bResult;
}
//用于p2p上传镜像
bool CAdminClient::OnUploadDiskImage(CAdminMsg_Ptr pReceiveMsg, CAdminConnection* pAdminConnect)
{
	//请求p2p下载文件
	BOOL ret(FALSE);
	IMAGE_DETAIL pImageDetail;
	auto pDataItem = boost::make_shared<CListDataCommon>();
	auto xml = pReceiveMsg->GetXml();
	if (pDataItem && xml && pReceiveMsg->GetComID() && pReceiveMsg->GetComID1() && pReceiveMsg->GetComValue() \
		&& pDataItem->SetData(*xml)
		&& !pDataItem->GetItemData(LISTDATASTATUS_STRING1).empty()) {
		pImageDetail.Set(pReceiveMsg->GetComID(), pReceiveMsg->GetComID1(), pReceiveMsg->GetComValue(), \
			boost::filesystem::absolute(pDataItem->GetItemData(LISTDATASTATUS_STRING1), GetRootFolder()),
			pDataItem->GetItemData(LISTDATASTATUS_STRING2));
		auto pDiskImage = pAdminConnect->GetDiskImage();
		if (pDiskImage && pDiskImage->GetID() != pReceiveMsg->GetComID()) {
			CloseDiskImage(pAdminConnect);
		}
		if (!pAdminConnect->GetDiskImage()) {
			//查找镜像
			auto pRemoteDiskImage = GetRemoteServerDiskImage();
			if (pRemoteDiskImage
				&& pRemoteDiskImage->GetID() == pReceiveMsg->GetComID()
				&& pRemoteDiskImage->GetTempLastWriteTime(pImageDetail) == pImageDetail.dwImageWriteTime) {
				pAdminConnect->SetDiskImage(pRemoteDiskImage);
			}
			else {
				boost::mutex::scoped_lock lock(m_mLock);
				FindOpenedDiskImage(pAdminConnect, pReceiveMsg->GetComID(), pImageDetail, false);
			}
		}
		if (!pAdminConnect->GetDiskImage()) {
			//尝试打开镜像
			auto pDiskImage = boost::make_shared<CDiskImage>();
			if (pDiskImage) {
				pDiskImage->SetID(pReceiveMsg->GetComID());
				if (pDiskImage->Open(pImageDetail, true)
					&& pDiskImage->GetLastWriteTime(pImageDetail) == pImageDetail.dwImageWriteTime) {
					pDiskImage->SeekEnd();//只读条件下，将当前读取指针位置指向最后
					pAdminConnect->SetDiskImage(pDiskImage);
				}
				else if (pDiskImage->Open(pImageDetail.GenTempFilePath(), false)
					&& pDiskImage->GetTempLastWriteTime(pImageDetail) == pImageDetail.dwImageWriteTime) {
					//如果是未下载完毕的临时文件，还需要对比
					pDiskImage->CompareDiskImage(FindLocalVHDfile(pImageDetail.pVHDfile));
					pAdminConnect->SetDiskImage(pDiskImage);
				}
			}
		}
		//等待sha1下载完毕后才能开始,必须等待上传端有一定量的数据再上传，避免频繁断开重连接
		pDiskImage = pAdminConnect->GetDiskImage();
		if (pDiskImage && (pDiskImage->GetPercent() == 100 ||
			pDiskImage->GetPercent() > 0 && pDiskImage->GetCurDiskOffset() >= pReceiveMsg->GetComValue1() + (MB_BYTE * 100))) {
			//对比长度，返回结果
			xml->SetDoc(_T(""));
			pDataItem->SetItemData(LISTDATASTATUS_STRING1, pImageDetail.pVHDfile.filename().wstring());
			pDataItem->SetItemDataValU(LISTDATASTATUS_NUMBER1, pImageDetail.dwImageFileSize);
			pDataItem->SetItemDataValU(LISTDATASTATUS_NUMBER3, pImageDetail.dwImageWriteTime);
			pDataItem->SetItemDataValU(LISTDATASTATUS_NUMBER4, pDiskImage->GetFileIndex().IsInit() ? TRUE : FALSE);
			pDataItem->SetItemDataValU(LISTDATASTATUS_NUMBER7, pDiskImage->HaveOpendDiskLoader() ? TRUE : FALSE);
			pDataItem->GetData(*xml);
			ret = TRUE;
			ret = pAdminConnect->SendCommand(iCOMMAND_DOWNLOADIMAGE_RESPONSE, *xml, ret, pReceiveMsg->GetComID());
		}
	}
	WRITE_ISCSIFILELOG(_T("OnUploadDiskImage disk image ID:") << pReceiveMsg->GetComID() \
		<< _T(", name: ") << pImageDetail.pVHDfile.filename() \
		<< _T(", remote client ip:") << pAdminConnect->GetRemoteAddressW() \
		<< _T(", result: ") << ret);
	if (!ret) {
		ret = pAdminConnect->SendCommand(iCOMMAND_DOWNLOADIMAGE_RESPONSE, ret, pReceiveMsg->GetComID());
	}
	return ret;
}
//从上传的客户端找到打开的磁盘镜像
bool CAdminClient::FindOpenedDiskImage(CAdminConnection* pAdminConnect, DWORD nImageID, const IMAGE_DETAIL& pImageDetail, bool bTempOnly)
{
	if (!pAdminConnect->GetDiskImage()) {
		//需要遍历其他的连接，如果没有再打开
		for (auto iter = m_lAdminConnects.begin(); iter != m_lAdminConnects.end(); iter++) {
			if ((*iter)->GetAdminRule() == CAdminConnection::admin_remote_client \
				&& (*iter).get() != pAdminConnect) {
				auto pDiskImage = (*iter)->GetDiskImage();
				if (pDiskImage && pDiskImage->GetID() == nImageID
					&& ((!bTempOnly && pDiskImage->GetLastWriteTime(pImageDetail) == pImageDetail.dwImageWriteTime)
						|| pDiskImage->GetTempLastWriteTime(pImageDetail) == pImageDetail.dwImageWriteTime)) {
					pAdminConnect->SetDiskImage(pDiskImage);
					return true;
				}
			}
		}
	}
	return false;
}

void CAdminClient::InitHardwareInfo()
{
	m_oVHDBootMng.FindVHDBootVolume();
#if (defined(linux) || defined(__linux) || defined(__linux__))
	if (!IsPEMode()) {
		m_oVHDBootMng.RestartLog();
	}
#else
	if (IsRemoveVirtualDiskIDKey()) {
		RemoveAllDiskHardwareIDVirtual();//删除所有的虚拟硬盘标记
	}
#endif
	LoadSetFromFile();

	if (GetbVHDVolume()) {
		if (IsPEMode()) {
			GetbLocalKeepMode() = KeepMode();
		}
		else if (IsVHDBoot()) {
			GetIscsiParameter().m_bWinPETools = m_oVHDBootMng.WinPEToolsMode() ? WINPE_MODE_WINPE : WINPE_MODE_NONE;
			GetbSuperUser() = m_oVHDBootMng.GetVHDBootMode();//检查当前启动的模式，用哪个vhd启动的
			GetbLocalKeepMode() = KeepMode();
			GetIscsiParameter().m_bPxexBoot = IsCompatibleMode(GetRootFolder());
			GetIscsiParameter().m_dwBootLun = m_oVHDBootMng.GetVHDBootNo(); //得到当前启动项
			WRITE_ISCSIFILELOG(_T("InitHardwareInfo get status, SuperUser: ") << GetbSuperUser()
				<< _T(", Local Keep Mode: ") << GetbLocalKeepMode()
				<< _T(", Pxe Boot mode: ") << GetIscsiParameter().m_bPxexBoot
				<< _T(", VHD Boot No:") << GetIscsiParameter().m_dwBootLun);
#if (defined(_WIN32) || defined(_WIN64))
			SaveParaToRegister();
			SaveiShareFilterParaToRegister();
#endif // defined
			SaveSetToFile(); //保存还原模式到set文件
		}
		//装载本地磁盘列表，并刷新。必须在 m_oVHDBootMng 初始化后，GetVHDBootMode()
		if (LoadDiskList(GetRootFolder())) {
			RefreshVhdList();
		}
	}

	//尽快禁用U盘
	SetDenyUsbDisk();
	InitAdapter(true, IsPEMode());

	GetHardwareInfo().GetHardwareInfo(GetEnableAdapterName(), GetIscsiParameter().m_MacAddress.data(), GetIscsiParameter().m_MacAddress.is_valid() ? MACADDRESSLEN : 0,
#if (defined(_WIN32) || defined(_WIN64))
		(GetiCacheX().Open() && (ICACHEX_DISK_TYPE)GetiCacheX().GetConfig()->bAutoStart) ? TRUE : FALSE,
#else
		FALSE,
#endif
		/*GetIscsiParameter().m_dwBootLun,*/ GetbVHDVolume()/*, GetBootMode()*/);

	//系统启动时已经登录了个人磁盘，就不要再登录了。
	if (IsDisklessBoot() && !GetIscsiParameter().m_sChapName.empty()) {
		m_PublicStatus.RunedFunction(ADMIN_RUNED_GAMEDISK);
	}

	RunSetIPFromIni(TRUE);

	//连接服务器
	auto pAdminAutoReconnect = boost::make_shared<CAdminAutoReconnect>(m_ioservice.get_io_service(), m_pSeactorPool, *this,
		boost::asio::ip::tcp::endpoint(string2ip(GetServerTargetIP()), ISHAREDISK_CLIENT_CONTROL_PORT_VAL));
	if (pAdminAutoReconnect) {
		m_lAdminConnects.push_back(pAdminAutoReconnect);
		m_pRemoteConnect = pAdminAutoReconnect;//保存连接，提高效率
		pAdminAutoReconnect->SetAdminRule(CAdminConnection::admin_connect_server);
		pAdminAutoReconnect->StartTimer();//开始连接服务器
	}

	m_PublicStatus.RunedFunction(ADMIN_CLIENT_STATS);//上报第一次状态统计
	m_tUdpTimer = boost::make_shared<CDeadlineTimer_Mutex>(m_ioservice.get_io_service(), m_mLock);
	if (m_tUdpTimer) {
		auto ret1 = m_tUdpTimer->Start(GetDurationFromSeconds(1), boost::bind(&CAdminClient::OnTime, this));
		WRITE_ISCSIFILELOG(_T("Start OnTime Timer: ") << ret1);
	}
	if (GetbService() && m_pAdminAcceptor) {
		m_pAdminAcceptor->Listen(ISHAREDISK_CLIENT_ADMIN_PORT_VAL);//开始监听iscsi
	}
}

void CAdminClient::RefreshVhdList()
{
	if (GetbVHDVolume()) {
		//检查版本
		auto rootfolder(GetRootFolder());
		//得到本地的所有vhd文件列表，不包括临时文件
		m_lVHDfiles.clear();
		Find_Directory(rootfolder, [this](const boost::filesystem::path& findfile)->bool {
			if (IsRestoreImgExt(findfile) && !CVHDBootMng::IsDefaultName(findfile.filename().wstring())) {
				this->m_lVHDfiles.push_back(findfile);
				WRITE_ISCSIFILELOG(_T("RefreshVhdList, Find local vhd file: ") << findfile.filename());
			}
			return true;
			});
		Reset();
		std::list<boost::filesystem::path> lUsedVHDfiles;//在用的VHD列表
		//对比服务器上传送下来的vhd列表，比较是否完成了更新
		m_GameListSet.GetVirDiskList()->for_each_all([&rootfolder, &lUsedVHDfiles, this](const CTREELIST_PATH& arg1, CTreeListDataItem_ptr pItem, const CTREELIST_PATH& treepath)->bool {
			bool ret(true);
			//只检查需要更新的镜像和游戏盘
			boost::filesystem::path pVHDfile;
			if (m_GameListSet.NeedInstallDisk(pItem, pVHDfile)) {
				//只比较vhd镜像和还原点, 保持住状态，避免中途 VHD_UPDATE_LASTSTEP 丢失
				//更新当前m_pLocalVHDfile信息
				this->SetVHDFileStatus(pItem, (this->m_VHDUpdateStatus.IsUnknow()) ? VHD_UPDATE_UNFINISH : \
					this->m_VHDUpdateStatus.m_dwUpdateStatus, boost::filesystem::absolute(pVHDfile.filename(), rootfolder), treepath);
				CDiskImage pDiskImage;
				//文件不存在,文件打不开，停止继续查找
				ret = pDiskImage.Open(this->m_pLocalVHDfile, true);
				if (ret) {
					//检查更新后的版本是否版本相符，是否有故障
					if (pDiskImage.CheckVersionImageNameError(this->m_pLocalVHDfile)) {
						//检查更新后的文件，实际版本号码是否相符,不相符，可能是出故障了，删除之
						auto pOldFilePath = pDiskImage.GetImagePath();
						WRITE_ISCSIFILELOG(_T("Delete local error vhd file: ") << pOldFilePath.filename().wstring());
						pDiskImage.Close(true, true);
						//重新尝试打开， 有可能因为系统已经切换在用了，无法删除，只能认为是好的，继续使用
						ret = pDiskImage.Open(pOldFilePath, true);
					}
				}
				if (ret) {
					lUsedVHDfiles.push_back(pDiskImage.GetImagePath());
					//成功打开后，如果文件名是FormatLastWriteTimeVHD，版本号自动相符，避免已经成功安装的vhd继续更新
					pDiskImage.UpdateStatus(this->m_pLocalVHDfile, this->m_VHDUpdateStatus);
					if (!this->m_pLocalVHDfile.Same(this->m_VHDUpdateStatus)) {
						ret = false;//版本不相同，停止比较
					}
					WRITE_ISCSIFILELOG(_T("RefreshVhdList, Check old vhd file: ") << pDiskImage.GetImagePath().filename() << _T(", Same verison: ") << ret);
					if (!ret) {
						WRITE_ISCSIFILELOG(_T("RefreshVhdList, LastImageWriteTime is : ") << this->m_pLocalVHDfile.dwImageWriteTime << _T(", local ssd LastImageWriteTime is : ") << this->m_VHDUpdateStatus.m_dwLastWriteTime);
						WRITE_ISCSIFILELOG(_T("RefreshVhdList, LastImageFileSize is : ") << this->m_pLocalVHDfile.dwImageFileSize << _T(", local ssd LastImageFileSize is : ") << this->m_VHDUpdateStatus.m_dwValidDataSize);
					}
				}
				else {
					WRITE_ISCSIFILELOG(_T("RefreshVhdList, can not find old vhd file : ") << this->m_pLocalVHDfile.GenVersionFilePath_V2().filename()
						<< _T(", or : ") << this->m_pLocalVHDfile.GenVersionFilePath().filename() << _T(", or : ") << this->m_pLocalVHDfile.GetFilePath().filename());
				}
				if (!ret) {
					//比较临时文件是否版本相符
					CDiskImage pTempDiskImage;
					if (pTempDiskImage.Open(this->m_pLocalVHDfile.GenTempFilePath(), true)) {
						pTempDiskImage.UpdateTempStatus(this->m_pLocalVHDfile, this->m_VHDUpdateStatus);
						if (this->m_pLocalVHDfile.Same(this->m_VHDUpdateStatus)) {
							this->m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_LASTSTEP; //可能会别后面的相同版本覆盖掉
							ret = true;//临时文件符合版本要求，继续比较下级
						}
						else if (this->m_VHDUpdateStatus.m_dwLastWriteTime != this->m_pLocalVHDfile.dwImageWriteTime) {
							pTempDiskImage.Close(true, true);//临时文件版本不符，删除之
						}
						WRITE_ISCSIFILELOG(_T("RefreshVhdList, Check temp vhd file: ") << pTempDiskImage.GetImagePath().filename() << _T(", Same verison: ") << ret);
						if (!ret) {
							WRITE_ISCSIFILELOG(_T("RefreshVhdList, LastImageWriteTime is : ") << this->m_pLocalVHDfile.dwImageWriteTime << _T(", local ssd LastImageWriteTime is : ") << this->m_VHDUpdateStatus.m_dwLastWriteTime);
							WRITE_ISCSIFILELOG(_T("RefreshVhdList, LastImageFileSize is : ") << this->m_pLocalVHDfile.dwImageFileSize << _T(", local ssd LastImageFileSize is : ") << this->m_VHDUpdateStatus.m_dwValidDataSize);
						}
					}
				}
				if (ret) {
					if (this->m_VHDUpdateStatus.m_dwUpdateStatus != VHD_UPDATE_LASTSTEP) //保持住临时文件状态
						this->m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_FINISH;//版本相同，继续比较下级
				}
				else {
					this->m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_UNFINISH;
				}
			}
			return ret;//true - 继续查找， false - 停止查找  碰到 VHD_UPDATE_UNFINISH 就停止 其他可以继续
			});
		CheckSystemVHDfileInit(); //删除未初始化的元素
		//文件版本检查完毕，开始检查启动文件
		if (m_VHDUpdateStatus.IsFinish()) {
			//检查本地更新是否完成了最后的切换
			//m_VHDUpdateStatus.m_dwUpdateStatus 有可能变为 VHD_UPATE_REBOOTNEED  VHD_UPDATE_LASTSTEP
			m_oVHDBootMng.CheckVHDUpdateStatus(m_pSystemVHDfile, m_VHDUpdateStatus.m_dwUpdateStatus);
		}
		WRITE_ISCSIFILELOG(_T("RefreshVhdList, m_dwUpdateStatus: ") << m_VHDUpdateStatus.FormatUpdateStatus());

		if (m_VHDUpdateStatus.IsFinish()
			&& VHD_BOOT_MODE_SUPER != GetbSuperUser()) {
			//正常启动，并切换成功后才清理多余VHD
			auto dwVHDFileCount(m_lVHDfiles.size());
			for (auto iter = m_lVHDfiles.begin(); iter != m_lVHDfiles.end();) {
				bool bRemoved(false);
				if (std::find(lUsedVHDfiles.begin(), lUsedVHDfiles.end(), *iter) == lUsedVHDfiles.end()) {
					//没有找到，属于无用的VHD文件
					bRemoved = RemoveFile(*iter);
					if (bRemoved) {
						RemoveFile(CFileIndex::GenIndexFilePath(*iter));
					}
					WRITE_ISCSIFILELOG(_T("RefreshVhdList, Remove local vhd file: ") << (*iter).filename() << _T(", result:") << bRemoved);
				}
				if (bRemoved) {
					iter = m_lVHDfiles.erase(iter);
				}
				else {
					iter++;
				}
			}
			//正常模式下还原restore, restore模式下还原 child, PE模式下 还原 child
			if (VHD_BOOT_MODE_NORMAL == GetbSuperUser() || VHD_BOOT_MODE_RESTORE == GetbSuperUser() || VHD_BOOT_MODE_PE == GetbSuperUser()) {
				m_oVHDBootMng.RestoreVHDWhenNormalAndRestoreBoot(VHD_BOOT_MODE_NORMAL == GetbSuperUser(), &m_pSystemVHDfile);
			}
			if (VHD_BOOT_MODE_NORMAL == GetbSuperUser()) {
				//清理多余的启动文件
				m_oVHDBootMng.RemoveMutilBootFiles(m_pSystemVHDfile);
			}
			//清理临时文件
			CVHDBootMng::CleanTmpFiles_S(rootfolder);
		}

		//检查是否需要更新菜单超时时间
		if (m_GameListSet.dwMenuTimeout != GetVHDMenuTimeout()) {
			//设置菜单超时时间
			SetVHDMenuTimeout(m_GameListSet.dwMenuTimeout);
		}
		//设置默认启动项
		SetDefaultBootNo();
	}
}

void CAdminClient::SetDefaultBootNo()
{
	//默认启动项为当前启动项
	int newBootNo = m_oVHDBootMng.GetVHDBootNo();
	int vhdCount = static_cast<int>(m_pSystemVHDfile.size());
	if (VHD_BOOT_MODE_SUPER != GetbSuperUser()) {
		//非超级用户模式下，检查是否需要设置默认启动项
		int defaultNo = m_GameListSet.dwDefaultNo;
		// 检查默认启动项是否有效
		if (defaultNo > 0 && defaultNo <= vhdCount && m_pSystemVHDfile[defaultNo - 1].IsInited()) {
			newBootNo = defaultNo - 1;
		}

		// 额外检查是否超出范围或因隐藏导致故障
		if (newBootNo >= vhdCount) {
			newBootNo = -1;
		}
	}

	// 仅调用一次 SetVHDDefaultBootNo
	auto oldBootNo = m_oVHDBootMng.GetVHDDefaultBootNo();
	if (oldBootNo != newBootNo) {
		// 设置默认启动项
		auto ret = m_oVHDBootMng.SetVHDDefaultBootNo(newBootNo);
		WRITE_ISCSIFILELOG(_T("SetDefaultBootNo, Set Default Boot No to: ") << newBootNo
			<< _T(", old Boot No: ") << oldBootNo
			<< _T(", VHD Count: ") << vhdCount
			<< _T(", result: ") << ret);
	}
}

//开始更新本地vhd,手动启动, 由 ISHAREDISKCLIENTCMD_STARTUPDATE， iCOMMAND_START_UPDATE 或者安装grub后调用 InstallVHDCmd
bool CAdminClient::StartUpdateVHD(CAdminConnection* pAdminConnect, int bVhdUpdateMode, std::string sDelayRebootPara)
{
	bool ret(false);
	//vhd更新模式：手动更新，手动更新后重启， 自动更新，自动更新并安装，自动更新并重启，强制更新, 更新完毕后安装, 手动更新后安装， 只更新grub， 自动更新后延时重启， 手动更新后延时重启
	GetIscsiParameter().m_bVHDUpdateMode = bVhdUpdateMode;
	//更新新的 获得延时启动参数， 包括手动更新的发出命令时间
	COptionMain::ParseClientDelayRebootOption(sDelayRebootPara, m_UpdateOption);
	m_bStopUpdateVHD = FALSE; //手动停止更新取消
	ret = true;
	CheckAndContinueUpdateVHD_safe();
	//因为服务器端会复位状态，故在这里重新发送一次
	SendVHDStatus(pAdminConnect);
	return ret;
}

//自动更新或者继续更新, 由定时器或者刷新磁盘列表回复后调用(iCOMMAND_LOCALVHD_INORMATION_RESPONSE)
bool CAdminClient::CheckAndContinueUpdateVHD(CAdminConnection* pAdminConnect)
{
	if (m_VHDUpdateStatus.m_dwUpdateStatus < GetIscsiParameter().GetNextVhdUpdateMode()
		&& ContinueUpdateVHD(pAdminConnect)) {
		//自动开始的
		if (!m_bUpdatingVHD) {
			m_bUpdatingVHD = TRUE; // UPDATINGVHD_AUTO_START;
		}
	}
	else if (!pAdminConnect->GetDiskImage()) {
		//无镜像上下载的时候，才停止更新
		m_bUpdatingVHD = FALSE;
	}
	return true;
}
//实际的更新执行体，具体根据当前更新阶段执行相应的动作
bool CAdminClient::ContinueUpdateVHD(CAdminConnection* pAdminConnect)
{
	//更新镜像
	bool ret(false);
	if (m_VHDUpdateStatus.IsUnfinish()) {
		if (m_VHDUpdateStatus.m_dwLastImageID) {
			if (!pAdminConnect->GetDiskImage()) {
				if (!FindOpenedDiskImage(pAdminConnect, m_VHDUpdateStatus.m_dwLastImageID, m_pLocalVHDfile, true)) {
					auto pNewDiskImage = boost::make_shared<CDiskImage>(m_pLocalVHDfile.GenTempFilePath());
					if (pNewDiskImage) {
						for (auto i = 0; i < 3; i++) {
							if (pNewDiskImage->Open(false, true) && \
								pNewDiskImage->GetTempLastWriteTime(m_pLocalVHDfile) != m_pLocalVHDfile.dwImageWriteTime) {
								pNewDiskImage->Close(true, true);//临时文件版本不符，删除之
							}
							if (pNewDiskImage->IsOpen() || pNewDiskImage->Create()) {
								pNewDiskImage->SetID(m_VHDUpdateStatus.m_dwLastImageID);
								//如果是未下载完毕的临时文件，还需要对比
								pNewDiskImage->CompareDiskImage(FindLocalVHDfile(m_pLocalVHDfile.pVHDfile));
								pAdminConnect->SetDiskImage(pNewDiskImage);
								break;
							}
							else {
								Sleep_Second(i);
								WRITE_ISCSIFILELOG(_T("ContinueUpdateVHD, can not open or create temp vhd file : ")
									<< m_pLocalVHDfile.GenTempFilePath().filename().wstring() << _T(", try again ") << (i + 1));
							}
						}
					}
				}
				auto pDiskImage = pAdminConnect->GetDiskImage();
				if (pDiskImage) {
					//因为是多线程接收的，m_bUpdatingVHD加锁无效
					//为了避免p2p连接客户端时反应过快，导致handle_message_connect_client的iCOMMAND_LOGIN_RESPONSE
					//中检查m_bUpdatingVHD还是FALSE,故意提前设置为开始更新标志
					m_bUpdatingVHD = TRUE;
					//请求服务器下载
					if (IsIncludeSourceP2pMode(GetIscsiParameter().m_nUpdateP2pMode)) {
						if (!GetIscsiParameter().m_sUpdateServerIPs.empty()) {
							ret = ConnectNextP2pClient(GetIscsiParameter().m_sUpdateServerIPs);
							WRITE_ISCSIFILELOG(_T("Connect exist p2p sources ip list : ") << \
								GetIscsiParameter().m_sUpdateServerIPs << _T(", result:") << ret);
						}
						else {
							//包含p2p的模式，需要请求下载列表
							ret = pAdminConnect->SendCommand_(iCOMMAND_P2PSOURCE, m_VHDUpdateStatus.m_dwLastImageID, m_pLocalVHDfile.dwImageWriteTime, 0, 0, NULL, 0, TRUE, true);
							WRITE_ISCSIFILELOG(_T("Request download p2p sources ip list : ") << \
								pDiskImage->GetImagePath().filename() << _T(", result:") << ret);
						}
					}
					else if (IsIncludeAllComputerP2pMode(GetIscsiParameter().m_nUpdateP2pMode)) {
						//包含所有计算机的模式，需要请求下载列表
						//包含p2p的模式，需要请求下载列表
						ret = pAdminConnect->SendCommand_(iCOMMAND_P2PSOURCE, m_VHDUpdateStatus.m_dwLastImageID, m_pLocalVHDfile.dwImageWriteTime, 0, 0, NULL, 0, TRUE, true);
						WRITE_ISCSIFILELOG(_T("Request download p2p sources ip list : ") << \
							pDiskImage->GetImagePath().filename() << _T(", result:") << ret);
					}
					else {
						ret = pAdminConnect->SendCommand_(iCOMMAND_DOWNLOADIMAGE, m_VHDUpdateStatus.m_dwLastImageID, 0, 0, 0, NULL, 0, TRUE, true);
						WRITE_ISCSIFILELOG(_T("Request download : ") \
							<< pDiskImage->GetImagePath().filename() << _T(", result:") << ret);
					}
				}
				else {
					WRITE_ISCSIFILELOG(_T("ContinueUpdateVHD, can not find temp vhd file : ") << m_pLocalVHDfile.GenTempFilePath().filename());
				}
			}
			else {
				auto dwDiskImageID = pAdminConnect->GetDiskImageID();
				if (dwDiskImageID == m_VHDUpdateStatus.m_dwLastImageID) {
					//正在更新，直接返回true,有可能为空, 但是，因为可能是p2p，其他connenct在连接，就不检查了。
					pAdminConnect->ResendMsgOnTimeout();
					ret = true;
				}
				else {
					//关闭磁盘，并清除待重新发送的命令
					StopP2pClient(dwDiskImageID);
					pAdminConnect->CloseDiskImage();
					ChangeStatus_After_AllCloseDiskImage();
				}
			}
		}
	}
	else if (m_VHDUpdateStatus.IsLastStep()) {
		//最后的改名动作
		ret = LastStep(pAdminConnect->GetRemoteIP());
		if (m_VHDUpdateStatus.IsFinish()) {
			//强制刷新一次，删除多余文件
			RefreshVhdList();
		}
		//发送本地vhd更新信息，会导致继续更新
		SendVHDStatus(pAdminConnect);
		SendVHDStatusToDesktop_safe();
	}
	else if (m_VHDUpdateStatus.IsNeedReboot()) {
		if (IsVHDBoot()) {
			//只有本地启动需要重启，其他启动方式立即完成
			if (GetIscsiParameter().IsDelayRebootVhdUpdateMode()) {
				//更新后的等待重启阶段也属于停止更新状态, 所以返回 false,
				//检查是否复合重启时间条件,不符合或者条件不全，返回false,中断重启, 停止状态
				CheckDalayReboot(true);
			}
			else {
				pAdminConnect->SendCommand_(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_REBOOT);
				ret = SafeRebootWindows();
			}
		}
		else {
			m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_FINISH;
			ret = true;
			if (m_VHDUpdateStatus.IsFinish()) {
				//强制刷新一次，删除多余文件
				RefreshVhdList();
			}
			//发送本地vhd更新信息，会导致继续更新
			SendVHDStatus(pAdminConnect);
			SendVHDStatusToDesktop_safe();
		}
	}
	return ret;
}
//检查自动重启条件，并提交给界面，参数 bUpdateReboot - 是否检查更新延迟重启条件
void CAdminClient::CheckDalayReboot(bool bUpdateReboot)
{
	//检查是否需要定时重启，关机等, 同时满足时，以最短的等待时间为准
	typedef std::pair<DWORD, BOOL> REBOOT_PARA;
	typedef std::vector< REBOOT_PARA > REBOOT_PARA_LIST;
	REBOOT_PARA_LIST lRebootPara;
	if (bUpdateReboot) {
		if (m_UpdateOption.IsDelayReboot() && m_UpdateOption.CheckTime()) {
			lRebootPara.push_back(std::make_pair(m_UpdateOption.nDelayTime, FALSE));
		}
	}
	if (m_RebootOption.IsDelayReboot() && m_RebootOption.CheckTime(m_UserStatus.m_dwLastRebootTime, false)) {
		lRebootPara.push_back(std::make_pair(m_RebootOption.nDelayTime, FALSE));
	}
	if (m_ShutdownOption.IsDelayReboot() && m_ShutdownOption.CheckTime(m_UserStatus.m_dwLastShutdownTime, true)) {
		lRebootPara.push_back(std::make_pair(m_ShutdownOption.nDelayTime, TRUE));
	}
	if (!lRebootPara.empty()) {
		if (lRebootPara.size() > 1) {
			//先按照 等待时间 从小到大排序；如果 等待时间 相等，则按照 是否重启 从小到大排序， (先重启，后关机)
			std::sort(lRebootPara.begin(), lRebootPara.end(), [](const REBOOT_PARA& para1, const REBOOT_PARA& para2) {
				if (para1.first < para2.first) {
					return true;
				}
				else if (para1.first == para2.first) {
					return para1.second < para2.second;
				}
				return false;
				});
		}
		FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_CHECK_LASTINPUT, lRebootPara.begin()->first, lRebootPara.begin()->second);
	}
}

DWORD CAdminClient::GetImagePercent(PCHARDWAREINFO_STATUS pHardwareInfoStatus /*= NULL*/) {
	DWORD dwPercent(-1);
	auto pDiskImage = GetRemoteServerDiskImage();
	if (pDiskImage) {
		dwPercent = pDiskImage->GetPercent(pHardwareInfoStatus);
	}
	if (-1 == dwPercent) {
		if (m_pMultiDiskImage) {
			dwPercent = m_pMultiDiskImage->GetMulicastPercent(pHardwareInfoStatus);
		}
		else {
			dwPercent = 0;
		}
	}
	return dwPercent;
}

bool CAdminClient::StopUpdateVHD(CAdminConnection* pAdminConnect)
{
	bool ret(false);
	m_bStopUpdateVHD = TRUE; //手动停止更新
	StopP2pClient();
	if (m_bUpdatingVHD) {
		//发送本地vhd更新信息
		if (pAdminConnect) {
			pAdminConnect->CloseDiskImage();
			//发送本地vhd更新信息，会导致继续更新
			ret = SendVHDStatus(pAdminConnect);
		}
		ChangeStatus_After_AllCloseDiskImage();
		RefreshVhdList();
		SendVHDStatusToDesktop_safe();
		ret = true;
	}
	return ret;
}
bool CAdminClient::StopP2pClient(DWORD dwID /*= -1*/)
{
	m_ioservice.io_service_post([this, dwID]() {
		boost::mutex::scoped_lock lock(m_mLock);
		for (auto iter = m_lAdminConnects.begin(); iter != m_lAdminConnects.end();) {
			//从其他客户机发动的远程连接，被动, 用于镜像上传p2p
			//发起到其他客户机的连接，主动, 用于下载镜像p2p
			if (((*iter)->GetAdminRule() == CAdminConnection::admin_remote_client
				|| (*iter)->GetAdminRule() == CAdminConnection::admin_connect_client)
				&& (-1 == dwID || (*iter)->GetDiskImageID() == dwID)) {
				(*iter)->Stop();
				(*iter)->ResetDiskImage();
				iter = m_lAdminConnects.erase(iter);
			}
			else {
				iter++;
			}
		}
		});
	return true;
}
//将所有临时文件改名为对应的最终文件名
bool CAdminClient::LastStep(std::string sRemoteIP)
{
	bool result(false);
	if (GetbVHDVolume()) {
		auto rootfolder = GetRootFolder();
		Reset();
		m_GameListSet.GetVirDiskList()->for_each_all([&rootfolder, &result, this](const CTREELIST_PATH& arg1, CTreeListDataItem_ptr pItem, const CTREELIST_PATH& treePath)->bool {
			bool ret(true);
			//只检查需要更新的镜像和游戏盘
			boost::filesystem::path pVHDfile;
			if (m_GameListSet.NeedInstallDisk(pItem, pVHDfile)) {
				this->SetVHDFileStatus(pItem, (this->m_VHDUpdateStatus.IsUnknow()) ? VHD_UPDATE_LASTSTEP : \
					this->m_VHDUpdateStatus.m_dwUpdateStatus, boost::filesystem::absolute(pVHDfile.filename(), rootfolder), treePath);
				CDiskImage pLocalDiskImage;
				ret = (pLocalDiskImage.Open(this->m_pLocalVHDfile, true) && pLocalDiskImage.IsSameTimeStamp(this->m_pLocalVHDfile)) ? true : false;
				if (!ret) {
					//检查临时文件是否下载完毕
					CDiskImage pTempDiskImage(this->m_pLocalVHDfile.GenTempFilePath());
					ret = (pTempDiskImage.Open(true) && pTempDiskImage.IsSameTimeStamp(this->m_pLocalVHDfile)) ? true : false;
					if (ret) {
						StopP2pClient(pItem->GetID());//停止正在上传的客户端，好改名字
						//修改临时文件名
						if (!treePath.single()) {
							//是还原点,先修改vhd的母磁盘
							auto retvalue = this->m_GameListSet.GetVirDiskList()->get_item_all(CTREELIST_TYPE::GetParentPath(treePath));
							if (RETVALUE_BOOL(retvalue)) {
								boost::filesystem::path pParentVHD(GetFilenameFromFullPath(RETVALUE_REF(retvalue)->GetOption(LISTDATADISK_DISKPATH)));
								if (IsRestoreImgExt(pParentVHD)) {
									CDiskImage pLocalParentDisk;
									IMAGE_DETAIL pParentVHDfileDetail(RETVALUE_REF(retvalue)->GetID(), \
										(DWORD64)RETVALUE_REF(retvalue)->GetOptionVal(LISTDATADISK_LASTWRITETIME), \
										(DWORD64)RETVALUE_REF(retvalue)->GetOptionVal(LISTDATADISK_FILESIZE), \
										boost::filesystem::absolute(pParentVHD.filename(), rootfolder));
									ret = pLocalParentDisk.Open(pParentVHDfileDetail, true) && pLocalParentDisk.IsSameTimeStamp(pParentVHDfileDetail);
									if (ret) {
										pLocalParentDisk.Close();
										pTempDiskImage.Close();
										//修改VHD的父磁盘
										CVHD pParentVHD, pTempVHD;
										ret = (pParentVHD.Open(pLocalParentDisk.GetImagePath(), true)
											&& pTempVHD.Open(pTempDiskImage.GetImagePath())
											&& pTempVHD.ChangeParentVHD(&pParentVHD, true));
									}
								}
							}
						}
						//更改文件名
						if (ret) {
							pLocalDiskImage.Close();//为了避免pLocalDiskImage打开的文件是m_pLocalVHDfile而无法改名，故先关闭
							ret = pTempDiskImage.RenameTo(this->m_pLocalVHDfile.GenVersionFilePath_V2());
							//if (ret) {
							//	//从父到子遍历磁盘，所以要压入前面
							//	vhdlist.push_front(this->m_pLocalVHDfile.GenVersionFilePath_V2().filename());
							//}
						}
					}
				}
				//else {
				//	//从父到子遍历磁盘，所以要压入前面
				//	vhdlist.push_front(pLocalDiskImage.GetImagePath().filename());
				//}
				if (ret) {
					this->m_VHDUpdateStatus.m_dwLastWriteTime = this->m_pLocalVHDfile.dwImageWriteTime;
					this->m_VHDUpdateStatus.m_dwValidDataSize = this->m_pLocalVHDfile.dwImageFileSize;
					this->m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPATE_REBOOTNEED;//版本相同，继续比较下级
				}
				else {
					//未完成最后一步，回滚
					this->m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_LASTSTEP;
				}
			}
			result = ret;
			return ret;//true - 继续查找， false - 停止查找
			});
		//删除未初始化的元素
		CheckSystemVHDfileInit();
		//所有文件版本都已经比较完毕，临时文件也已经改名，所有文件版本正确，开始下一步
		if (result) {
			//安装grub模式
			if (VHD_UPDATE_INSTALL_UPDATE_GRUB == GetIscsiParameter().m_bVHDUpdateMode) {
				//重新安装grub
				result = OnMulticastDiskEnd(sRemoteIP, false);
				if (result) {
					//安装成功，等待重启的下一个步骤
					m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_FINISH;
				}
			}
			if (result && (VHD_UPDATE_INSTALL_UPDATE_GRUB != GetIscsiParameter().m_bVHDUpdateMode)) {
				//设置启动项为最终的启动文件，先检查是否已经是的了。
				//检查本地更新是否完成了最后的切换
				//m_VHDUpdateStatus.m_dwUpdateStatus 有可能变为 VHD_UPATE_REBOOTNEED  VHD_UPDATE_FINISH
				m_oVHDBootMng.CheckVHDUpdateStatus(m_pSystemVHDfile, m_VHDUpdateStatus.m_dwUpdateStatus);
				if (m_VHDUpdateStatus.IsLastStep()) {
					result = m_oVHDBootMng.SetVHDBootLastStep(m_pSystemVHDfile, GetbLocalKeepMode());
					if (result) {
						//安装成功，等待重启的下一个步骤
						m_VHDUpdateStatus.m_dwUpdateStatus = IsVHDBoot() ? VHD_UPATE_REBOOTNEED : VHD_UPDATE_FINISH;
					}
				}
			}
			if (!result) {
				//未完成最后一步，回滚
				m_VHDUpdateStatus.m_dwUpdateStatus = VHD_UPDATE_LASTSTEP;
			}
		}
	}
	return result;
}
//复位更新镜像信息
void CAdminClient::Reset()
{
	m_VHDUpdateStatus.Reset();
	m_pLocalVHDfile.Reset();
	m_pSystemVHDfile.clear();
	if (IsVhdBootMenuSortNo()) {
		m_pSystemVHDfile.resize(GetMaxVhdBootMenuSortNum());
	}
	else {
		m_pSystemVHDfile.resize(m_GameListSet.setIDs.empty() ? 1 : m_GameListSet.setIDs.size());
	}
}
//更新设置更新进行信息
void CAdminClient::SetVHDFileStatus(CTreeListDataItem_ptr pItem, DWORD dwVHDUpdateStatus, const boost::filesystem::path& pLocalVHDfile, const CTREELIST_PATH& treepath)
{
	m_VHDUpdateStatus.Reset(dwVHDUpdateStatus, pItem->GetID());
	m_pLocalVHDfile.Set(pItem->GetID(), \
		pItem->GetOptionVal(LISTDATADISK_LASTWRITETIME), \
		pItem->GetOptionVal(LISTDATADISK_FILESIZE),
		pLocalVHDfile, pItem->GetOption(LISTDATADISK_SYSTEMVERSION),
		nullptr, pItem->GetOptionVal(LISTDATADISK_LOCALKEEPMODE));
	//只有系统盘和非隐藏盘才能放进菜单里
	if (pItem->GetOptionVal(LISTDATADISK_ISSYSTEMDISK)
		&& m_GameListSet.setHideSysDiskIDs.find(pItem->GetID()) == m_GameListSet.setHideSysDiskIDs.end()) {
		if (!treepath.single()) {
			//是还原点
			auto rootID = sting2DWORD(CTREELIST_TYPE::GetRootSingle(treepath).dump()); //得到根磁盘ID
			auto retvalue = m_GameListSet.GetVirDiskList()->get_item_all(rootID);
			if (RETVALUE_BOOL(retvalue)) {
				//根磁盘的版本号
				auto pRootVHDfile = boost::make_shared<IMAGE_DETAIL>();
				if (pRootVHDfile) {
					pRootVHDfile->Set(RETVALUE_REF(retvalue)->GetID(), \
						(DWORD64)RETVALUE_REF(retvalue)->GetOptionVal(LISTDATADISK_LASTWRITETIME), \
						(DWORD64)RETVALUE_REF(retvalue)->GetOptionVal(LISTDATADISK_FILESIZE), \
						boost::filesystem::absolute(GetFilenameFromFullPath(RETVALUE_REF(retvalue)->GetOption(LISTDATADISK_DISKPATH)), GetRootFolder()), \
						RETVALUE_REF(retvalue)->GetOption(LISTDATADISK_SYSTEMVERSION),
						nullptr, RETVALUE_REF(retvalue)->GetOptionVal(LISTDATADISK_LOCALKEEPMODE));
					m_pLocalVHDfile.pRootImageDetail = pRootVHDfile;
				}
			}
		}
		//记录系统启动盘
		int nMenuNo = 0;
		if (IsVhdBootMenuSortNo()) {
			nMenuNo = pItem->GetOptionVal(LISTDATADISK_MENU_NO);
		}
		SetSystemVHDfile(m_pSystemVHDfile, m_pLocalVHDfile, pItem->GetID(), nMenuNo, m_GameListSet);
	}
}

//查找真实的老版本VHD文件
boost::filesystem::path CAdminClient::FindLocalVHDfile(boost::filesystem::path pVHDfile)
{
	boost::filesystem::path pLocalVHDfile(pVHDfile);
	DWORD64 dwLastWriteTime(0);
	for (auto iter = m_lVHDfiles.begin(); iter != m_lVHDfiles.end(); iter++) {
		DWORD64 dwNoVersionLastWriteTime(0);
		auto pNoVersionPath = IMAGE_DETAIL::GenNoVersionFilePath(*iter, &dwNoVersionLastWriteTime);
		if (wstring2icmp(pVHDfile.wstring(), pNoVersionPath.wstring()) == 0
			&& dwNoVersionLastWriteTime > dwLastWriteTime) {
			//找到镜像
			pLocalVHDfile = *iter;
			dwLastWriteTime = dwNoVersionLastWriteTime;
		}
	}
	// 	if (!pLocalVHDfile.empty()) {
	// 		WRITE_ISCSIFILELOG(_T("CompareDiskImage, Find local old vhd file: ") << pLocalVHDfile.c_str());
	// 	}
	return pLocalVHDfile;
}

//发送硬件信息到服务器
bool CAdminClient::SendHardwareInfo(CAdminConnection* pAdminConnect, bool bLocked, BOOL bUserHardware)
{
	//提交硬件信息发送命令iCOMMAND_HARDWARE_INORMATION到服务器，服务器回应的同时
	//服务器会发送客户端网络配置命令iCOMMAND_NETWORK_INORMATION， 客户端根据配置调整
	auto xml = boost::make_shared<CMarkup>();
	auto dataitem = CListDataUser::GetTempListDataUser();
	if (dataitem && xml && pAdminConnect) {
		CListDataUser::IscsiParaSetToListData(&GetIscsiParameter(), dataitem);
		CListDataUser::HardwareInfoToListData(&GetHardwareInfo(), dataitem);
		dataitem->GetData(*xml);
		return bLocked ? pAdminConnect->SendCommand(iCOMMAND_HARDWARE_INORMATION, *xml, bUserHardware)
			: pAdminConnect->SendCommand_(iCOMMAND_HARDWARE_INORMATION, *xml, bUserHardware);
	}
	return false;
}

bool CAdminClient::SendHardwareInfoStatus(CAdminConnection* pAdminConnect, bool bLocked, const std::vector<CAdminConnection*>& connects, bool bClientLocked)
{
	bool ret(false);
	if (m_ServerConnectVersion >= LOGINRESPONSE_VERSION && m_PublicStatus.IsRunedFunction(ADMIN_CLIENT_STATS)) {
		//提交硬件信息发送命令iCOMMAND_HARDWARE_INORMATION到服务器，服务器回应的同时
		//服务器会发送客户端网络配置命令iCOMMAND_NETWORK_INORMATION， 客户端根据配置调整
		auto xml = boost::make_shared<CMarkup>();
		auto dataitem = CListDataUser::GetTempListDataUser();
		if (dataitem && xml && pAdminConnect) {
			CListDataUser::HardwareInfoStatusToListData(GetHardwareInfoStatus(), dataitem);
			dataitem->GetData(*xml);
			ret = bLocked ? pAdminConnect->SendCommand(iCOMMAND_HARDWARE_STATUS, *xml)
				: pAdminConnect->SendCommand_(iCOMMAND_HARDWARE_STATUS, *xml);
			for (auto iter = connects.begin(); iter != connects.end(); iter++) {
				ret &= bClientLocked ? (*iter)->SendCommand(iCOMMAND_HARDWARE_STATUS, *xml)
					: (*iter)->SendCommand_(iCOMMAND_HARDWARE_STATUS, *xml);
			}
		}
	}
	return ret;
}

CAdminConnection* CAdminClient::FindRemoteConnect(CAdminConnection::adminconnect_rule connect_rule)
{
	if (CAdminConnection::admin_connect_server == connect_rule && m_pRemoteConnect) {
		return m_pRemoteConnect.get();
	}

	auto connects = FindRemoteConnects(connect_rule);
	return !connects.empty() ? connects.front() : nullptr;
}

std::vector<CAdminConnection*> CAdminClient::FindRemoteConnects(CAdminConnection::adminconnect_rule connect_rule)
{
	std::vector<CAdminConnection*> lAdminConnects;
	if (CAdminConnection::admin_connect_server == connect_rule && m_pRemoteConnect) {
		lAdminConnects.push_back(m_pRemoteConnect.get());
	}
	else {
		for (const auto& connection : m_lAdminConnects) {
			if (connection->GetAdminRule() == connect_rule) {
				lAdminConnects.push_back(connection.get());
			}
		}
	}
	return lAdminConnects;
}

bool CAdminClient::FindConnectSendMsg_safe_all(CAdminConnection::adminconnect_rule connect_rule, const CAdminMsg_Ptr& pReceiveMsg)
{
	if (connect_rule == CAdminConnection::admin_connect_server && GetRemoteServerLogined()) {
		return  m_pRemoteConnect->SendMsg(pReceiveMsg);
	}

	m_ioservice.io_service_post([this, connect_rule, pReceiveMsg]() {
		boost::mutex::scoped_lock lock(m_mLock);
		auto lAdminConnects = FindRemoteConnects(connect_rule);
		for (const auto& iter : lAdminConnects) {
			if (iter->GetLogined()) {
				iter->SendMsg(pReceiveMsg);
			}
		}
		});
	return true;
}

bool CAdminClient::FindConnectSendCommand(CAdminConnection* pAdminConnection, bool bLocked, DWORD dwType, DWORD64 dwID /*= 0*/, DWORD64 dwID1 /*= 0*/, DWORD64 dwValue /*= 0*/, DWORD64 dwValue1 /*= 0*/, const PBYTE pData /*= NULL*/, DWORD dwDataLen /*= 0*/, BOOL bCompress /*= TRUE*/, bool bSave /*= false*/) {
	if (!pAdminConnection || !pAdminConnection->GetLogined()) {
		WRITE_ISCSIFILELOG(_T("FindConnectSendCommand can not send command, dwType:") << dwType << _T(", dwID:") << dwID << _T(", dwID1:") << dwID1 << _T(", connect:") << (pAdminConnection ? s2ws(pAdminConnection->GetRemoteIP()) : _T("NULL")) << _T(", bLogined:") << (pAdminConnection ? pAdminConnection->GetLogined() : false));
		return false;
	}
	return (bLocked ? pAdminConnection->SendCommand(dwType, dwID, dwID1, dwValue, dwValue1, pData, dwDataLen, bCompress, bSave)
		: pAdminConnection->SendCommand_(dwType, dwID, dwID1, dwValue, dwValue1, pData, dwDataLen, bCompress, bSave));
}

bool CAdminClient::FindConnectSendCommand(const std::vector<CAdminConnection*>& connects, bool bLocked, DWORD dwType, DWORD64 dwID /*= 0*/, DWORD64 dwID1 /*= 0*/, DWORD64 dwValue /*= 0*/, DWORD64 dwValue1 /*= 0*/, const PBYTE pData /*= NULL*/, DWORD dwDataLen /*= 0*/, BOOL bCompress /*= TRUE*/, bool bSave /*= false*/) {
	if (connects.empty()) {
		WRITE_ISCSIFILELOG(_T("FindConnectSendCommand can not find connect and send command, dwType:") << dwType << _T(", dwID:") << dwID << _T(", dwID1:") << dwID1 << _T(", connect size empty!"));
		return false;
	}

	bool ret = true;
	for (auto* connection : connects) {
		if (!connection || !connection->GetLogined()) {
			WRITE_ISCSIFILELOG(_T("FindConnectSendCommand can not send command, dwType:") << dwType << _T(", dwID:") << dwID << _T(", dwID1:") << dwID1 << _T(", connect:") << (connection ? s2ws(connection->GetRemoteIP()) : _T("NULL")) << _T(", bLogined:") << (connection ? connection->GetLogined() : false));
			continue;
		}
		ret &= FindConnectSendCommand(connection, bLocked, dwType, dwID, dwID1, dwValue, dwValue1, pData, dwDataLen, bCompress, bSave);
	}
	return ret;
}

//发送状态信息到界面
bool CAdminClient::SendUserStatusInfo(CAdminConnection* pAdminConnect, bool bLocked, bool bLogined)
{
	bool ret(false);
	if (pAdminConnect && pAdminConnect->GetLogined()) {
		auto xml = boost::make_shared<CMarkup>();
		auto pDataItem = CListDataUser::GetTempListDataUser();
		if (pDataItem && xml) {
			GetIscsiParameter().PrintDetailLog();
			CListDataUser::IscsiParaSetToListData(&GetIscsiParameter(), pDataItem);
			CListDataUser::UserStatusToListData(&m_UserStatus, pDataItem);
			CListDataUser::LockScreenParaToListData(m_LockScreenOption, pDataItem);
			CListDataUser::ScheduledCmdOptionsToListData(m_ScheduledCmdOption, pDataItem);
			CListDataUser::ScheduledCmdToListData(m_ScheduledCommand, pDataItem);
			CListDataUser::PoweronCmdToListData(m_PowerOnCommand, pDataItem);
			pDataItem->GetData(*xml);
			m_PublicStatus.RunedFunction(ADMIN_RUNED_SENDUSERCLIENT);
			ret = (bLocked ? pAdminConnect->SendCommand(iCOMMAND_NETWORK_INORMATION, *xml, bLogined)
				: pAdminConnect->SendCommand_(iCOMMAND_NETWORK_INORMATION, *xml, bLogined));
			WRITE_ISCSIFILELOG(_T("SendUserStatusInfo sended bLogined : ") << bLogined << _T(", result:") << ret);
			if (IsLockClientScreenWhenOffline()) {
				//服务器在线或者离线时，发送锁屏或者解锁屏命令
				if (ret) {
					if (bLogined) {
						ret = bLocked ? pAdminConnect->SendCommand(iCOMMAND_SUPERUSER_RESPONSE, TRUE, iCLIENT_COMMAND::iCLIENT_COMMAND_UNLOCKSCREEN)
							: pAdminConnect->SendCommand_(iCOMMAND_SUPERUSER_RESPONSE, TRUE, iCLIENT_COMMAND::iCLIENT_COMMAND_UNLOCKSCREEN);
					}
					else {
						ret = bLocked ? pAdminConnect->SendCommand(iCOMMAND_LOCK_SCREEN)
							: pAdminConnect->SendCommand_(iCOMMAND_LOCK_SCREEN);
					}
					WRITE_ISCSIFILELOG(_T("SendUserStatusInfo client :") << (bLogined ? _T("Unlock") : _T("Lock")) << _T(" screen because server online or offline, result:") << ret);
				}
			}
		}
	}
	return ret;
}
//发送所有接收到的磁盘信息列表到界面
bool CAdminClient::SendUserDiskList(CAdminConnection* pAdminConnect, bool bLocked)
{
	auto xml = boost::make_shared<CMarkup>();
	if (pAdminConnect && xml && ExportDiskList(*xml, TRUE)) {
		return (bLocked ? pAdminConnect->SendCommand(iCOMMAND_ALLDISK, *xml)
			: pAdminConnect->SendCommand_(iCOMMAND_ALLDISK, *xml));
	}
	return false;
}

bool CAdminClient::ChangeToSuperUser(bool bSuperUser)
{
	bool ret(false);
	if (IsVHDBoot() && !m_oVHDBootMng.IsTempKeepMode()) {
		//临时还原模式下，不允许切换超级，参数2 - 超级用户变普通时，是否需要还原child vhd
		ret = m_oVHDBootMng.ChangeToSuperUser(bSuperUser, m_IscsiPara);
	}
	WRITE_ISCSIFILELOG(_T("ChangeToSuperUser, IsVHDBoot : ") << IsVHDBoot() << _T(", IsTempKeepMode :") << (int)m_oVHDBootMng.IsTempKeepMode() << _T(", bSuperUser :") << bSuperUser << _T(", result:") << ret);
	return ret;
}

//是否需要上传
bool CAdminClient::IsNeedUploadSuperImage(bool bSetSame /*= false*/)
{
	bool ret(false);
	if (IsVHDBoot() && GetbSuperUser() == VHD_BOOT_MODE_NORMAL && m_VHDUpdateStatus.IsFinish()) {
		ret = m_oVHDBootMng.IsNeedUploadSuperImage(bSetSame);
	}
	return ret;
}

void CAdminClient::SetGateWay()
{
	if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)) {
		if (SetGateWay_(&GetIscsiParameter(), m_adapterinfo)) {
			PingGateway();
			m_PublicStatus.RunedFunction(ADMIN_RUNED_GATEWAY);
		}
	}
}

void CAdminClient::LoginDomain()
{
	if (GetIscsiParameter().m_bIbftBoot
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_JOINDOMAIN)) {
#if (defined(_WIN32) || defined(_WIN64))
		if (LoginDomain_()) {
#else
		if (TRUE) {
#endif
			m_PublicStatus.RunedFunction(ADMIN_RUNED_JOINDOMAIN);
		}
	}
}

std::wstring CAdminClient::GetGameDiskServerIP()
{
	std::wstring sGameDiskServerIP;
	if (/*GetBootMode() == ISHAREDISK_BOOTMODE_LOCAL_MUSTLOGIN &&*/ !GetIscsiParameter().m_sChapName.empty()
		&& !GetPersonalServerIP().empty() && IsIPString(GetPersonalServerIP())) {
		//登陆第三方个人磁盘nas
		sGameDiskServerIP = GetPersonalServerIP();
	}
	else {
		if (GetRemoteServerLogined()) {
			sGameDiskServerIP = ip2wstring(dynamic_cast<CAdminAutoReconnect*>(m_pRemoteConnect.get())->GetServerEndpoint().address().to_v4());
		}
		else {
			sGameDiskServerIP = GetServerTargetIP();
		}
	}
	return sGameDiskServerIP;
}

bool CAdminClient::LoginIscsiGameDisk()
{
	if (GetIscsiParameter().NeedLoginIscsiGameDisk()
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GAMEDISK)) {
		//		&& (GetBootMode() != ISHAREDISK_BOOTMODE_LOCAL_MUSTLOGIN || !GetIscsiParameter().m_sChapName.empty()) ) {
				//只运行一次 //客户端要登录个人漫游盘
				//矫正登录提交的IP和MAC
		auto sGameDiskServerIP = GetGameDiskServerIP();
		bool bThirdNas = (wstring2icmp(sGameDiskServerIP, GetIscsiParameter().m_sTargetIP) != 0) ? true : false;
		if (GetRemoteServerLogined()) {
			auto pAdapterNetwork = GetLoginAdapterNetwork(m_pRemoteConnect.get());
			if (pAdapterNetwork) {
				GetIscsiParameter().m_MacAddress = pAdapterNetwork->GetMacAddress();
			}
			//通知 ishare 服务器准备登录个人磁盘
			if (!bThirdNas) {
				m_pRemoteConnect->SendCommand(iCOMMAND_MOUNTDISK, GetIscsiParameter().IsDisklessBoot() ? FALSE : TRUE);
			}
		}
		if (m_ServiceStatus.LoginServerTarget(GetIscsiParameter(), sGameDiskServerIP)) {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_GAMEDISK);
			//本地启动后，个人漫游磁盘登录成功后，要提示服务器重新运行OnEnterDesktop，用于保存个人磁盘回写
			if (!GetIscsiParameter().m_sChapName.empty()) {
				// 给服务器发送进入桌面命令
				FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_ENTERDESKTOP);
#if (defined(_WIN32) || defined(_WIN64))
				SaveParaToRegister();
#endif // defined
			}
			//重新设置虚拟磁盘的客户端缓存
			if (GetIscsiParameter().GetCacheSize()) {
				Sleep_Second(3);//延时
				m_PublicStatus.ResetFunction(ADMIN_RUNED_DISKCACHE);
			}
			return true;
		}
		else {
#if (defined(_WIN32) || defined(_WIN64))
			m_dwRetryLoginIscsiDisk++;
			if (m_dwRetryLoginIscsiDisk < 30) {
				WRITE_ISCSIFILELOG(_T("LoginIscsiGameDisk run times : ") << m_dwRetryLoginIscsiDisk);
			}
			else {
				m_PublicStatus.RunedFunction(ADMIN_RUNED_GAMEDISK);
			}
#else
			m_PublicStatus.RunedFunction(ADMIN_RUNED_GAMEDISK);
#endif
		}
	}
	//else {
	//	WRITE_ISCSIFILELOG(_T("CAdminClient LoginIscsiGameDisk failed, NeedLoginIscsiGameDisk:") << GetIscsiParameter().NeedLoginIscsiGameDisk()
	//		<< _T(", ADMIN_RUNED_GATEWAY:") << m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
	//		<< _T(", ADMIN_RUNED_NEWPARAMETER:") << m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER)
	//		<< _T(", ADMIN_RUNED_LOCALPARAMETER:") << m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER)
	//		<< _T(", ADMIN_RUNED_GAMEDISK:") << m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GAMEDISK)	);
	//}
	return false;
}

bool CAdminClient::LogoutIscsiGameDisk()
{
	bool ret(false);
#if (defined(_WIN32) || defined(_WIN64))
	if (GetIscsiParameter().NeedLogoutIscsiGameDisk(ISHAREDISK_BOOTMODE_LOCAL_MUSTLOGIN)
		&& !m_ServiceStatus.GetIscsi().IsLogining1()) {
		auto sGameDiskServerIP = GetGameDiskServerIP();
		bool bThirdNas = (wstring2icmp(sGameDiskServerIP, GetIscsiParameter().m_sTargetIP) != 0) ? true : false;
		if (!bThirdNas && IsDisklessBoot()) {
			FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_UNMOUNTDISK, TRUE);
			//无盘启动下的, 从无盘服务器的个人磁盘退出
			auto sChapName = GetIscsiParameter().m_sChapName;
			GetIscsiParameter().m_sChapName = EMPTY_STRING;
			auto sChapPassword = GetIscsiParameter().m_sChapPassword;
			GetIscsiParameter().m_sChapPassword = EMPTY_STRING;
			ret = m_ServiceStatus.LoginServerTarget(GetIscsiParameter(), sGameDiskServerIP);
			if (!ret) {
				GetIscsiParameter().m_sChapName = sChapName;
				GetIscsiParameter().m_sChapPassword = sChapPassword;
			}
		}
		else {
			auto lock = m_ServiceStatus.GetIscsi().GetLoginingLock();
			ret = m_ServiceStatus.GetIscsi().UnloginAndUnmountDisk([this, bThirdNas](LPCTSTR pGameDiskServerIP) {
				if (!bThirdNas) {
					WRITE_ISCSIFILELOG(_T("CAdminClient LogoutIscsiGameDisk: send iCOMMAND_UNMOUNTDISK to server!"));
					FindConnectSendCommand_safe_all(CAdminConnection::admin_connect_server, true, iCOMMAND_UNMOUNTDISK);
				}
				}, true, sGameDiskServerIP.c_str(), bThirdNas);
		}
		if (ret) {
			//清空登录信息，避免无法重复登录
			GetIscsiParameter().m_sChapName = EMPTY_STRING;
			GetIscsiParameter().m_sChapPassword = EMPTY_STRING;
			m_PublicStatus.RunedFunction(ADMIN_RUNED_GAMEDISK);//停止再登录
		}
	}
#endif
	return ret;
}

//IDR_FILE_MPIO
void CAdminClient::LoginIscsiMpio()
{
#if (defined(_WIN32) || defined(_WIN64))
	if (GetIscsiParameter().m_bIbftBoot && GetIscsiParameter().m_bMPIOmode
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& VHD_BOOT_MODE_NORMAL == GetbSuperUser()
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_MPIO)) {
		if (m_ServiceStatus.LoginMutilServerTarget(GetIscsiParameter(), m_nMPIOID)) {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_MPIO);
		}
	}
#endif
}

void CAdminClient::SetUsersProfile()
{
	if (GetIscsiParameter().m_bIbftBoot
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_USERSPROFILE_DISK)) {
		//重新初始化profile，因为磁盘变化
#if (defined(_WIN32) || defined(_WIN64))
		if (g_JunctionPoint && g_JunctionPoint->ReinitUserProfile(m_UserStatus)) {
			FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_LOAD_PROFILE_RESPONSE);
			m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE_DISK);
		}
		else {
			m_dwRetryProfile++;
			if (m_dwRetryProfile < 3) {
				WRITE_ISCSIFILELOG(_T("SetUsersProfile fail times : ") << m_dwRetryProfile);
			}
			else {
				m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE_DISK);
			}
		}
#else
		m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE_DISK);
#endif
	}
	if (GetIscsiParameter().m_bIbftBoot
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER)/* || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER)*/)
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_USERSPROFILE)) {
#if (defined(_WIN32) || defined(_WIN64))
		//只运行一次
		if (g_JunctionPoint) {
			//初始化所有的自定义连接点
			g_JunctionPoint->InitUserProfile(m_UserStatus);
			m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE);
		}
#else
		m_PublicStatus.RunedFunction(ADMIN_RUNED_USERSPROFILE);
#endif
	}
	if (GetIscsiParameter().m_bIbftBoot
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_APPLICATIONLAYER)) {
#if (defined(_WIN32) || defined(_WIN64))
		//只运行一次
		if (g_JunctionPoint) {
			g_JunctionPoint->ReinitApplicationLayer(m_UserStatus);
			if (m_UserStatus.m_ApplicationLayer.bApplicationLayer) {
				//超级保留下，本地保留要打开
				ChangeToKeepMode(true);
			}
			m_PublicStatus.RunedFunction(ADMIN_RUNED_APPLICATIONLAYER);
		}
#else
		m_PublicStatus.RunedFunction(ADMIN_RUNED_APPLICATIONLAYER);
#endif
	}
}

bool CAdminClient::SetIPXEBootIP(boost::shared_ptr<CMarkup> xml, BOOL bFromServer, std::string sCurrentTargetIP, bool bLogined)
{
	bool bResult(false);
	//修改本地启动的设置
	if (GetbVHDVolume() || bFromServer) {
		auto pDataItem = CListDataUser::GetTempListDataUser();
		auto IscsiPara = boost::make_shared<ISCSI_PARAMETER>();
		auto pUserStatus = boost::make_shared<CLIENTUSERSTATUS>();
		if (pDataItem && xml && IscsiPara && pUserStatus) {
			pDataItem->SetData(*xml);
			CListDataUser::IscsiParaGetFromListData(pDataItem, IscsiPara.get());
			CListDataUser::UserStatusGetFromListData(pDataItem, pUserStatus.get());
			bResult = SetIPXEBootIP(IscsiPara, pUserStatus, bFromServer, sCurrentTargetIP, bLogined);
		}
	}
	return bResult;
}

bool CAdminClient::SetIPXEBootIP(ISCSI_PARAMETER_ptr IscsiPara, CLIENTUSERSTATUS_ptr pUserStatus,
	BOOL bFromServer, std::string sCurrentTargetIP, bool bLogined, bool bForceUpdate /*= false*/)
{
	auto bResult = m_oVHDBootMng.SetIPXEBootIP(sCurrentTargetIP, &GetIscsiParameter(), IscsiPara.get());
	if (bResult || bForceUpdate) {
		//提前处理客户端IP配置，避免后面的post更新不及时
		GetIscsiParameter().CheckAndUpdateClientIPConfig(IscsiPara.get(), &m_PublicStatus, boost::bind(&CAdminClient::SetGateWay, this));
	}
	//不应该因为本地IP设置错误，而不处理其他配置。
	m_ioservice.io_service_post(boost::bind(&CAdminClient::OnChangeIscsiParameter, this,
		bFromServer ? pUserStatus : CLIENTUSERSTATUS_ptr(), (bFromServer || GetbVHDVolume()) ? IscsiPara : ISCSI_PARAMETER_ptr(), bLogined));
	return bResult;
}

void CAdminClient::SetDiskCache()
{
	if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_DISKCACHE)) {
		if (IsDisklessBoot() && GetHardwareInfo().GetiCachex()) {
			if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER)) {
				//只运行一次
				if (m_ServiceStatus.SetDiskCache(GetIscsiParameter())) {
					m_PublicStatus.RunedFunction(ADMIN_RUNED_DISKCACHE);
				}
			}
		}
		else {
			//正常启动和本地启动，未安装缓存的 不需要设置
			m_PublicStatus.RunedFunction(ADMIN_RUNED_DISKCACHE);
		}
	}
}

void CAdminClient::SetDenyUsbDisk()
{
	if (
		//#if !(defined(DEBUG) || defined(_DEBUG))
		//		m_ServiceStatus.HaveChild()	&&
		//#endif
		(m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_DENYUSBDISK)) {
		//只运行一次
		if (m_ServiceStatus.SetDenyUsbDisk(GetIscsiParameter())) {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_DENYUSBDISK);
		}
	}
}

void CAdminClient::ResetNewIPConfig(Public_Status * pClientPublicStatus) {
	//IP更换，重新设置
	if (pClientPublicStatus) {
		pClientPublicStatus->ResetFunction(ADMIN_RUNED_ENUM::ADMIN_RUNED_IPFROMINI);
		pClientPublicStatus->ResetFunction(ADMIN_RUNED_ENUM::ADMIN_RUNED_NEWPARAMETER);
		pClientPublicStatus->ResetFunction(ADMIN_RUNED_ENUM::ADMIN_RUNED_PINGGATEWAY);
	}
}

//无盘启动时自动登录计算机的个人磁盘, sharefolder = "iscsi" 时直接连接， sharefolder 从 "iqn" 开头时， 连接目标
void CAdminClient::CreateIscsiPersonalDisk()
{
	if (GetIscsiParameter().m_bIbftBoot
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PERSONALSERVER)) {
		//只运行一次
		BOOL ret(FALSE);
		if (GetPersonalServerIP().size() > 0 && (wstringicmp(GetShareFolder(), _T(ISCSI_PROROCOL_NAME)) == 0
			|| string_starts_with(GetShareFolder(), _T(ISCSI_IQN_NAME)))) {
			ret = CreateIscsiPersonalDisk_();
			WRITE_ISCSIFILELOG(_T("Create iscsi Personal Disk : ") << GetPersonalServerIP() << _T(", result: ") << ret);
		}
		else {
			//共享磁盘映射交给UserClient完成
			ret = TRUE;//不需要设置
		}
		if (ret) {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_PERSONALSERVER);
		}
	}
}

//无盘启动时自动登录计算机的个人磁盘
BOOL CAdminClient::CreateIscsiPersonalDisk_()
{
	BOOL result(FALSE);
#if (defined(_WIN32) || defined(_WIN64))
	std::wstring sMutilTargetIP;
	int port(CISCSI_DEFAULT_PORT);
	auto split_path = SplitStrings2Vector(m_UserStatus.GetPersonalServerIP(), _T(":"), true);
	if (split_path.size() == 2) {
		sMutilTargetIP = split_path[0];
		port = wsting2DWORD(split_path[1]);
	}
	else {
		sMutilTargetIP = m_UserStatus.GetPersonalServerIP();
	}
	//密码不足12位时，空格补齐
	std::wstring sLoginPassword(m_UserStatus.GetLoginPassword());
	if (sLoginPassword.size() > 0 && sLoginPassword.size() < 12) {
		sLoginPassword.append(12 - sLoginPassword.size(), _T(' '));
	}
	auto sLoginName(m_UserStatus.GetLoginName());//允许登录名为空
	if (!m_ServiceStatus.GetIscsi().IsLogining1()) {
		auto lock = m_ServiceStatus.GetIscsi().GetLoginingLock();
		m_ServiceStatus.GetIscsi().Refresh();
		result = m_ServiceStatus.GetIscsi().Login(sMutilTargetIP.c_str(), port, TRUE, ws2s(sLoginName), ws2s(sLoginPassword),
			((wstringicmp(m_UserStatus.GetShareFolder(), _T(ISCSI_PROROCOL_NAME)) != 0) ? m_UserStatus.GetShareFolder().c_str() : NULL));
	}
	WRITE_ISCSIFILELOG(_T("Login for personal disk , Target IP:") << sMutilTargetIP << _T(", Port:") << port
		<< _T(", Login Name:") << sLoginName << _T(", Login Password len:") << m_UserStatus.GetLoginPassword().size() << _T(", result:") << result);
#endif
	return result;
}

void CAdminClient::OnChangeIscsiParameter(CLIENTUSERSTATUS_ptr pUserStatus, ISCSI_PARAMETER_ptr pNewIscsiPara, bool bLogined)
{
	boost::mutex::scoped_lock lock(m_mLock);
	bool bNeedSaveRegistry(false);
	if (pNewIscsiPara.get()) {
		GetIscsiParameter().CheckAndUpdateServiceClientConfig(pNewIscsiPara.get(), &m_PublicStatus,
			/*IsPEMode(),*/ bNeedSaveRegistry, m_dwRetryLoginIscsiDisk,
			boost::bind(&CAdminClient::ChangeToKeepMode, this, boost::placeholders::_1),
			/*boost::bind(&CAdminClient::SetGateWay,this),*/
			boost::bind(&CAdminClient::ChangeToMaintenanceMode, this, boost::placeholders::_1));
	}
	if (pUserStatus.get()) {
		m_UserStatus.CheckAndUpdateServiceClientStatus(pUserStatus.get(), &m_PublicStatus);
	}

	if (bNeedSaveRegistry) {
#if (defined(_WIN32) || defined(_WIN64))
		SaveParaToRegister();
#endif // defined
	}

	SaveSetToFile();

	if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_SENDUSERCLIENT)) {
		SendUserStatusInfo_safe_all(CAdminConnection::admin_local_client, bLogined);
	}
}

bool CAdminClient::SaveSetToFile(const boost::filesystem::path & pSetFile)
{
	//可能创建新的set文件，故意不检查文件是否存在
	//auto pSetFile = GetSetFilePath();
	CIniFile fSetIni;
	fSetIni.Open(pSetFile);
	SaveSet(fSetIni);
	SaveDelayRebootPara(m_UpdateOption, fSetIni, g_pDelayUpdateOptions);
	SaveDelayRebootPara(m_RebootOption, fSetIni, m_pDelayRebootOptions);
	SaveDelayRebootPara(m_ShutdownOption, fSetIni, m_pDelayShutdownOptions);
	fSetIni.SetValue(CLIENT_SET_SECTION_NAME, LISTDATA_NAME_PASSWORD, m_sServerPasswordMd5);
	auto ret = fSetIni.Save(pSetFile);
#if (defined(linux) || defined(__linux) || defined(__linux__))
	if (ret) {
		set_file_permission(pSetFile.string(), true, true, false);
	}
#endif
	return ret;
}

bool CAdminClient::LoadSetFromFile()
{
	bool bResult(false);
	auto pSetFile = GetSetFilePath();
	if (IsFile(pSetFile) && LoadSetFromFile(pSetFile)) {
		WRITE_ISCSIFILELOG(_T("LoadSetFromFile sucess : ") << pSetFile);
		bResult = true;
	}
	if (!bResult && GetbBCDVolume()) {
		pSetFile = GetSetFilePathBCD();
		bResult = IsFile(pSetFile) && LoadSetFromFile(pSetFile);
		WRITE_ISCSIFILELOG(_T("LoadSetFromFile BCD ") << pSetFile << _T(", result:") << bResult);
	}
	return bResult;
}

bool CAdminClient::LoadSetFromFile(const boost::filesystem::path & pSetFile)
{
	CIniFile fSetIni;
	if (fSetIni.Open(pSetFile)) {
		LoadSet(fSetIni);
		LoadDelayRebootPara(fSetIni, m_UpdateOption, g_pDelayUpdateOptions);
		LoadDelayRebootPara(fSetIni, m_RebootOption, m_pDelayRebootOptions);
		LoadDelayRebootPara(fSetIni, m_ShutdownOption, m_pDelayShutdownOptions);
		fSetIni.GetValue(CLIENT_SET_SECTION_NAME, LISTDATA_NAME_PASSWORD, m_sServerPasswordMd5);
		m_PublicStatus.ResetFunction(ADMIN_RUNED_DISKCACHE);
		m_PublicStatus.ResetFunction(ADMIN_RUNED_DENYUSBDISK);
		m_PublicStatus.ResetFunction(ADMIN_RUNED_MPIO);
		//m_PublicStatus.ResetFunction(ADMIN_RUNED_GAMEDISK);
		//m_PublicStatus.ResetFunction(ADMIN_RUNED_USERSPROFILE);
		m_PublicStatus.ResetFunction(ADMIN_RUNED_PERSONALSERVER);
		if (m_UserStatus.IsNeedSetBlackWhiteListDomain()) {
			m_PublicStatus.ResetFunction(ADMIN_RUNED_BLACKLIST_DOMAIN);
		}
		else {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_BLACKLIST_DOMAIN);
		}
		m_PublicStatus.RunedFunction(ADMIN_RUNED_LOCALPARAMETER);//标记为已经装载本地参数
		return true;
	}
	return false;
}

bool CAdminClient::StartMulticast(BOOL bRestart)
{
	bool bResult(false);
	if (!m_pMulticastReceiver) {
		m_pMulticastReceiver = boost::make_shared<CMulticastClient>(m_ioservice.get_io_service(), m_pSeactorPool, *this);
	}
	if (m_pMulticastReceiver) {
		bResult = m_pMulticastReceiver->StartMulticast(bRestart);
	}
	return bResult;
}

bool CAdminClient::handle_multicast_message(const CMulticastClient_ptr & pMulticastUpload, const UDPENDPOINT & sender_endpoint, const CAdminMsg_Ptr & pReceiveMsg)
{
	bool bResult(false);
	switch (pReceiveMsg->GetComType())
	{
	case iCOMMAND_ALLDISK_RESPONSE:
	{
		auto xml = pReceiveMsg->GetXml();
		if (pReceiveMsg->GetComID() && xml) {
			bResult = ImportDiskList(*xml, GetRootFolder());
			SendUserDiskList_safe_all(CAdminConnection::admin_local_client);
		}
	}
	break;
	case iCOMMAND_CLIENTCMD:
	{
		//服务器发来的客户端多播命令
		switch (pReceiveMsg->GetComID()) {
		case ISHAREDISKCLIENTCMD_REBOOT:
		{
			bResult = SafeRebootWindows();
		}
		break;
		case ISHAREDISKCLIENTCMD_POWEROFF:
		{
			bResult = SafeShutDownWindows();
		}
		break;
		}
	}
	break;
	case iCOMMAND_CHANGEIP:
	{
		//多播时，服务器主动修改IP
		if (GetbVHDVolume() || pReceiveMsg->GetComID1()) {
			bResult = SetIPXEBootIP(pReceiveMsg, ip2string(sender_endpoint.address().to_v4()), true);
		}
		else {
			//网络启动不需要修改
			bResult = true;
		}
	}
	break;
	case iCOMMAND_MULTICAST_DISK:
	{
		//开始下载镜像文件
		if ((!m_pMultiDiskImage || m_pMultiDiskImage->GetID() != pReceiveMsg->GetComID())
			&& MULTI_RECEIVE_STAGE_STOP == pMulticastUpload->GetStage()) {
			auto pDataItem = boost::make_shared<CListDataCommon>();
			auto xml = pReceiveMsg->GetXml();
			if (pDataItem && xml) {
				pDataItem->SetData(*xml);
				auto retvalue = m_GameListSet.GetVirDiskList()->get_item_all(pReceiveMsg->GetComID());
				if (RETVALUE_BOOL(retvalue)) {
					boost::filesystem::path pVHDfile(GetFilenameFromFullPath(RETVALUE_REF(retvalue)->GetOption(LISTDATADISK_DISKPATH)));
					//值捕获 与参数传值类似，值捕获的前提是变量可以拷贝，不同之处则在于，被捕获的变量在 lambda 表达式被创建时拷贝，而非调用时才拷贝：
					auto funFormat = [=]()-> bool {
						pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_FORMATDISK, pReceiveMsg->GetComID(), TRUE);
						bool ret(false);
						if (IsRestoreImgExt(pVHDfile)
							&& OnMulticastDiskStart(pDataItem->GetItemDataS(LISTDATASTATUS_STRING2),
								ip2string(sender_endpoint.address().to_v4()))) {
							MakeDirectory(GetRootFolder());
							IMAGE_DETAIL pVHDfileDetail;
							pVHDfileDetail.Set(pReceiveMsg->GetComID(),
								pDataItem->GetItemDataValU(LISTDATASTATUS_NUMBER3), \
								pDataItem->GetItemDataValU(LISTDATASTATUS_NUMBER1), \
								boost::filesystem::absolute(pVHDfile.filename(), GetRootFolder()),
								pDataItem->GetItemData(LISTDATASTATUS_STRING3));
							auto pVHDfile1 = boost::filesystem::absolute(pVHDfile.filename(), GetRootFolder());
							auto pNewDiskImage = boost::make_shared<CDiskImage>(pVHDfileDetail.GenTempFilePath());
							if (pNewDiskImage && pNewDiskImage->Create()) {
								pNewDiskImage->SetName(pVHDfileDetail.pVHDfile.filename().wstring());
								pNewDiskImage->SetSystemVersion(pVHDfileDetail.sSystemVersion);
								pNewDiskImage->SetID(pReceiveMsg->GetComID());
								pNewDiskImage->SetValidDataSize(pVHDfileDetail.dwImageFileSize);//设置文件长度
								pNewDiskImage->SetLastWriteTime(pVHDfileDetail.dwImageWriteTime);//设置文件修改时间
								pMulticastUpload->SetMsgBlockSize(pDataItem->GetItemDataValU(LISTDATASTATUS_NUMBER5));//设置包大小
								pNewDiskImage->CreateWriteBitmap(pMulticastUpload->GetMsgBlockSize());
								if (pDataItem->GetItemDataValU(LISTDATASTATUS_NUMBER4)) {
									pNewDiskImage->GetFileIndex().CreateWriteBitmap(pMulticastUpload->GetMsgBlockSize());
								}
								else {
									pNewDiskImage->GetFileIndex().Close(true, true);//无索引文件，删除之
								}
								m_pMultiDiskImage = pNewDiskImage;
								m_lImageDataMsgs.clear();
								m_nLastPosIndex = 0;
								m_nLastPosImage = 0;
								//回应准备好了接收文件
								ret = pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_DISK_RESPONSE, pReceiveMsg->GetComID(), TRUE);
							}
						}
						if (ret) {
							pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_START);
						}
						else {
							OnMulticastClose(pMulticastUpload.get(), false);
						}
						return ret;
						};
					//开始格式化
					pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_FORMAT_DISK);
					if (pReceiveMsg->GetComID1()) {
						//后台格式化
						m_ioservice.io_service_post(funFormat);
						bResult = true;
					}
					else {
						bResult = funFormat();
					}
					pMulticastUpload->UpdateTimer();
				}
			}
		}
		else if (MULTI_RECEIVE_STAGE_FORMAT_DISK == pMulticastUpload->GetStage()
			|| (MULTI_RECEIVE_STAGE_START == pMulticastUpload->GetStage()
				&& m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID())) {
			//格式化中或者开始后，等待其他客户机格式化的时候，更新最后时间，避免超时而导致复位
			pMulticastUpload->UpdateTimer();
		}
	}
	break;
	case iCOMMAND_MULTICAST_CANCELDISK:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			//取消停止
			bResult = pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_CANCELDISK_RESPONSE, pReceiveMsg->GetComID(), TRUE);
			OnMulticastClose(pMulticastUpload.get(), (pMulticastUpload->GetStage() == MULTI_RECEIVE_STAGE_END));
		}
		break;
	case iCOMMAND_MULTICAST_SHA1_START:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_INDEX) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_INDEX);
			}
			pMulticastUpload->UpdateTimer();
			bResult = true;
		}
		break;
	case iCOMMAND_MULTICAST_SHA1_CONTENT:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_INDEX) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_INDEX);
			}
			pMulticastUpload->UpdateTimer();
			bResult = m_pMultiDiskImage->WriteMessage(pReceiveMsg);
		}
		break;
	case iCOMMAND_MULTICAST_SHA1_END:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_INDEX) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_INDEX);
			}
			pMulticastUpload->UpdateTimer();
			bResult = true;
		}
		break;
	case iCOMMAND_MULTICAST_IMAGE_START:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_CONTENT) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_CONTENT);
			}
			pMulticastUpload->UpdateTimer();
			bResult = true;
		}
		break;
	case iCOMMAND_MULTICAST_IMAGE_CONTENT:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_CONTENT) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_CONTENT);
			}
			pMulticastUpload->UpdateTimer();
			bResult = m_pMultiDiskImage->WriteMessage(pReceiveMsg);
		}
		break;
	case iCOMMAND_MULTICAST_IMAGE_END:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() < MULTI_RECEIVE_STAGE_RESEND) {
				pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_RESEND);
				pMulticastUpload->UpdateTimer();
			}
			bResult = true;
		}
		break;
	case iCOMMAND_MULTICAST_IMAGE_END_RESPONSE:
		if (m_pMultiDiskImage && m_pMultiDiskImage->GetID() == pReceiveMsg->GetComID()) {
			if (pMulticastUpload->GetStage() == MULTI_RECEIVE_STAGE_END) {
				//正常停止
				OnMulticastClose(pMulticastUpload.get(), true);
			}
			bResult = true;
		}
		break;
	default:
		break;
	}
	return bResult;
}

bool CAdminClient::OnMulticastTime(CMulticastClient * pMulticastUpload, const UDPENDPOINT & sender_endpoint)
{
	// 	pMulticastUpload->RejoinGroup();
	if (m_pMultiDiskImage) {
		if (pMulticastUpload->IsNotTimeout()) {
			switch (pMulticastUpload->GetStage())
			{
			case MULTI_RECEIVE_STAGE_FORMAT_DISK:
				pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_FORMATDISK, m_pMultiDiskImage->GetID(), TRUE);
				break;
			case MULTI_RECEIVE_STAGE_START:
				pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_DISK_RESPONSE, m_pMultiDiskImage->GetID(), TRUE);
				break;
#ifdef PRINT_MULTICAST_LOG
			case MULTI_RECEIVE_STAGE_CONTENT:
				if (m_pMultiDiskImage) m_pMultiDiskImage->PrintMultiPrcent();
				break;
#endif
			case MULTI_RECEIVE_STAGE_RESEND:
			{
				// 				OnMulticastResend(pMulticastUpload, sender_endpoint);
				OnMulticastResendList(pMulticastUpload/*, sender_endpoint*/);
				if (m_lImageDataMsgs.empty()) {
					//补发完毕，开始收尾工作
					m_pMultiDiskImage->Close();
					auto sBootVhdFile = boost::filesystem::absolute(m_pMultiDiskImage->GetName(), m_pMultiDiskImage->GetImagePath().parent_path());
					auto sBootVhdFileVersion = IMAGE_DETAIL::GenVersionFilePath_V2(sBootVhdFile, m_pMultiDiskImage->GetLastWriteTime(), m_pMultiDiskImage->GetValidDataSize());
					auto isFirstRenameSuccess = m_pMultiDiskImage->RenameTo(sBootVhdFile);
					bool isSecondRenameSuccess = false;
					if (!isFirstRenameSuccess) {
						isSecondRenameSuccess = m_pMultiDiskImage->RenameTo(sBootVhdFileVersion);
					}
					if (isFirstRenameSuccess || isSecondRenameSuccess) {
						std::vector<INSTALL_VHD_FILE> lVhdFiles;
						lVhdFiles.push_back({ (isFirstRenameSuccess ? sBootVhdFile : sBootVhdFileVersion),
							m_pMultiDiskImage->GetSystemVersion_(), m_pMultiDiskImage->GetID() });
						//自动安装本地启动
						OnMulticastDiskEnd(lVhdFiles, ip2string(sender_endpoint.address().to_v4()), true);
					}
					pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_END);
					auto pNewMsg = pMulticastUpload->GetNewMsg();
					if (pNewMsg) {
						//发送完毕的最后命令
						pNewMsg->SetHead(iCOMMAND_MULTICAST_IMAGE_END, m_pMultiDiskImage->GetID(), TRUE);
						pMulticastUpload->SendMsg_(&sender_endpoint, pNewMsg);
					}
				}
				else {
					auto pNewMsg = pMulticastUpload->GetNewMsg();
					if (pNewMsg) {
						//报告自己的状态
						pNewMsg->SetHead(iCOMMAND_MULTICAST_RESEND, m_pMultiDiskImage->GetID(), TRUE);
						m_lImageDataMsgs.push_back(pNewMsg);
					}
				}
				pMulticastUpload->SendMsg_(&sender_endpoint, m_lImageDataMsgs);
			}
			break;
			case MULTI_RECEIVE_STAGE_END:
				pMulticastUpload->SendCommand_(&sender_endpoint, iCOMMAND_MULTICAST_IMAGE_END, m_pMultiDiskImage->GetID(), TRUE);
				break;
			}
		}
		else {
			//超时，强制停止
			OnMulticastClose(pMulticastUpload, false);
		}
	}
	return true;
}

bool CAdminClient::OnMulticastClose(CMulticastClient * pMulticastUpload, bool bEnd)
{
	if (pMulticastUpload) {
		pMulticastUpload->SetStage(MULTI_RECEIVE_STAGE_STOP);
	}
	if (m_pMultiDiskImage) {
		if (!bEnd) {
			//删除临时文件
			m_pMultiDiskImage->Close(true, true);
		}
		m_pMultiDiskImage.reset();
	}
	return true;
}

bool CAdminClient::OnMulticastDiskStart(const std::string & sFormatPara, const std::string & sCurrentTargetIP, DWORD * dwInstallError /*= nullptr*/)
{
#if (defined(linux) || defined(__linux) || defined(__linux__))
	OnStartFormating(true);
#endif
	MulticastDiskStartParams multicastDiskStartParams(m_ioservice.get_io_service(), sFormatPara, sCurrentTargetIP, &GetIscsiParameter(), boost::bind(&CAdminClient::SaveSetToFile, this, boost::placeholders::_1), m_GameListSet.dwMenuTimeout, dwInstallError, GetHardwareInfo().GetLanguage());
	auto result = m_oVHDBootMng.OnMulticastDiskStart(multicastDiskStartParams);
#if (defined(linux) || defined(__linux) || defined(__linux__))
	OnStartFormating(false);
#endif
	if (result) {
		RefreshVhdList();
		//发送本地vhd更新信息，会导致继续更新
		SendVHDStatus_safe();
		SendVHDStatusToDesktop_safe();
	}
	WRITE_ISCSIFILELOG(_T("OnMulticastDiskStart VHDVolume: ") << GetVHDVolume() << _T(" end, result: ") << result);
	return result;
}

bool CAdminClient::OnMulticastDiskEnd(std::string sCurrentTargetIP, bool bSendStatus, PDWORD dwErrorBit /*= NULL*/)
{
	std::vector<INSTALL_VHD_FILE> lVhdFiles;
	lVhdFiles.clear();
	lVhdFiles.resize(m_pSystemVHDfile.size());
	for (auto i = 0; i < m_pSystemVHDfile.size(); i++) {
		if (m_pSystemVHDfile[i].IsInited()) {
			boost::filesystem::path pExistVHDFile;
			if (m_pSystemVHDfile[i].CheckVHDFilePath(pExistVHDFile)) {
				WINSYSTEMVERSION oSystemVersion;
				if (m_pSystemVHDfile[i].sSystemVersion.empty()
					|| !GetSystemVersionBit(m_pSystemVHDfile[i].sSystemVersion, &oSystemVersion)) {
					//未获取到系统版本，尝试打开vhd文件获取
					GetVHDSystemVersion(pExistVHDFile, oSystemVersion);
				}
				lVhdFiles[i] = { pExistVHDFile, oSystemVersion, m_pSystemVHDfile[i].dwID };
				if (m_pSystemVHDfile[i].pRootImageDetail
					&& m_pSystemVHDfile[i].pRootImageDetail->CheckVHDFilePath(pExistVHDFile)) {
					auto pRootVhd = boost::make_shared<INSTALL_VHD_FILE>();
					if (pRootVhd) {
						pRootVhd->dwID = m_pSystemVHDfile[i].pRootImageDetail->dwID;
						pRootVhd->sBootVhdFile = pExistVHDFile;
						WINSYSTEMVERSION oRootSystemVersion;
						if (m_pSystemVHDfile[i].pRootImageDetail->sSystemVersion.empty()
							|| !GetSystemVersionBit(m_pSystemVHDfile[i].pRootImageDetail->sSystemVersion, &oRootSystemVersion)) {
							//未获取到系统版本，尝试打开vhd文件获取
							GetVHDSystemVersion(pExistVHDFile, oRootSystemVersion);
						}
						pRootVhd->oSystemVersion = oRootSystemVersion;
						lVhdFiles[i].pRootVhdFile = pRootVhd;
					}
				}
			}
		}
	}
	return OnMulticastDiskEnd(lVhdFiles, sCurrentTargetIP, bSendStatus, dwErrorBit);
}

//g_hInstance, IDR_FILE_GRUB4DOS,IDR_FILE_GRUB4DOS_OEM
bool CAdminClient::OnMulticastDiskEnd(const std::vector<INSTALL_VHD_FILE>&lVhdFiles, std::string sCurrentTargetIP, bool bSendStatus /*= true*/, PDWORD dwErrorBit /*= NULL*/)
{
	auto ret = m_oVHDBootMng.OnMulticastDiskEnd(lVhdFiles, dwErrorBit);
	if (ret && bSendStatus) {
		//本地盘启动，刷新即可
		RefreshVhdList();
		//发送本地vhd更新信息，会导致继续更新
		SendVHDStatus_safe();
		SendVHDStatusToDesktop_safe();
	}
	return ret;
}

BOOL CAdminClient::InstallVHDCmd(DWORD dwAutoupdate, std::string sFormatPara, PDWORD dwInstallError)
{
	boost::mutex::scoped_lock lock(m_mLock);
	BOOL ret(FALSE);
	if (!GetRemoteServerLogined()) return ret;

	//先停止更新，释放所有的文件句柄，避免无法格式化，或者格式化后对更新的判断错误
	auto ret1 = StopUpdateVHD();
	WRITE_ISCSIFILELOG(_T("run InstallVHDCmd, Autoupdate: ") << dwAutoupdate
		<< _T(", StopUpdateVHD: ") << ret1);
	if (ret1) Sleep_Second(3);
	ret = OnMulticastDiskStart(sFormatPara, m_pRemoteConnect->GetRemoteIP(), dwInstallError);
	if (ret) {
		//有了vhd volume
		if (m_VHDUpdateStatus.IsFinish() || m_VHDUpdateStatus.IsNeedReboot()) {
			ret = OnMulticastDiskEnd(m_pRemoteConnect->GetRemoteIP(), true, dwInstallError);
		}
		else {
			//自动开始更新
			MakeDirectory(GetRootFolder());
			//格式化磁盘后，客户端状态改变，需要检查本地是否有磁盘列表，才能更新
			m_pRemoteConnect->GetLocker();
			m_pRemoteConnect->RefreshAllDiskCommand(m_VHDUpdateStatus.IsUnknow(), NULL);
			if (dwAutoupdate) {
				//未避免新磁盘开始就无bin
				auto tCommandTime = m_UpdateOption.tCommandTime;
				m_UpdateOption.reset();//关闭延时重启
				//为避免更新出错后，中途停止，无法再依靠IsUnfinishVHDUpdateMode拉起更新，必须保存命令时间
				m_UpdateOption.tCommandTime = tCommandTime;
				ret = StartUpdateVHD(m_pRemoteConnect.get(), VHD_UPDATE_INSTALL_UPDATE_GRUB, "");
			}
		}
	}
	m_bStopUpdateVHD = FALSE; //手动停止更新取消
	return ret;
}

//bool CAdminClient::OnMulticastResend(CMulticastClient* pMulticastUpload, const UDPENDPOINT& sender_endpoint)
//{
//	const boost::dynamic_bitset<BYTE>& writebitmapIndex = boost::dynamic_pointer_cast<CDiskImage>(m_pMultiDiskImage)->GetFileIndex().GetWriteBitmap();
//	const boost::dynamic_bitset<BYTE>& writebitmapImage = boost::dynamic_pointer_cast<CDiskImage>(m_pMultiDiskImage)->GetWriteBitmap();
//	if (!m_lImageDataMsgs.empty()) {
//		for (auto iter = m_lImageDataMsgs.begin(); iter != m_lImageDataMsgs.end(); ) {
//			bool bDelete(false);
//			switch ((*iter)->GetComType())
//			{
//			case iCOMMAND_MULTICAST_SHA1_REQUEST:
//				bDelete = !writebitmapIndex.test((*iter)->GetComID1());
//				break;
//			case iCOMMAND_MULTICAST_IMAGE_REQUEST:
//				bDelete = !writebitmapImage.test((*iter)->GetComID1());
//				break;
//			case iCOMMAND_MULTICAST_RESEND:
//			default:
//				bDelete = true;
//				break;
//			}
//			if (bDelete) {
//				iter = m_lImageDataMsgs.erase(iter);
//			}
//			else {
//				iter++;
//			}
//		}
//	}
//	while (m_lImageDataMsgs.size() < pMulticastUpload->GetMaxSendMsgCount()
//		&& m_nLastPosIndex != boost::dynamic_bitset<BYTE>::npos) {
//		m_nLastPosIndex = (m_nLastPosIndex == 0) ? writebitmapIndex.find_first() : writebitmapIndex.find_next(m_nLastPosIndex);
//		if (m_nLastPosIndex != boost::dynamic_bitset<BYTE>::npos) {
//			auto pNewMsg = pMulticastUpload->GetNewMsg();
//			if (pNewMsg) {
//				//请求补发index文件
//				pNewMsg->SetHead(iCOMMAND_MULTICAST_SHA1_REQUEST, m_pMultiDiskImage->GetID(), m_nLastPosIndex);
//				m_lImageDataMsgs.push_back(pNewMsg);
//			}
//		}
//		else {
//			break;
//		}
//	}
//	while (m_lImageDataMsgs.size() < pMulticastUpload->GetMaxSendMsgCount()
//		&& m_nLastPosImage != boost::dynamic_bitset<BYTE>::npos) {
//		m_nLastPosImage = (m_nLastPosImage == 0) ? writebitmapImage.find_first() : writebitmapImage.find_next(m_nLastPosImage);
//		if (m_nLastPosImage != boost::dynamic_bitset<BYTE>::npos) {
//			auto pNewMsg = pMulticastUpload->GetNewMsg();
//			if (pNewMsg) {
//				//请求补发image文件
//				pNewMsg->SetHead(iCOMMAND_MULTICAST_IMAGE_REQUEST, m_pMultiDiskImage->GetID(), m_nLastPosImage);
//				m_lImageDataMsgs.push_back(pNewMsg);
//			}
//		}
//		else {
//			break;
//		}
//	}
//	return true;
//}

bool CAdminClient::OnMulticastResendList(CMulticastClient * pMulticastUpload/*, const UDPENDPOINT& sender_endpoint*/)
{
	const boost::dynamic_bitset<BYTE>& writebitmapIndex = boost::dynamic_pointer_cast<CDiskImage>(m_pMultiDiskImage)->GetFileIndex().GetWriteBitmap();
	const boost::dynamic_bitset<BYTE>& writebitmapImage = boost::dynamic_pointer_cast<CDiskImage>(m_pMultiDiskImage)->GetWriteBitmap();
	if (!m_lImageDataMsgs.empty()) {
		for (auto iter = m_lImageDataMsgs.begin(); iter != m_lImageDataMsgs.end(); ) {
			if (boost::dynamic_pointer_cast<CDiskImage>(m_pMultiDiskImage)->CheckMessageBitmap(*iter)) {
				iter = m_lImageDataMsgs.erase(iter);
			}
			else {
				iter++;
			}
		}
	}
	while (m_lImageDataMsgs.size() < pMulticastUpload->GetMaxSendMsgCount()
		&& m_nLastPosIndex != boost::dynamic_bitset<BYTE>::npos) {
		auto pNewMsg = pMulticastUpload->GetNewMsg();
		if (pNewMsg) {
			pNewMsg->SetHead(iCOMMAND_MULTICAST_SHA1_REQUEST_LIST, m_pMultiDiskImage->GetID());
			pNewMsg->CreateDataBuffer(pMulticastUpload->GetSeactorPool(), MULTICAST_BLOCKSIZE);
#if BOOST_VERSION >= 108700
			auto index_no_array = (PDWORD)(pNewMsg->GetDataBuffer(false).data());
#else
			auto index_no_array = boost::asio::buffer_cast<PDWORD>(pNewMsg->GetDataBuffer(false));
#endif
			DWORD i(0);
			while (i < (MULTICAST_BLOCKSIZE / sizeof(DWORD))
				&& m_nLastPosIndex != boost::dynamic_bitset<BYTE>::npos) {
				m_nLastPosIndex = (m_nLastPosIndex == 0) ? writebitmapIndex.find_first() : writebitmapIndex.find_next(m_nLastPosIndex);
				if (m_nLastPosIndex != boost::dynamic_bitset<BYTE>::npos) {
					index_no_array[i] = m_nLastPosIndex + 1;
					i++;
				}
				else {
					break;
				}
			}
			if (i > 0) {
				pNewMsg->SetDataLen(i * sizeof(DWORD));
				WRITE_ISCSIFILELOG_DWORD(_T("Multicast make resend index file, ID list:"), index_no_array, i);
				m_lImageDataMsgs.push_back(pNewMsg);
			}
			else {
				break;
			}
		}
	}
	while (m_lImageDataMsgs.size() < pMulticastUpload->GetMaxSendMsgCount()
		&& m_nLastPosImage != boost::dynamic_bitset<BYTE>::npos) {
		auto pNewMsg = pMulticastUpload->GetNewMsg();
		if (pNewMsg) {
			pNewMsg->SetHead(iCOMMAND_MULTICAST_IMAGE_REQUEST_LIST, m_pMultiDiskImage->GetID());
			pNewMsg->CreateDataBuffer(pMulticastUpload->GetSeactorPool(), MULTICAST_BLOCKSIZE);
#if BOOST_VERSION >= 108700
			auto index_no_array = (PDWORD)(pNewMsg->GetDataBuffer(false).data());
#else
			auto index_no_array = boost::asio::buffer_cast<PDWORD>(pNewMsg->GetDataBuffer(false));
#endif
			DWORD i(0);
			while (i < (MULTICAST_BLOCKSIZE / sizeof(DWORD))
				&& m_nLastPosImage != boost::dynamic_bitset<BYTE>::npos) {
				m_nLastPosImage = (m_nLastPosImage == 0) ? writebitmapImage.find_first() : writebitmapImage.find_next(m_nLastPosImage);
				if (m_nLastPosImage != boost::dynamic_bitset<BYTE>::npos) {
					index_no_array[i] = m_nLastPosImage + 1;
					i++;
				}
				else {
					break;
				}
			}
			if (i > 0) {
				pNewMsg->SetDataLen(i * sizeof(DWORD));
				WRITE_ISCSIFILELOG_DWORD(_T("Multicast make resend image file , ID list:"), index_no_array, i);
				m_lImageDataMsgs.push_back(pNewMsg);
			}
			else {
				break;
			}
		}
	}
	return true;
}

std::string CAdminClient::CaculPasswordMd5(std::string sPassword)
{
	std::string sPasswordMd5;
	if (sPassword.empty()) {
		sPasswordMd5 = EMPTY_STRING_MD5;
	}
	else {
		MD5 md5((const unsigned char*)sPassword.c_str(), sPassword.size());
		sPasswordMd5 = md5.hex_digest();
	}
	return sPasswordMd5;
}

BOOL CAdminClient::SetIPFromIni()
{
#if (defined(_WIN32) || defined(_WIN64))

	//设置自定义IP
	std::list<boost::filesystem::path> lIPPaths;
	auto addPathIfNotEmpty = [&](const boost::filesystem::path& path) {
		if (CIniFile::IsNotEmpty(path)) {
			lIPPaths.push_back(path);
		}
		};

	if (GetbVHDVolume()) {
		addPathIfNotEmpty(boost::filesystem::absolute(IPINI_NAME, GetRootFolder()));
	}
	if (g_JunctionPoint && !g_JunctionPoint->GetUserDataRoot().empty()) {
		addPathIfNotEmpty(boost::filesystem::absolute(IPINI_NAME, g_JunctionPoint->GetUserDataRoot()));
	}
	addPathIfNotEmpty(boost::filesystem::absolute(IPINI_NAME, GetModuleFolder()));

	BOOL bSetIP = TRUE;  //默认没得ip.ini，为返回正确
	if (!lIPPaths.empty()) {
		if (!m_oIPFromIni) {
			m_oIPFromIni = std::make_shared<CIPFromIni>(m_adapterinfo, GetIscsiParameter().m_MacAddress);
		}
		if (m_oIPFromIni) {
			bSetIP = m_oIPFromIni->ChangeIPFromIni(lIPPaths);
			//读取虚拟磁盘版本的服务器IP
			if (GetIscsiParameter().IsNormalBoot() && !GetIscsiParameter().IsValidServerTargetIP()) {
				GetIscsiParameter().SetServerTargetIP(m_oIPFromIni->GetTargetIPFromIni(lIPPaths));
			}
		}
	}

	if ((!bSetIP || lIPPaths.empty()) && m_adapterinfo.AdapterCount() > 0) {
		if (IsVHDBoot()) {
			//15秒改一次，避免修改ip过快导致网卡未准备好，出错
			if (m_nOnTimeCount % 15 == 0 || m_nOnTimeCount == 1) {
				GetIscsiParameter().UpdateIscsiParameterFromAdapter(m_adapterinfo);
				//ip 不正确 或者是 动态ip问题, 15秒后强制设置一次, 第一次不强制设置
				auto nFindIndex = m_adapterinfo.FindByIP(ip2string(GetIscsiParameter().m_dwClientIP), true);
				if (!GetIscsiParameter().m_bLocalDHCP && (0 != GetIscsiParameter().m_dwClientIP)
					&& (CADAPTERINFO_ERROR_INDEX == nFindIndex
						|| m_adapterinfo.HaveDHCPIP() || m_adapterinfo.HavePrivateIP() || m_nOnTimeCount > 1)) {
					//检查IP是否正确，适用于VHD启动，USB网卡的情况
					for (int i = 0; i < m_adapterinfo.AdapterCount(); i++) {
						auto pAdapter = m_adapterinfo.GetAdapter(i);
						if (!pAdapter->bDisable
							&& (m_adapterinfo.AdapterCount() == 1
								|| !GetIscsiParameter().m_MacAddress.is_valid()
								|| GetIscsiParameter().m_MacAddress == CMac(&pAdapter->Address[0], sizeof(pAdapter->Address))
								|| i == m_adapterinfo.AdapterCount() - 1)) {
							WRITE_ISCSIFILELOG(_T("Change ip to iscsi parameter, IP : ") << ip2wstring(GetIscsiParameter().m_dwClientIP).c_str());
							//修正IP不正确的问题
							if (m_adapterinfo.HavePrivateIP()) {
								//先复位，删除 168 等内部地址, 在第一次后，15秒后再执行，太早执行会导致自己产生内部地址
								netshIPreset();
							}
							GetIscsiParameter().SetNewIP(pAdapter, false);
							//刷新IP信息
							Sleep_Second();//延时
							InitAdapter(false);
							PingGateway();
							//只修改一个IP，退出内循环 i, 因为，m_adapterinfo 已经刷新，
							//在外循环时，再次检查是否需要修改，不需要就退出外循环j
							bSetIP = TRUE;
							break;
						}
					}
				}
				else if (LOCAL_DHCP_ALL != GetIscsiParameter().m_bLocalDHCP && CADAPTERINFO_ERROR_INDEX != nFindIndex
					&& !GetIscsiParameter().CheckDNSConfig(m_adapterinfo.GetAdapter(nFindIndex))) {
					if (LOCAL_DHCP_IP_ONLY == GetIscsiParameter().m_bLocalDHCP) {
						GetIscsiParameter().SetDHCPIP(m_adapterinfo.GetAdapter(nFindIndex));
					}
					else {
						//dns不同，修改dns, 超级用户时，全部修改
						GetIscsiParameter().SetNewIP(m_adapterinfo.GetAdapter(nFindIndex), VHD_BOOT_MODE_SUPER != GetbSuperUser());
					}
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
					PingGateway();
					bSetIP = TRUE;
				}
				else if (GetIscsiParameter().m_bLocalDHCP
					&& (m_adapterinfo.HavePrivateIP() || !m_adapterinfo.HaveDHCPIP() || m_nOnTimeCount > 1)) {
					//dhcp分配错误
					for (int i = 0; i < m_adapterinfo.AdapterCount(); i++) {
						auto pAdapter = m_adapterinfo.GetAdapter(i);
						if (!pAdapter->bDisable && (pAdapter->HavePrivateIP() || pAdapter->IsDhcpEnabled()
							|| m_adapterinfo.AdapterCount() == 1
							|| !GetIscsiParameter().m_MacAddress.is_valid()
							|| GetIscsiParameter().m_MacAddress == CMac(&pAdapter->Address[0], sizeof(pAdapter->Address))
							|| i == m_adapterinfo.AdapterCount() - 1)) {
							if (m_adapterinfo.HavePrivateIP()) {
								//每三次复位一次网卡，避免 netsh interface ip set address "13" dhcp 失效
								//先复位，删除 168 等内部地址, 在第一次后，15秒后再执行，太早执行会导致自己产生内部地址
								netshIPreset();
							}
							WRITE_ISCSIFILELOG(_T("Change ip to DHCP IP : ") << s2ws(pAdapter->AdapterName).c_str());
							//修正DHCP, IP不正确的问题
							GetIscsiParameter().SetDHCPIP(pAdapter);
							//刷新IP信息
							Sleep_Second();//延时
							InitAdapter(false);
							PingGateway();
							//只修改一个IP，退出内循环 i, 因为，m_adapterinfo 已经刷新，
							//在外循环时，再次检查是否需要修改，不需要就退出外循环j
							bSetIP = TRUE;
							break;
						}
					}
				}
			}
		}
		else if (IsDisklessBoot()) {
			auto sIPstring = ip2string(GetIscsiParameter().m_dwClientIP);
			auto nIndex = m_adapterinfo.FindByIP(sIPstring);
			auto pAdapter = m_adapterinfo.GetAdapter(nIndex);
			if (CADAPTERINFO_ERROR_INDEX != nIndex && pAdapter) {
				if (!pAdapter->IsDhcpEnabled() && GetIscsiParameter().m_bLocalDHCP) {
					if (m_adapterinfo.HavePrivateIP()) {
						//每三次复位一次网卡，避免 netsh interface ip set address "13" dhcp 失效
						//先复位，删除 168 等内部地址, 在第一次后，15秒后再执行，太早执行会导致自己产生内部地址
						WRITE_ISCSIFILELOG(_T("because have private IP, netshIPreset : ") << s2ws(pAdapter->AdapterName).c_str());
						netshIPreset();
					}
					auto ret1 = pAdapter->SetDHCPIP();
					WRITE_ISCSIFILELOG(_T("because local DHCP, Change to DHCP IP : ") << s2ws(sIPstring).c_str() << _T(", result : ") << ret1);
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
				}
				else if (pAdapter->IsDhcpEnabled() && !GetIscsiParameter().m_bLocalDHCP) {
					//由于无盘启动开始没有网关设置，可以从网卡的dhcp里获取
					GetIscsiParameter().UpdateIscsiParameterFromAdapter(pAdapter);
					auto ret1 = GetIscsiParameter().SetNewIP(pAdapter, false);
					WRITE_ISCSIFILELOG(_T("because local DHCP is false, Change to static IP : ") << s2ws(sIPstring).c_str() << _T(", result : ") << ret1);
					if (!ret1) {
						//从dhcp转换到静态IP错误时，会丢失网关，这里复位重新设置
						m_PublicStatus.ResetFunction(ADMIN_RUNED_GATEWAY);
					}
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
				}
				else if (pAdapter->GetIPCount() > 1) {
					//删除多余IP
					WRITE_ISCSIFILELOG(_T("because mutli IP count : ") << pAdapter->GetIPCount() << _T(", remove	other IP !"));
					for (int i = 0; i < MAX_ADAPTER_IP_COUNT; i++) {
						if (IsIPString(pAdapter->IPAddressMaskList[i].IpAddress.String)
							&& stringicmp(sIPstring, pAdapter->IPAddressMaskList[i].IpAddress.String) != 0) {
							netshDeleteIP(IsXp2003Version() ? GetAdapterFriendName(s2ws(pAdapter->AdapterName)) : DWORD2wstring(pAdapter->Index),
								s2ws(pAdapter->IPAddressMaskList[i].IpAddress.String));
						}
					}
					//auto ret1 = GetIscsiParameter().SetNewIP(pAdapter, false);
					////恢复正确的IP信息
					//WRITE_ISCSIFILELOG(_T("Change to iscsi parameter, IP : ") << s2ws(sIPstring).c_str() << _T(", result : ") << ret1);
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
				}
				else if (!GetIscsiParameter().CheckDNSConfig(pAdapter)) {
					auto ret1 = GetIscsiParameter().SetNewIP(pAdapter, true);
					//dns不同，修改dns
					WRITE_ISCSIFILELOG(_T("because DNS different, Change to iscsi parameter, IP : ") << s2ws(sIPstring).c_str() << _T(", result : ") << ret1);
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
				}
			}
			//删除多余IP后
			bSetIP = TRUE;
		}
		else {
			//正常情况
			bSetIP = TRUE;
		}
	}
	else {
		//无盘启动等其他情况，默认为TRUE，避免无限循环修改IP
		bSetIP = TRUE;
	}
	return bSetIP;

#else

	BOOL bSetIP(TRUE); //默认没得ip.ini，为返回正确
	//由于linux启动时网卡未准备好，导致 unavailable， EnabledAdapterCount 返回0，故修改判断条件
	//add new device by nmcli,  name: eno1, type: ethernet, State: unavailable, UUID:
	if ((IsPEMode() || IsVHDBoot()) && m_adapterinfo.AdapterCount() > 0) {
		//vhd启动时，再次检查IP
		for (int j = 0; j < 3; j++) {
			if (!GetIscsiParameter().m_bLocalDHCP && (0 != GetIscsiParameter().m_dwClientIP)
				&& (CADAPTERINFO_ERROR_INDEX == m_adapterinfo.FindByIP(ip2string(GetIscsiParameter().m_dwClientIP)))) {
				WRITE_ISCSIFILELOG(_T("need Change ip to iscsi parameter, IP : ") << ip2wstring(GetIscsiParameter().m_dwClientIP).c_str());
				bSetIP = FALSE;
				bool bFound(false);
				//检查IP是否正确，适用于VHD启动，USB网卡的情况
				for (std::size_t i = 0; i < m_adapterinfo.AdapterCount(); i++) {
					auto pAdapter = m_adapterinfo.GetAdapter(i);
					if (!pAdapter->bDisable && !pAdapter->bWifi &&
						(m_adapterinfo.AdapterCount() == 1
							|| !GetIscsiParameter().m_MacAddress.is_valid()
							|| GetIscsiParameter().m_MacAddress == CMac(&pAdapter->Address[0], sizeof(pAdapter->Address))
							|| i == m_adapterinfo.AdapterCount() - 1)) {
						//修正IP不正确的问题
						bSetIP = GetIscsiParameter().SetNewIP(pAdapter, false);
						WRITE_ISCSIFILELOG(_T("Change ip to iscsi parameter, Name : ") << s2ws(pAdapter->AdapterName).c_str()
							<< _T(", Description: ") << s2ws(pAdapter->Description).c_str()
							<< _T(", IP : ") << ip2wstring(GetIscsiParameter().m_dwClientIP).c_str()
							<< _T(", Gateway : ") << ip2wstring(GetIscsiParameter().m_dwDHCPGateway).c_str()
							<< _T(", result : ") << bSetIP);
						//刷新IP信息
						Sleep_Second();//延时
						InitAdapter(false, IsPEMode());
						PingGateway();
						bFound = true;
						break;
					}
					else {
						WRITE_ISCSIFILELOG(_T("adapter can not changed , adapter no: ") << i);
						pAdapter->PrintLog();
					}
				}
				if (!bFound) {
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false, IsPEMode());
				}
				else {
					if (bSetIP) {
						auto nIndex = m_adapterinfo.FindByIP(ip2string(GetIscsiParameter().m_dwClientIP));
						if (CADAPTERINFO_ERROR_INDEX != nIndex) break; //退出外循环
					}
				}
			}
			//PE 模式下无地址，需要设置为dhcp获取地址
			else if ((GetIscsiParameter().m_bLocalDHCP
				|| (IsPEMode() && !m_adapterinfo.ValidAdapterCount()))
				&& (m_adapterinfo.HavePrivateIP() || !m_adapterinfo.HaveDHCPIP())) {
				WRITE_ISCSIFILELOG(_T("need Change ip to DHCP."));
				//dhcp分配错误
				bool bFound(false);
				for (int i = 0; i < m_adapterinfo.AdapterCount(); i++) {
					auto pAdapter = m_adapterinfo.GetAdapter(i);
					if (!pAdapter->bDisable && !pAdapter->bWifi &&
						(pAdapter->HavePrivateIP()
							|| pAdapter->IsDhcpEnabled()
							|| m_adapterinfo.AdapterCount() == 1
							|| !GetIscsiParameter().m_MacAddress.is_valid()
							|| GetIscsiParameter().m_MacAddress == CMac(&pAdapter->Address[0], sizeof(pAdapter->Address))
							|| i == m_adapterinfo.AdapterCount() - 1)) {
						//修正DHCP, IP不正确的问题
						bSetIP = pAdapter->SetDHCPIP();
						WRITE_ISCSIFILELOG(_T("Change ip to DHCP adapter : ") << s2ws(pAdapter->AdapterName).c_str()
							<< _T(", Description: ") << s2ws(pAdapter->Description).c_str()
							<< _T(", result : ") << bSetIP);
						//刷新IP信息
						Sleep_Second();//延时
						InitAdapter(false, IsPEMode());
						PingGateway();
						bFound = true;
						break;
					}
					else {
						WRITE_ISCSIFILELOG(_T("adapter can not changed , adapter no: ") << i);
						pAdapter->PrintLog();
					}
				}
				if (!bFound) {
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false, IsPEMode());
				}
				else {
					if (bSetIP) {
						if (m_adapterinfo.HaveDHCPIP()) break; //退出外循环
					}
				}
			}
			else {
				auto nIndex = m_adapterinfo.FindByIP(ip2string(GetIscsiParameter().m_dwClientIP));
				WRITE_ISCSIFILELOG(_T("NO ip need change! m_bLocalDHCP :") << GetIscsiParameter().m_bLocalDHCP
					<< _T(" ,m_dwClientIP :") << ip2wstring(GetIscsiParameter().m_dwClientIP).c_str()
					<< _T(" , find at index: ") << nIndex);
				break; //退出外循环
			}
		}
	}
	else {
		WRITE_ISCSIFILELOG(_T("NO ip need change! IsVHDBoot:") << IsVHDBoot()
			<< _T(" ,AdapterCount:") << m_adapterinfo.AdapterCount());
	}
	return bSetIP;

#endif
}

void CAdminClient::RunSetIPFromIni(BOOL bFirst)
{
	//连上服务器或者ping通网关后，就停止检查IP
	if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_IPFROMINI)
		|| !(m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER)
			|| m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PINGGATEWAY))) {
		if (SetIPFromIni()) {
			if (IsVHDBoot() && GetIscsiParameter().UpdateIscsiParameterFromAdapter(m_adapterinfo)) {
#if (defined(_WIN32) || defined(_WIN64))
				SaveParaToRegister(true);
#endif
			}
			m_PublicStatus.RunedFunction(ADMIN_RUNED_IPFROMINI);
		}
	}
#if (defined(_WIN32) || defined(_WIN64))
	if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_IPFROMINI)
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER)
			|| m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PINGGATEWAY))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_RESETIP)) {
		if (m_nOnTimeCount % 15 == 0 && m_nOnTimeCount > 1) {
			InitAdapter(false);//检查是否有168私有IP
			auto nFindIndex = m_adapterinfo.FindByIP(ip2string(GetIscsiParameter().m_dwClientIP), true);
			//15秒后，再次修正网卡pnp时遗留的168地址问题和清除超级用户下的历史IP
			if ((IsVHDBoot() || IsDisklessBoot())
				&& (VHD_BOOT_MODE_SUPER == GetbSuperUser() || m_adapterinfo.HavePrivateIP() || CADAPTERINFO_ERROR_INDEX == nFindIndex)) {
				if (CADAPTERINFO_ERROR_INDEX != nFindIndex) {
					//超级用户下复位网卡IP，删除历史IP
					std::wstring sScript;
					//windows 11, server 2025 无盘启动时，在超级用户下 不清除历史IP
					if ((!IsWin11Version() || !IsDisklessBoot())
						&& VHD_BOOT_MODE_SUPER == GetbSuperUser()) {
						netshIPresetScript(sScript);
					}
					//DHCP下可以清除历史IP，所以尽量设置为动态IP
					if (GetIscsiParameter().m_bLocalDHCP) {
						//dhcp分配错误
						GetIscsiParameter().SetDHCPIPScript(m_adapterinfo.GetAdapter(nFindIndex), sScript);
					}
					else if (IsDisklessBoot() && VHD_BOOT_MODE_SUPER == GetbSuperUser()) {
						//超级用户下，清除历史IP
						m_adapterinfo.GetAdapter(nFindIndex)->SetDHCPIPScript(sScript);
					}
					else {
						GetIscsiParameter().SetNewIPScript(m_adapterinfo.GetAdapter(nFindIndex), sScript);
					}
					netshRunScript(sScript);
					//刷新IP信息
					Sleep_Second();//延时
					InitAdapter(false);
					WRITE_ISCSIFILELOG(_T("Clear IP history!"));
				}
				else {
					//找不到IP，重新设置
					ResetNewIPConfig(&m_PublicStatus);
					WRITE_ISCSIFILELOG(_T("Set IP to iscsi parameter again!"));
					return;
				}
			}
			m_PublicStatus.RunedFunction(ADMIN_RUNED_RESETIP);
		}
	}
#endif
}

#if (defined(_WIN32) || defined(_WIN64))
BOOL CAdminClient::OnHardwareChanged(WPARAM wParam, LPARAM lParam)
{
	switch (wParam)
	{
	case DBT_DEVICEARRIVAL:
	case DBT_DEVNODES_CHANGED:
	{
		//WRITE_ISCSIFILELOG(_T("Diks Hardware changed, WM_DEVICECHANGE wParam : ") << wParam);
		m_dwRetryProfile = 0;
		m_PublicStatus.ResetFunction(ADMIN_RUNED_USERSPROFILE_DISK);
	}
	break;
	default:
		break;
	}
	return TRUE;
}

//https://github.com/paulmarsy/VncPassword.git
#define TIGHTVNC_KEY_PATH _T("SOFTWARE\\TightVNC\\Server")
bool CAdminClient::UpdateTightVNCPassword(const std::string & sPassword, DWORD dwClientListenPort)
{
	bool ret = false;
	if (!sPassword.empty()) {
		const static unsigned short MAXPWLEN = 8;
		unsigned char FixedKey[8] = { 23, 82, 107, 6, 35, 78, 88, 7 };
		// Pad password with nulls
		unsigned char encryptedPasswd[MAXPWLEN];
		for (unsigned int i = 0; i < MAXPWLEN; i++) {
			if (i < sPassword.length())
				encryptedPasswd[i] = sPassword[i];
			else
				encryptedPasswd[i] = 0;
		}
		// Create the obfuscated VNC key
		deskey(FixedKey, EN0);
		des(encryptedPasswd, encryptedPasswd);

		ret = SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("UseVncAuthentication"), (DWORD)TRUE)
			&& SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("Password"), encryptedPasswd, sizeof(encryptedPasswd))
			&& SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("PasswordViewOnly"), encryptedPasswd, sizeof(encryptedPasswd))
			&& SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("UseControlAuthentication"), (DWORD)TRUE)
			&& SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("ControlPassword"), encryptedPasswd, sizeof(encryptedPasswd));
	}
	if (dwClientListenPort) {
		DWORD dwClientListenPortOld = 0;
		if (ReadRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("RfbPort"), dwClientListenPortOld)) {
			if (dwClientListenPort != dwClientListenPortOld) {
				ret = SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("RfbPort"), dwClientListenPort);
			}
		}
		else {
			ret = SetRegValue(HKEY_LOCAL_MACHINE, TIGHTVNC_KEY_PATH, _T("RfbPort"), dwClientListenPort);
		}
	}
	return ret;
}

void CAdminClient::GetPageFileSizes(const boost::filesystem::path & pPageFilePath, DWORD dwPageFileSize, DWORD & initialSize, DWORD & maximumSize) {
	if (IsOEMICloud()) {
		DWORD64 dwDiskCapacity = 0, dwFree = 0;
		FileSystemFreeSize(pPageFilePath.root_path().wstring(), dwDiskCapacity, dwFree);
		// 硬盘小于 40GB 的初始 8G，否则 16GB，文件最大大小 16GB
		initialSize = (dwDiskCapacity < (GB_BYTE * 40)) ? 8192 : 16384;
		maximumSize = 16384;
	}
	else if (IsOEMRuiwo()) {
		// 4G 至 64G
		initialSize = 4096;
		maximumSize = 65536;
	}
	else {
		// 内存小于 1024MB 时，按照内存大小创建，否则初始大小 1024MB，文件最大大小 16GB
		initialSize = (dwPageFileSize < 1024) ? dwPageFileSize : 1024;
		maximumSize = (dwPageFileSize * 2 < 16384) ? (dwPageFileSize * 2) : 16384;
	}
}

void CAdminClient::CreatePageFile()
{
	if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PAGEFILE) && m_PublicStatus.IsRunedFunction(ADMIN_RUNED_DISKCACHE)) {
		MEMORYSTATUSEX statex = { 0 };
		statex.dwLength = sizeof(statex);
		GlobalMemoryStatusEx(&statex);
		//ullTotalPhys：物理内存的总大小，以字节为单位。
		//ullTotalPageFile：交换文件的总大小，以字节为单位。包括物理内存和页面文件的总和
		//ullAvailPageFile：可用的交换文件的大小，以字节为单位。表示当前系统中可用的页面文件大小，即尚未被使用的虚拟内存空间。
		//ullTotalVirtual：进程的虚拟地址空间的总大小，以字节为单位。
		WRITE_ISCSIFILELOG(_T("GlobalMemoryStatusEx, ullTotalPhys:") << statex.ullTotalPhys
			<< _T(", ullTotalPageFile:") << statex.ullTotalPageFile
			<< _T(", ullAvailPageFile:") << statex.ullAvailPageFile
			<< _T(", ullTotalVirtual:") << statex.ullTotalVirtual);
		//#ifdef DEBUG
		//		if (TRUE) {
		//#else
		if (statex.ullTotalPhys >= statex.ullTotalPageFile || statex.ullTotalPageFile <= statex.ullAvailPageFile) {
			//#endif // DEBUG
						//无盘启动时，必须有本地文件缓存时，才把页面文件也放在本地，其他启动模式，可以把页面文件放在本地
			auto bCreateOnLocalDisk = (!IsDisklessBoot() || GetHardwareInfo().GetiCachex() && GetFileCacheValid()) ? TRUE : FALSE;
			//创建页面文件
			boost::filesystem::path pPageFilePath;
			if (bCreateOnLocalDisk) {
				//先放到本地盘，开放的用户目录
				auto pDefaultPageFolder = boost::filesystem::absolute(PAGEFILE_FOLDER, GetOperatingSystemRootPath());
				if (g_JunctionPoint && !g_JunctionPoint->GetUserDataRoot().empty()) {
					pPageFilePath = boost::filesystem::absolute(PAGEFILE_NAME, g_JunctionPoint->GetUserDataRoot().root_path());
				}
				else if (IsJunctionPoint(pDefaultPageFolder.c_str(), TRUE)) {
					pPageFilePath = boost::filesystem::absolute(PAGEFILE_NAME, pDefaultPageFolder);
				}
				//if (pPageFilePath.empty() && m_oVHDBootMng.GetbVHDVolume()) {
				//	//再放到本地盘的vhd启动目录, 创建装载点，最后的措施
				//	auto sVolumeName = CheckLastSeparator(m_oVHDBootMng.GetVHDVolume().wstring(), true);
				//	std::list<std::wstring> szVolumePathName;
				//	std::wstring sPagefileMountPoint;
				//	if (GetVolumeMountPoint(sVolumeName.c_str(), szVolumePathName)) {
				//		WRITE_ISCSIFILELOG(_T("Create pagefile for vhd boot volume: ") << sVolumeName << _T(", Volume Path size:") << szVolumePathName.size());
				//		std::for_each(szVolumePathName.begin(), szVolumePathName.end(), [&sPagefileMountPoint](const std::wstring& val) {
				//			WRITE_ISCSIFILELOG(_T(", Volume Path: ") << val);
				//			if (sPagefileMountPoint.empty() || IsVolumePath(val)) {
				//				sPagefileMountPoint = val;
				//			}
				//		});
				//	}
				//	if (sPagefileMountPoint.empty()) {
				//		if (!DiskHided(sVolumeName)) {
				//			//装载一个盘符
				//			sPagefileMountPoint = GetVolumeNextLetter();
				//		}
				//		else {
				//			auto pMountPoint = boost::filesystem::absolute(_T("pagefile"), GetOperatingSystemRootPath());
				//			RemoveDir(pMountPoint);
				//			MakeDirectory(pMountPoint);
				//			::SetFileAttributes(pMountPoint.c_str(), FILE_ATTRIBUTE_HIDDEN);
				//			sPagefileMountPoint = CheckLastSeparator(pMountPoint.wstring(), true);
				//		}
				//		if (::SetVolumeMountPoint(sPagefileMountPoint.c_str(), sVolumeName.c_str())) {
				//			pPageFilePath = boost::filesystem::absolute(PAGEFILE_NAME, sPagefileMountPoint);
				//		}
				//		else {
				//			WRITE_ISCSIFILELOG(_T("SetVolumeMountPoint error:  ") << GetLastError() <<
				//				_T(", lpszVolumeMountPoint:") << sPagefileMountPoint.c_str() << _T(", lpszVolumeName:") << sVolumeName.c_str());
				//		}
				//	}
				//	else {
				//		pPageFilePath = boost::filesystem::absolute(PAGEFILE_NAME, sPagefileMountPoint);
				//	}
				//}
			}
			if (pPageFilePath.empty() && !IsVHDBoot()) {
				//非本地启动时，可以放到C盘根目录
				pPageFilePath = boost::filesystem::absolute(PAGEFILE_NAME, GetOperatingSystemRootPath());
			}
			if (!pPageFilePath.empty()) {
				DWORD initialSize(0), maximumSize(0);
				GetPageFileSizes(pPageFilePath, (DWORD)(statex.ullTotalPhys / 1024 / 1024), initialSize, maximumSize);
				CreateWindowsPageFile(pPageFilePath, initialSize, maximumSize);
			}
			if (m_nOnTimeCount < 30) {
				WRITE_ISCSIFILELOG(_T("CreatePageFile run times : ") << m_nOnTimeCount);
			}
			else {
				m_PublicStatus.RunedFunction(ADMIN_RUNED_PAGEFILE);
			}
		}
		else {
			//检查并删除盘符
			if (m_oVHDBootMng.GetbVHDVolume()) {
				auto sVolumeName = CheckLastSeparator(m_oVHDBootMng.GetVHDVolume().wstring(), true);
				if (DiskHided(sVolumeName)) {
					std::list<std::wstring> szVolumePathName;
					if (GetVolumeMountPoint(sVolumeName.c_str(), szVolumePathName)) {
						WRITE_ISCSIFILELOG(_T("Delete mount point for vhd boot volume: ") << sVolumeName << _T(", Volume Path size:") << szVolumePathName.size());
						std::for_each(szVolumePathName.begin(), szVolumePathName.end(), [](const std::wstring& val) {
							BOOL bDeleted(FALSE);
							if (IsVolumePath(val)) {
								bDeleted = ::DeleteVolumeMountPoint(val.c_str());
							}
							WRITE_ISCSIFILELOG(_T(", Volume Path: ") << val << _T(", bDeleted: ") << bDeleted);
							});
					}
				}
			}
			m_PublicStatus.RunedFunction(ADMIN_RUNED_PAGEFILE);
		}
	}
	return;
}

#endif // defined

void CAdminClient::RunSlmgr()//注册windows
{
	if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_WINDOWSKEY)) {
		if (!m_UserStatus.m_sWindowsKey.empty()) {
#if (defined(_WIN32) || defined(_WIN64))
			auto sModuleFolder(GetModuleFolder());
			auto pAutoBatFile = boost::filesystem::absolute(_T("slmgr.bat"), sModuleFolder);
			//https://www.jb51.net/os/windows/571184.html
			auto sCsript = wstring_format(_T("cscript /nologo %%systemroot%%\\system32\\slmgr.vbs -upk\r\n\
cscript /nologo %%systemroot%%\\system32\\slmgr.vbs -cpky\r\n\
cscript /nologo %%systemroot%%\\system32\\slmgr.vbs -ipk %s\r\n"), m_UserStatus.m_sWindowsKey.c_str());
			if (!IsOEMICloud()) {
				sCsript += _T("cscript /nologo %systemroot%\\system32\\slmgr.vbs -ato\r\n");
			}
			if (CMarkup::WriteTextFile(pAutoBatFile.c_str(), sCsript)) {
				if (IsProcessRunAsAdmin()) {
					WaitForEndCreateProcess(pAutoBatFile.c_str(), NULL, 0, SW_HIDE, FALSE, sModuleFolder.c_str());
					WRITE_ISCSIFILELOG(_T("windows key enter at : ") << pAutoBatFile.c_str());
				}
			}
#else
			//https://faq.uniontech.com/desktop/sysmain/activation/3d41
			//使用 uos-activator-cmd [-a/-A] [xxxx-xxxx-xxxx-xxxx]  激活系统
			auto sCommand = string_format("uos-activator-cmd -A %s", ws2s(m_UserStatus.m_sWindowsKey).c_str());
			auto result = system(sCommand.c_str());
			WRITE_ISCSIFILELOG(_T("uos key enter at : ") << sCommand.c_str() << _T(", result : ") << result);
#endif
		}
		m_PublicStatus.RunedFunction(ADMIN_RUNED_WINDOWSKEY);
	}
}

void CAdminClient::RunAutoBat()
{
	if ((m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_AUTOBAT)) {
		RunAutobatCommand(GetbService());
		m_PublicStatus.RunedFunction(ADMIN_RUNED_AUTOBAT);
	}
}

void CAdminClient::RunScheduleCmd()
{
	if ((m_PublicStatus.IsRunedFunction(ADMIN_RUNED_GATEWAY)
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& !m_PublicStatus.IsRunedFunction(ADMIN_RUNED_SCHEDULEDCMD)) {
		if (m_ScheduledCommand.m_bRunUser != GetbService() || RunScheduleCommand(GetbService())) {
			m_PublicStatus.RunedFunction(ADMIN_RUNED_SCHEDULEDCMD);
			if (m_ScheduledCommand.m_bRunUser == GetbService()) {
				SaveSetToFile();
				if (m_pRemoteConnect) m_pRemoteConnect->SendCommand(iCOMMAND_CLIENTCMD, ISHAREDISKCLIENTCMD_SCHEDULECMD);
			}
		}
	}
}

void CAdminClient::RunBlackWhiteListDomain()
{
	//网络通畅后，才能设置黑白名单
	if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_IPFROMINI)
		&& (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_NEWPARAMETER) || m_PublicStatus.IsRunedFunction(ADMIN_RUNED_LOCALPARAMETER))
		&& m_PublicStatus.IsRunedFunction(ADMIN_RUNED_PINGGATEWAY)) {
		if (!m_PublicStatus.IsRunedFunction(ADMIN_RUNED_BLACKLIST_DOMAIN)) {
			if (ApplyBlackWhiteListDomain()) {
				m_PublicStatus.RunedFunction(ADMIN_RUNED_BLACKLIST_DOMAIN);
			}
		}
		if (m_PublicStatus.IsRunedFunction(ADMIN_RUNED_BLACKLIST_DOMAIN)) {
			CheckFirewallStatus();
		}
	}
}

BOOL CAdminClient::StartVNCServer(const std::string & sPassword /*= ""*/, DWORD dwServerPort /*= 0*/, DWORD dwClientListenPort /*= 0*/)
{
	BOOL bResult(FALSE);
	dwServerPort = (dwServerPort ? dwServerPort : GetIShareDiskVNCClientPort());
	dwClientListenPort = (dwClientListenPort ? dwClientListenPort : GetVNCClientListenPort());
#if (defined(_WIN32) || defined(_WIN64))
	CSCManager scm;
	if (scm.Open(VNC_SERVICE_NAME)
		&& (scm.ServerIsRunning() || scm.StartScmService())) {
		auto command = scm.GetBinaryPathName();
		string_trimleft(command, _T("\""));
		string_remove(command, _T("\" -service"));
		if (UpdateTightVNCPassword(sPassword, dwClientListenPort)) {
			//设置密码
			auto commandpara = _T("-controlservice -reload");
			WaitForEndCreateProcess(command.c_str(), commandpara);
			WRITE_ISCSIFILELOG(_T("Run vnc server command : ") << command.c_str() << _T(" ") << commandpara);
		}
		//反向连接服务器
		auto commandpara = wstring_format(_T("-controlservice -connect %s:%d"), GetServerTargetIP().c_str(), dwServerPort);
		WaitForEndCreateProcess(command.c_str(), commandpara.c_str());
		WRITE_ISCSIFILELOG(_T("Run vnc server command : ") << command.c_str() << _T(" ") << commandpara.c_str());
		bResult = TRUE;
	}
#else
	if (!IsPEMode()) {
		//sudo netstat -tulnp | grep :5900
		//-bg 参数已经是后台运行，所以不需要 wait 加上 & 参数, root 后台服务运行，必须加上  -auth guess 参数才能找到 display :0
		//-auth guess: using 'XAUTHORITY=/var/run/lightdm/root/:0' for disp=':0'
		auto commandpara = string_format("-display :0 -auth guess -rfbport %d -forever -nopw -shared -connect %s:%d -bg > /tmp/x11vnclog.txt 2>&1", dwClientListenPort,
			ws2s(GetServerTargetIP()).c_str(), dwServerPort);
		bResult = WaitForEndCreateProcess("x11vnc", commandpara, true);
		WRITE_ISCSIFILELOG(_T("Run vnc server command : x11vnc ") << s2ws(commandpara) << _T(" , result : ") << bResult << _T(", IsPEMode : ") << IsPEMode());
		FindConnectSendCommand_safe_all(CAdminConnection::admin_local_client, true, iCOMMAND_START_VNC);
	}
	else {
		bResult = OnStartVncServer(ws2s(GetServerTargetIP()), dwServerPort, dwClientListenPort);
	}
#endif
	return bResult;
}

void CAdminClient::PingGateway()
{
	auto funOnPingCallback = [this](bool bSucess, LPCTSTR log, boost::asio::ip::icmp::endpoint ip, CPinger* pPinger) {
		WRITE_ISCSIFILELOG(_T("Ping IP : ") << s2ws(ip.address().to_string()).c_str()
			<< _T(", ") << (bSucess ? _T("success") : _T("failed"))
			<< _T(", ") << log);
		//当网关不在线，发出的ping可能会导致传输故障 destination_unreachable
		//当停止ping后，问题会解决。如果云桌面服务器也不在线，就增加个第三方服务器，尽快停止ping或则1分钟超时
		if (bSucess || m_nOnTimeCount >= 60) {
			//成功ping 或者 1分钟后，就不再修改设置网卡IP了
			m_PublicStatus.RunedFunction(ADMIN_RUNED_PINGGATEWAY);
			if (m_pPingerGateway) m_pPingerGateway->stop_ping();
			if (m_pPingerTarget) m_pPingerTarget->stop_ping();
			if (m_pPingerPersonalDisk) m_pPingerPersonalDisk->stop_ping();
			WRITE_ISCSIFILELOG(_T("Stop Ping IP to check gateway, on time: ") << m_nOnTimeCount);
		}
		else {
			if (pPinger) pPinger->delay_reping();
		}
		};
	CAdminClientBase::PingGateway(funOnPingCallback);
}

bool CAdminClient::SafeRebootWindows(bool bFast /*= false*/)
{
	bool bResult(false);
	m_UserStatus.UpdateLastRebootTime();
	SaveSetToFile();
	//无延时，立即执行
	if (IsPEMode() || (bFast && CanFastShutdown())) {
		//快速关机重启,导致回复命令无法发出
		bResult = FastRebootWindows();
	}
	if (!bResult && !IsPEMode()) {
		//快速关机重启,导致回复命令无法发出
		bResult = RebootWindows();
	}
	return bResult;
}

bool CAdminClient::SafeShutDownWindows(bool bFast /*= false*/)
{
	bool bResult(false);
	m_UserStatus.UpdateLastShutdownTime();
	SaveSetToFile();
	//无延时，立即执行
	if (IsPEMode() || (bFast && CanFastShutdown())) {
		//快速关机重启,导致回复命令无法发出
		bResult = FastShutdownWindows();
	}
	if (!bResult && !IsPEMode()) {
		//快速关机重启,导致回复命令无法发出
		bResult = ShutDownWindows();
	}
	return bResult;
}