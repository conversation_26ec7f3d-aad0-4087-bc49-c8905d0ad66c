﻿#include "stdafx.h"
#include <numeric>
#include <Dbt.h>
#include "..\iShareDll\OEMFunc.h"
#include "..\iShareDll\stringsystem.h"
#include "..\iShareDll\FormatDisk.h"
//#include "..\iShareDll\VHDInstall.h"
#include "..\iShareDll\WindowsFunc.h"
#include "resource.h"
#include "iShareISO.h"
#include "Wizards.h"
#include "PageTask.h"
#include "PageFormatNew.h"

FORMATDISKPARAALL m_pFormatParaNew;  //格式化自定义参数
bool m_FormatFinishedNew = false; //是否完成格式化
//https://blog.csdn.net/liangyuannao/article/details/7905368
PVOID g_hNotifyDevNodeFormatNew = NULL;
//元素ID
#define WND_DISK_ROW 4
#define WND_DISK_COLUMN 7
int nWndIDAll[WND_DISK_ROW][WND_DISK_COLUMN] = {
	{ IDC_CHECK_GPT, IDC_COMBO_PERCENT, IDC_COMBO_CLIENTLETTER, IDC_COMBO_HIDEDISK, IDC_RADIO_VHDBOOT, IDC_RADIO_USERDATA, IDC_RADIO_WRITEBACK },
	{ IDC_CHECK_INITDISK2, IDC_COMBO_PERCENT2, IDC_COMBO_CLIENTLETTER2, IDC_COMBO_HIDEDISK2, IDC_RADIO_VHDBOOT2, IDC_RADIO_USERDATA2, IDC_RADIO_WRITEBACK2 },
	{ IDC_CHECK_INITDISK3, IDC_COMBO_PERCENT3, IDC_COMBO_CLIENTLETTER3, IDC_COMBO_HIDEDISK3, IDC_RADIO_VHDBOOT3, IDC_RADIO_USERDATA3, IDC_RADIO_WRITEBACK3 },
	{ IDC_CHECK_INITDISK4, IDC_COMBO_PERCENT4, IDC_COMBO_CLIENTLETTER4, IDC_COMBO_HIDEDISK4, IDC_RADIO_VHDBOOT4, IDC_RADIO_USERDATA4, IDC_RADIO_WRITEBACK4 },
};

int nWndIDInitDisk[WND_DISK_ROW] = { nWndIDAll[0][0], nWndIDAll[1][0], nWndIDAll[2][0], nWndIDAll[3][0] };
int nWndIDPercentCombo[WND_DISK_ROW] = { nWndIDAll[0][1], nWndIDAll[1][1], nWndIDAll[2][1], nWndIDAll[3][1] };
int nWndIDClientLetter[WND_DISK_ROW] = { nWndIDAll[0][2], nWndIDAll[1][2], nWndIDAll[2][2], nWndIDAll[3][2] };
int nWndIDHideModeCombo[WND_DISK_ROW] = { nWndIDAll[0][3], nWndIDAll[1][3], nWndIDAll[2][3], nWndIDAll[3][3] };
int nWndIDRadioVhdBoot[WND_DISK_ROW] = { nWndIDAll[0][4], nWndIDAll[1][4], nWndIDAll[2][4], nWndIDAll[3][4] };
int nWndIDRadioUserData[WND_DISK_ROW] = { nWndIDAll[0][5], nWndIDAll[1][5], nWndIDAll[2][5], nWndIDAll[3][5] };
int nWndIDRadioWriteBack[WND_DISK_ROW] = { nWndIDAll[0][6], nWndIDAll[1][6], nWndIDAll[2][6], nWndIDAll[3][6] };

void SetLetterCombo(HWND hdlg, int nComboID, WCHAR cClientLetter)
{
	auto nCount = SendDlgItemMessage(hdlg, nComboID, CB_GETCOUNT, 0, 0);
	for (auto i = 0; i < nCount; i++) {
		WCHAR cLetter = SendDlgItemMessage(hdlg, nComboID, CB_GETITEMDATA, i, 0);
		if (cClientLetter == cLetter) {
			SendDlgItemMessage(hdlg, nComboID, CB_SETCURSEL, i, 0);
			return;
		}
	}
}

#define PERCENT_COMBO_DEFAULT _T("0;20;30;40;50;60;70;80;90;")
#define GBSIZE_COMBO_DEFAULT _T("0;50;100;150;200;250;300;500;")
#define HIDEMODE_COMBO_DEFAULT _T("Show;Hide;Hide on diskless;Hide on VHD boot;")
#define PERCENTTYPE_COMBO_DEFAULT _T("Percent;Size;")
#define FORMAT_UEFI_LEGENCY_COMBO_DEFAULT _T("Legacy/UEFI;UEFI only;Legacy only;")

void InitHideModeCombo(HWND hdlg, int nComboID, DWORD dwCurSel, LPCTSTR pDefaultList)
{
	auto aHideMode = SplitStrings2List(pDefaultList);
	std::wstring sTextTemp;
	GetDlgItemText(hdlg, nComboID, tmpstr(sTextTemp, MAX_LOADSTRING), MAX_LOADSTRING);
	SendDlgItemMessage(hdlg, nComboID, CB_RESETCONTENT, 0, 0);
	for (auto iter = aHideMode.begin(); iter != aHideMode.end(); iter++) {
		auto nIndex = SendDlgItemMessage(hdlg, nComboID, CB_ADDSTRING, 0, (LPARAM)TRANSLATE(*iter).c_str());
		if (nIndex >= 0) {
			SendDlgItemMessage(hdlg, nComboID, CB_SETITEMDATA, nIndex, (LPARAM)0);
		}
	}
	SendDlgItemMessage(hdlg, nComboID, CB_SETCURSEL, dwCurSel, 0);
	if (!sTextTemp.empty()) {
		SetDlgItemText(hdlg, nComboID, sTextTemp.c_str());
	}
}

void SetFormatPara(HWND hdlg, const PFORMATDISKPARAALL pFormatPara)
{
	SendDlgItemMessage(hdlg, IDC_COMBO_UEFI, CB_SETCURSEL, pFormatPara->bGPT, 0);
	SendDlgItemMessage(hdlg, IDC_COMBO_PERCENT_TYPE, CB_SETCURSEL, pFormatPara->bFormatGB, 0);
	auto m_nVHDBootPartition(-1), m_nUserDataPartition(-1), m_nWritebackPartition(-1);
	for (int row = 0; row < WND_DISK_ROW; row++) {
		BOOL bInitDisk = (row < pFormatPara->nPartitionCount) ? TRUE : FALSE;
		SendDlgItemMessage(hdlg, nWndIDInitDisk[row], BM_SETCHECK, bInitDisk ? BST_CHECKED : BST_UNCHECKED, 0);
		if (bInitDisk) {
			SetDlgItemText(hdlg, nWndIDPercentCombo[row], DWORD2wstring(pFormatPara->aDiskPartitions[row].nPercent).c_str());
			SetLetterCombo(hdlg, nWndIDClientLetter[row], pFormatPara->aDiskPartitions[row].cClientLetter);
			SendDlgItemMessage(hdlg, nWndIDHideModeCombo[row], CB_SETCURSEL, pFormatPara->aDiskPartitions[row].nHideDiskMode, 0);
			if (pFormatPara->aDiskPartitions[row].bVHDBootPartition) {
				m_nVHDBootPartition = row;
			}
			if (pFormatPara->aDiskPartitions[row].bUserDataPartition) {
				m_nUserDataPartition = row;
			}
			if (pFormatPara->aDiskPartitions[row].bWritebackPartition) {
				m_nWritebackPartition = row;
			}
		}
	}

	auto funCheckRadio = [hdlg](const int* nWndIDRadio, int nRadioCount, int nCheckedIndex) {
		std::accumulate(nWndIDRadio, nWndIDRadio + nRadioCount, 0, [hdlg, nCheckedIndex](auto index, auto nID)->int {
			SendDlgItemMessage(hdlg, nID, BM_SETCHECK, (nCheckedIndex == index) ? BST_CHECKED : BST_UNCHECKED, 0);
			return ++index;
			});
		};
	funCheckRadio(nWndIDRadioVhdBoot, _countof(nWndIDRadioVhdBoot), m_nVHDBootPartition);
	funCheckRadio(nWndIDRadioUserData, _countof(nWndIDRadioUserData), m_nUserDataPartition);
	funCheckRadio(nWndIDRadioWriteBack, _countof(nWndIDRadioWriteBack), m_nWritebackPartition);
}

void GetFormatPara(HWND hdlg, PFORMATDISKPARAALL pFormatPara)
{
	pFormatPara->bGPT = SendDlgItemMessage(hdlg, IDC_COMBO_UEFI, CB_GETCURSEL, 0, 0);
	pFormatPara->bFormatGB = SendDlgItemMessage(hdlg, IDC_COMBO_PERCENT_TYPE, CB_GETCURSEL, 0, 0);
	pFormatPara->nPartitionCount = 0;
	for (int row = 0; row < WND_DISK_ROW; row++) {
		if (row == 0 || BST_CHECKED == SendDlgItemMessage(hdlg, nWndIDInitDisk[row], BM_GETCHECK, 0, 0)) {
			pFormatPara->nPartitionCount++;
			std::wstring sCapacity;
			GetDlgItemText(hdlg, nWndIDPercentCombo[row], tmpstr(sCapacity, MAX_LOADSTRING), MAX_LOADSTRING);
			pFormatPara->aDiskPartitions[row].nPercent = wsting2DWORD(sCapacity);
			pFormatPara->aDiskPartitions[row].cClientLetter = SendDlgItemMessage(hdlg, nWndIDClientLetter[row], CB_GETITEMDATA, SendDlgItemMessage(hdlg, nWndIDClientLetter[row], CB_GETCURSEL, 0, 0), 0);
			pFormatPara->aDiskPartitions[row].nHideDiskMode = SendDlgItemMessage(hdlg, nWndIDHideModeCombo[row], CB_GETCURSEL, 0, 0);
			pFormatPara->aDiskPartitions[row].bVHDBootPartition = (BST_CHECKED == SendDlgItemMessage(hdlg, nWndIDRadioVhdBoot[row], BM_GETCHECK, 0, 0)) ? TRUE : FALSE;
			pFormatPara->aDiskPartitions[row].bUserDataPartition = (BST_CHECKED == SendDlgItemMessage(hdlg, nWndIDRadioUserData[row], BM_GETCHECK, 0, 0)) ? TRUE : FALSE;
			pFormatPara->aDiskPartitions[row].bWritebackPartition = (BST_CHECKED == SendDlgItemMessage(hdlg, nWndIDRadioWriteBack[row], BM_GETCHECK, 0, 0)) ? TRUE : FALSE;
		}
		else {
			break;
		}
	}
}

void OnCheckInitDisk(HWND hdlg)
{
	BOOL bRowEnable(FALSE), bLastRowEnable(TRUE);
	for (int row = 0; row < WND_DISK_ROW; row++) {
		bRowEnable = TRUE;
		for (int i = 1; i <= row; i++) {
			bRowEnable &= (BST_CHECKED == SendDlgItemMessage(hdlg, nWndIDInitDisk[i], BM_GETCHECK, 0, 0));
		}
		for (int column = 0; column < WND_DISK_COLUMN; column++) {
			if (column == 0) {
				EnableWindow(GetDlgItem(hdlg, nWndIDAll[row][column]), bLastRowEnable);
			}
			else {
				EnableWindow(GetDlgItem(hdlg, nWndIDAll[row][column]), bRowEnable);
			}
		}
		bLastRowEnable = bRowEnable;
	}
}

void EnableFormatControlsNew(HWND hdlg, BOOL bEnable)
{
	EnableWindow(GetDlgItem(hdlg, IDC_COMBO_VOLUME), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_COMBO_UEFI), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_CHECK_LOG), bEnable);
	EnableWindow(GetDlgItem(hdlg, IDC_COMBO_PERCENT_TYPE), bEnable);
	for (int row = 0; row < WND_DISK_ROW; row++) {
		for (int column = 0; column < WND_DISK_COLUMN; column++) {
			EnableWindow(GetDlgItem(hdlg, nWndIDAll[row][column]), bEnable);
		}
	}
	PostMessage(g_hwndPropSheet, PSM_SETWIZBUTTONS, 0, bEnable ? PSWIZB_NEXT : 0);
	if (bEnable) {
		OnCheckInitDisk(hdlg);
	}
}

void FormatDiskNew(HWND hdlg, DWORD nDiskID, DWORD dwCapacityMB, PFORMATDISKPARAALL pFormatPara, BOOL bShowLog)
{
	auto sFormatScript = FormatDiskScript(nDiskID, dwCapacityMB, pFormatPara);
	std::wstring sNewDiskLetter;//新格式化的磁盘盘符
	bool result(false);
	//格式化十次
	for (auto i = 0; i < 3; i++) {
		result = RunDiskpartScript(sFormatScript, bShowLog);
		if (result) {
			//检查是否正确格式化，防止磁盘服务超时导致的失败
			sNewDiskLetter = GetDiskVolumeLetters(nDiskID);
			if (sNewDiskLetter.size() >= pFormatPara->nPartitionCount) {
				HideDiskAfterFormat(sNewDiskLetter, pFormatPara);
				if (FORMAT_PARA_LEGACY_UEFI == pFormatPara->bGPT) {
					for (int i = 0; i < pFormatPara->nPartitionCount; i++) {
						if (pFormatPara->aDiskPartitions[i].bVHDBootPartition) {
							HidePartition_MBR(nDiskID, i + 1, pFormatPara->aDiskPartitions[i].nHideDiskMode ? true : false);
							break;
						}
					}
				}
				break;
			}
			else {
				Sleep_Second(i + 1);//暂停1秒后再试试
			}
		}
	}
	m_FormatFinishedNew = true;
	EnableFormatControlsNew(hdlg, TRUE);
	InitDiskComboVolumeList(hdlg);
	MessageBox(hdlg, LoadStringFromID(result ? MSG_FORMAT_OK : MSG_FORMAT_ERROR).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), result ? MB_OK : MB_ICONERROR);
	if (result) {
		SendMessage(g_hwndPropSheet, PSM_PRESSBUTTON, PSBTN_NEXT, 0);
	}
}

void UpdateFormatProgressNew(HWND hdlg)
{
	DWORD dwPercent(0);
	while (!m_FormatFinishedNew) {
		SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, dwPercent, 0);
		if (dwPercent < 100)
			dwPercent += 10;
		else
			dwPercent = 0;
		Sleep_Second();    // 这种更好用
	}
	SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, 100, 0);
}

void UpdateFormatTypeControl(HWND hdlg, BOOL dwCurSel) {
	SetDlgItemText(hdlg, IDC_STATIC_PER1, dwCurSel ? _T("GB") : _T("%"));
	SetDlgItemText(hdlg, IDC_STATIC_PER2, dwCurSel ? _T("GB") : _T("%"));
	SetDlgItemText(hdlg, IDC_STATIC_PER3, dwCurSel ? _T("GB") : _T("%"));
	SetDlgItemText(hdlg, IDC_STATIC_PER4, dwCurSel ? _T("GB") : _T("%"));
	std::for_each(nWndIDPercentCombo, nWndIDPercentCombo + _countof(nWndIDPercentCombo),
		[hdlg, dwCurSel](auto nID) { InitHideModeCombo(hdlg, nID, 0, dwCurSel ? GBSIZE_COMBO_DEFAULT : PERCENT_COMBO_DEFAULT); });
}

INT_PTR CALLBACK FormatNewDlgProc(HWND hdlg, UINT uMessage, WPARAM wParam, LPARAM lParam)
{
	int wmId, wmEvent;
	LPNMHDR     lpnmhdr;
	switch (uMessage)
	{
	case WM_INITDIALOG:
	{
		InitDiskComboVolumeList(hdlg);
		std::for_each(nWndIDPercentCombo, nWndIDPercentCombo + _countof(nWndIDPercentCombo), [hdlg](auto nID) { InitHideModeCombo(hdlg, nID, 0, PERCENT_COMBO_DEFAULT); });
		std::for_each(nWndIDClientLetter, nWndIDClientLetter + _countof(nWndIDClientLetter), [hdlg](auto nID) { InitLetterCombo(hdlg, nID, 0); });
		std::for_each(nWndIDHideModeCombo, nWndIDHideModeCombo + _countof(nWndIDHideModeCombo), [hdlg](auto nID) { InitHideModeCombo(hdlg, nID, 0, HIDEMODE_COMBO_DEFAULT); });
		InitHideModeCombo(hdlg, IDC_COMBO_UEFI, 0, FORMAT_UEFI_LEGENCY_COMBO_DEFAULT);
		InitHideModeCombo(hdlg, IDC_COMBO_PERCENT_TYPE, 0, PERCENTTYPE_COMBO_DEFAULT);
		SendDlgItemMessage(hdlg, IDC_PROGRESS1, PBM_SETPOS, 0, 0);
		RegisterForDevChange(hdlg, &g_hNotifyDevNodeFormatNew);
		TranlateCWnd(hdlg);
		EnumChildWindows(hdlg, EnumChildProc, (LPARAM)hdlg);

		if (m_pFormatParaNew.nPartitionCount == 0) {
			//第一次初始化
			m_pFormatParaNew.bFormatGB = FALSE;  //格式化时按照百分比
			m_pFormatParaNew.nPartitionCount = 2;
			m_pFormatParaNew.aDiskPartitions[0].nPercent = 70;
			m_pFormatParaNew.aDiskPartitions[0].nHideDiskMode = FORMATDISK_HIDEDISK;
			m_pFormatParaNew.aDiskPartitions[0].bVHDBootPartition = TRUE;
			m_pFormatParaNew.aDiskPartitions[1].nPercent = 30;
			m_pFormatParaNew.aDiskPartitions[1].bUserDataPartition = TRUE;
			m_pFormatParaNew.aDiskPartitions[1].bWritebackPartition = TRUE;
			if (IsOEMSoulLon()) {
				m_pFormatParaNew.bFormatGB = TRUE;  //格式化时按照容量计算(GB)
				m_pFormatParaNew.aDiskPartitions[0].nPercent = 200;
				m_pFormatParaNew.aDiskPartitions[1].nPercent = 100;
			}
		}
		SetFormatPara(hdlg, &m_pFormatParaNew);
		UpdateFormatTypeControl(hdlg, m_pFormatParaNew.bFormatGB);
		OnCheckInitDisk(hdlg);
	}
	break;
	case WM_COMMAND:
	{
		PropSheet_Changed(GetParent(hdlg), hdlg);
		wmId = LOWORD(wParam);
		wmEvent = HIWORD(wParam);
		switch (wmId)
		{
		case IDC_CHECK_INITDISK2:
		case IDC_CHECK_INITDISK3:
		case IDC_CHECK_INITDISK4:
			if (BN_CLICKED == wmEvent) {
				OnCheckInitDisk(hdlg);
			}
			break;
		case IDC_COMBO_PERCENT_TYPE:
			if (CBN_SELCHANGE == wmEvent) {
				auto dwCurSel = SendMessage((HWND)lParam, CB_GETCURSEL, (WORD)0, 0L);
				UpdateFormatTypeControl(hdlg, dwCurSel);
			}
			break;
		}
	}
	break;
	case WM_NOTIFY:
	{
		lpnmhdr = (NMHDR FAR*)lParam;
		switch (lpnmhdr->code)
		{
		case PSN_SETACTIVE:
			//this will be ignored if the property sheet is not a wizard
			PropSheet_SetWizButtons(GetParent(hdlg), PSWIZB_BACK | PSWIZB_NEXT);
			m_FormatFinishedNew = false;
			SendMessage(hdlg, WM_INITDIALOG, 0, 0);//重新初始化一次
			break;
		case PSN_RESET:
		{
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, IDD_PAGE_RESET_INDEX);
			return IDD_PAGE_RESET_INDEX;
		}
		break;
		case PSN_WIZBACK:
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, IDD_PAGETASK);
			return (IDD_PAGETASK);
			break;
		case PSN_WIZNEXT:
		{
			bool bGoToNextPage(false);
			if (!m_FormatFinishedNew) {
				auto clickbutton = MessageBox(hdlg, LoadStringFromID(MSG_FORMATDISK).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_YESNOCANCEL);
				bGoToNextPage = (IDNO == clickbutton);
				if (IDYES == clickbutton) {
					auto dwVolumeCurSel = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETCURSEL, 0, 0);
					UINT errormsg(0);
					if (CB_ERR == dwVolumeCurSel) {
						errormsg = MESSAGE_VOLUME_ERROR;
					}

					if (errormsg) {
						//有参数错误显示
						MessageBox(hdlg, LoadStringFromID(errormsg).c_str(), LoadStringFromID(IDS_APP_TITLE).c_str(), MB_ICONERROR);
					}
					else {
						DWORD nDiskID = SendDlgItemMessage(hdlg, IDC_COMBO_VOLUME, CB_GETITEMDATA, dwVolumeCurSel, 0);
						DWORD dwCapacityMB = (g_lDiskLists[dwVolumeCurSel].get<1>()) / 1024 / 1024;
						GetFormatPara(hdlg, &m_pFormatParaNew);
						EnableFormatControlsNew(hdlg, FALSE);
						auto thread1 = boost::thread(boost::bind(&FormatDiskNew, hdlg, nDiskID, dwCapacityMB, &m_pFormatParaNew,
							BST_CHECKED == SendDlgItemMessage(hdlg, IDC_CHECK_LOG, BM_GETCHECK, 0, 0)));
						auto thread2 = boost::thread(boost::bind(&UpdateFormatProgressNew, hdlg));
					}
				}
			}
			else {
				bGoToNextPage = true;//上传完成
			}
			if (bGoToNextPage) {
				g_NextStepPage = PAGE_DOWNLOAD;
				UnregisterDeviceNotification(g_hNotifyDevNodeFormatNew);
			}
			//https://msdn.microsoft.com/zh-cn/library/bb774572(v=vs.85).aspx
			SetWindowLong(hdlg, DWL_MSGRESULT, bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
			return (bGoToNextPage ? IDD_PAGETASK : IDD_PAGE_RESET_INDEX);
		}
		break;
		default:
			break;
		}
	}
	break;

	case WM_DEVICECHANGE:
	{
		switch (wParam)
		{
		case DBT_DEVICEARRIVAL:
			// Handle device arrival
			if (IsWindowEnabled(GetDlgItem(hdlg, IDC_COMBO_VOLUME))) {
				InitDiskComboVolumeList(hdlg);//重新初始化一次
			}
			break;

		case DBT_DEVICEQUERYREMOVE:
			// Handle device removal request
			break;

		case DBT_DEVICEREMOVECOMPLETE:
			// Handle device removal
			if (IsWindowEnabled(GetDlgItem(hdlg, IDC_COMBO_VOLUME))) {
				InitDiskComboVolumeList(hdlg);//重新初始化一次
			}
			break;
		}
	}
	break;

	default:
		break;
	}
	return FALSE;
}