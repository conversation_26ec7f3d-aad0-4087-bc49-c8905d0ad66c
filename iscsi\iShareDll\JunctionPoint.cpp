﻿#include "stdafx.h"
#include "commondef.h"
#include "OEMFunc.h"
#include "UsbBootDef.h"
#include "CDeadlineTimer.hpp"
#include "LogSystem.h"
#include "windowssystem.h"
#include "WindowsFunc.h"
#include "regsystem.h"
#include "PrivilegeSystem.h"
#include "HiveSystem.h"
#include "IniFileReg.h"
//#include "iShareInstall.h"
#include "JunctionPoint.h"

#define ICONCACHE_DB_FILEPATH _T("AppData\\Local\\IconCache.db")
#define ICONCACHE_EXPLORER_FOLDERPATH _T("AppData\\Local\\Microsoft\\Windows\\Explorer")

CJunctionPoint_ptr g_JunctionPoint; //全局变量

CJunctionPoint::CJunctionPoint()
	: m_bSuperUser(false)
	, m_dwIbftBoot(0)
	, m_UsersInit(FALSE)	//是否完成默认目录的拷贝初始化
{
	//调整读写连接点的权限
	AdjustPrivilegeForJunctionPoint(TRUE);
	AdjustPrivilegeForJunctionPoint(FALSE);
}

CJunctionPoint::~CJunctionPoint()
{
}
//初始化服务时调用，读取系统启动参数和本地userdata
void CJunctionPoint::InitService()
{
	CheckBootMode();
	if (InitAndSetUserProfile(true)) {
		ClearSystemIconCache();//清理图标缓存
	}
}

bool CJunctionPoint::InitAndSetUserProfile(bool bInitService)
{
	auto ret = InitUserDataIni();
	if (ret) {
		SetUserProfile(bInitService);
	}
	return ret;
}

void CJunctionPoint::SetUserProfile(bool bInitService)
{
	if (!m_dwIbftBoot || !m_UserProfile.m_bUserProfile || !m_UserProfile.m_bUserProfileSuper && m_bSuperUser) {
		//还原转移
		RestoreUsersProfile(true);
		RestoreUsersProfile(false);
		FixDocumentsandSettings(true);
	}
	else if (m_dwIbftBoot && m_UserProfile.m_bUserProfile && (!m_bSuperUser || m_UserProfile.m_bUserProfileSuper)) {
		//设置目录
		RestoreUsersProfile(true);
		SetUsersProfile(bInitService);
		FixDocumentsandSettings(false);
	}
}

//查找本地userdata, 当磁盘变化时，由客户端调用
bool CJunctionPoint::ReinitUserProfile(const User_Status& oUserStatus)
{
	auto ret = InitAndSetUserProfile(false);
	if (ret) {
		//设置有变化
		InitUserProfile(oUserStatus);
	}
	return ret;
}
//初始化所有的自定义连接点, 当设置变化时，由客户端调用,并且保存新的设置
void CJunctionPoint::InitUserProfile(const User_Status& oUserStatus)
{
	if (!m_sUserDataIni.empty()) {
		//重新导入文件列表和设置
		if (CompareUserProfile(&oUserStatus) && MakeDirectory(GetUserDataRoot())) {
			UpdateUserProfile(&oUserStatus);
			SaveUserDataIni();
			ReloadUsersProfiles();
			auto workthread = boost::thread(boost::bind(&CJunctionPoint::SetUserProfile, this, false));
		}
	}
	else {
		WRITE_ISCSIFILELOG(_T("UserDataIni set is empty, so can not set and save!"));
	}
}

bool CJunctionPoint::ReinitApplicationLayer(const User_Status& oUserStatus)
{
	if (!m_sUserDataIni.empty() && UpdateApplicationLayer(&oUserStatus) && SaveUserDataIni()) {
		Diskletter(m_sUserDataIni.wstring(), m_ApplicationLayer.cUserDataDiskLetter, false);
		//通知驱动重新装载配置
		DeviceIoControliSharePnp(IOCTL_SANBOOTCONF_CONFIGRELOAD, true);
		return true;
	}
	return false;
}

bool CJunctionPoint::GetBootMode(bool& isPnpVHDBooting, bool& isSuperUser, bool bRestartLog /*= true*/)
{
	auto pJunctionPoint = boost::make_shared<CJunctionPoint>();
	if (pJunctionPoint) {
		isPnpVHDBooting = pJunctionPoint->CheckBootMode(false, bRestartLog);
		isSuperUser = pJunctionPoint->IsSuperUser();
		return true;
	}
	return false;
}

bool CJunctionPoint::CheckBootMode(bool bRebootWhenPnpVHDBooting /*= true*/, bool bRestartLog /*= true*/)
{
	bool isPnpVHDBooting = false;
	auto para_reg = wstring_format(SERVICE_PARAMETER_FORMAT, GetISharePnpServerName());

	// 读取启动模式
	if (!ReadRegValue(HKEY_LOCAL_MACHINE, para_reg.c_str(), ISHAREPNP_IBFTBOOT, m_dwIbftBoot)) {
		WRITE_ISCSIFILELOG(_T("Failed to read IbftBoot!"));
	}

	// 根据启动模式处理
	if (BOOT_IBFT == m_dwIbftBoot) {
		HandleIBFTBootMode(para_reg, bRestartLog);
	}
	else if (BOOT_GRUB4DOS == m_dwIbftBoot) {
		isPnpVHDBooting = HandleGrub4DosBootMode(bRebootWhenPnpVHDBooting, bRestartLog);
	}

	WRITE_ISCSIFILELOG(_T("CheckBootMode completed, Boot mode: ") << m_dwIbftBoot << _T(", Superuser: ") << m_bSuperUser << _T(", PnP VHD booting: ") << isPnpVHDBooting);
	return isPnpVHDBooting;
}

boost::shared_ptr<CVHDBootMng> CJunctionPoint::GetVHDBootMng(bool bRestartLog)
{
	// 初始化VHD启动管理
	auto oVHDBootMng = boost::make_shared<CVHDBootMng>();
	if (oVHDBootMng && oVHDBootMng->FindVHDBootVolume()) {
		if (bRestartLog) oVHDBootMng->RestartLog();
	}
	else {
		oVHDBootMng.reset();
	}
	return oVHDBootMng;
}

void CJunctionPoint::HandleIBFTBootMode(const std::wstring& para_reg, bool bRestartLog)
{
	// 读取超级用户标志
	int bSuperUser = 0;
	if (ReadRegValue(HKEY_LOCAL_MACHINE, para_reg.c_str(), ISHAREPNP_SUPERUSER, bSuperUser)) {
		m_bSuperUser = bSuperUser ? TRUE : FALSE;
	}
	else {
		WRITE_ISCSIFILELOG(_T("Failed to read superuser flag!"));
	}

	// 读取主机名
	std::wstring sHostname;
	if (ReadRegValue(HKEY_LOCAL_MACHINE, para_reg.c_str(), ISHAREPNP_HOSTNAME, sHostname)) {
		string_trim(sHostname);
		if (string_ends_with(sHostname, GetSuperuserSuffix())) {
			sHostname = string_left(sHostname, sHostname.find(GetSuperuserSuffix()));
			m_bSuperUser = TRUE;
		}
		WRITE_ISCSIFILELOG(_T("Hostname read: ") << sHostname);
	}
	else {
		WRITE_ISCSIFILELOG(_T("Failed to read hostname!"));
	}

	GetVHDBootMng(bRestartLog);
}

bool CJunctionPoint::HandleGrub4DosBootMode(bool bRebootWhenPnpVHDBooting, bool bRestartLog)
{
	bool isPnpVHDBooting = false;
	auto oVHDBootMng = GetVHDBootMng(bRestartLog);
	if (oVHDBootMng) {
		m_bSuperUser = (VHD_BOOT_MODE_SUPER == oVHDBootMng->GetVHDBootMode(true));

		isPnpVHDBooting = oVHDBootMng->IsPnpVHDBooting();
		WRITE_ISCSILOG(_T("CheckBootMode, PnP VHD booting! Superuser: ") << m_bSuperUser
			<< _T(", PnP VHD booting: ") << isPnpVHDBooting
			<< _T(", Reboot required: ") << bRebootWhenPnpVHDBooting);

		if (isPnpVHDBooting && bRebootWhenPnpVHDBooting) {
			HandlePnPDeviceCheck(oVHDBootMng);
		}
	}
	return isPnpVHDBooting;
}

void CJunctionPoint::HandlePnPDeviceCheck(const boost::shared_ptr<CVHDBootMng>& oVHDBootMng)
{
	const int MAX_RETRY_COUNT = 60;
	for (int i = 0; i < MAX_RETRY_COUNT; ++i) {
		//检查设备管理器的设备是否完成安装，完成或者超时后重启
		bool bDevHasProblem = false;
		ScanForHardwareChange([&bDevHasProblem](LPCTSTR instanceId, ULONG status, ULONG problem) -> bool {
			if (status == DN_HAS_PROBLEM) {
				//设备有问题，不重启，等待1分钟后重启
				WRITE_ISCSIFILELOG(_T("Device has issues! Waiting... Device ID: ") << instanceId);
				bDevHasProblem = true;
				return false;
			}
			return true;
			});

		if (!bDevHasProblem) {
			//设备安装完成，或者超过1分钟重启
			auto ret1 = oVHDBootMng->FinishPnpAndReboot();
			WRITE_ISCSIFILELOG(_T("OnTime Device has problem: ") << bDevHasProblem \
				<< _T(", times: ") << i << _T(", FinishPnpAndReboot result:") << ret1);
			//重启成功后, 退出循环，以便于重启，不然因为服务器未关闭，导致无法重启。
			if (ret1) break;
		}
		else {
			Sleep_Second();
		}
	}
}

boost::filesystem::path CJunctionPoint::FindUserDataIni(LPCTSTR pUserDataIniPath)
{
	boost::filesystem::path pUserDataIni;
	std::list<std::wstring> lPersonalDisk, lLocalDisk;
	if (IsOEMSoulLon()) {
		//山大鲁能本地盘优先
		GetLocalPersonalDisks(lLocalDisk, lPersonalDisk);
	}
	else {
		GetLocalPersonalDisks(lPersonalDisk, lLocalDisk);
	}
	std::find_if(lPersonalDisk.begin(), lPersonalDisk.end(), [pUserDataIniPath, &pUserDataIni](auto sVolume)->bool {
		//个人磁盘，本地磁盘
		auto sIni = boost::filesystem::absolute(pUserDataIniPath, sVolume);
		if (IsFile(sIni)) {
			//找到，退出
			pUserDataIni = sIni;
			auto pFolderUserData = sIni.parent_path();
			HideDirectory(pFolderUserData);
			return true;
		}
		return false;
		});
	if (pUserDataIni.empty()) {
		//找到后，开始初始化
		if (lPersonalDisk.size() > 0) {
			//保存一个待用的ini路径
			pUserDataIni = boost::filesystem::absolute(pUserDataIniPath, *(lPersonalDisk.begin()));
		}
		else {
			std::find_if(lLocalDisk.begin(), lLocalDisk.end(), [pUserDataIniPath, &pUserDataIni](auto sVolume)->bool {
				//个人磁盘，本地磁盘
				auto sIni = boost::filesystem::absolute(pUserDataIniPath, sVolume);
				if (IsFile(sIni)) {
					//找到，退出
					pUserDataIni = sIni;
					auto pFolderUserData = sIni.parent_path();
					HideDirectory(pFolderUserData);
					return true;
				}
				return false;
				});
			if (pUserDataIni.empty()) {
				if (lLocalDisk.size() > 0) {
					//保存一个待用的ini路径
					pUserDataIni = boost::filesystem::absolute(pUserDataIniPath, *(lLocalDisk.begin()));
				}
				else {
					WRITE_ISCSIFILELOG(_T("UserDataIni set is empty, because can not find a personal volume!"));
				}
			}
		}
	}
	return pUserDataIni;
}

bool CJunctionPoint::UpdateEnvVarPath(boost::filesystem::path pUserDataIni)
{
	// 检查文件是否存在
	if (!IsFile(pUserDataIni)) {
		return false;
	}

	CIniFile hUserDataIni;
	if (!hUserDataIni.Open(pUserDataIni)) {
		return false;
	}

	// 检查并保存 Hive 文件（如果需要）
	if (APPLICATION_LAYER_PARA_::UpdateHiveFile(hUserDataIni)) {
		bool saveResult = hUserDataIni.Save(pUserDataIni);
		if (!saveResult) {
			WRITE_ISCSIFILELOG(_T("Failed to save Hive file to: ") << pUserDataIni.wstring());
		}
		else {
			WRITE_ISCSIFILELOG(_T("Hive file saved successfully to: ") << pUserDataIni.wstring());
		}
	}

	// 检查是否需要更新环境变量
	if (!NeedUpdateEnvVars(hUserDataIni)) {
		return true;
	}

	// 更新环境变量
	return UpdateAllEnvVars(hUserDataIni, pUserDataIni);
}

bool CJunctionPoint::NeedUpdateEnvVars(CIniFile& hUserDataIni)
{
	// 检查环境变量数量是否匹配
	if (hUserDataIni.GetSectionKeyCount(MAKELINK_INI_ENV_SECTION) != _countof(env_vars)) {
		return true;
	}

	// 检查 USERPROFILE, SYSTEMROOT 是否发生变化
	for (const auto& env_var : env_test_vars) {
		if (UpdateSingleEnvVar(hUserDataIni, env_var)) {
			return true;
		}
	}
	return false;
}

bool CJunctionPoint::UpdateAllEnvVars(CIniFile& hUserDataIni, const boost::filesystem::path& pUserDataIni)
{
	// 清除旧的环境变量配置
	hUserDataIni.DeleteSection(MAKELINK_INI_ENV_SECTION);

	// 更新所有环境变量
	int updateCount = 0;
	for (const auto& env_var : env_vars) {
		if (UpdateSingleEnvVar(hUserDataIni, env_var)) {
			updateCount++;
		}
	}

	// 保存更新的配置
	bool saveResult = hUserDataIni.Save(pUserDataIni);
	if (!saveResult) {
		WRITE_ISCSIFILELOG(_T("Failed to save updated environment variables to: ") << pUserDataIni.wstring());
		return false;
	}

	WRITE_ISCSIFILELOG(_T("Successfully updated ") << updateCount << _T(" environment variables"));
	return true;
}

bool CJunctionPoint::UpdateSingleEnvVar(CIniFile& hUserDataIni, const EnvVar& env_var)
{
	std::wstring sPath;
	bool success = false;

	// 获取环境变量值
	if (env_var.csidl) {
		success = GetEnvVarPath(env_var.csidl, sPath);
	}
	else if (env_var.var_name == MAKELINK_INI_ENV_CURRENT_SID) {
		sPath = GetCurrentUserSid();
		success = !sPath.empty();
	}
	else {
		success = GetEnvVarValue(env_var.var_name.c_str(), sPath);
	}

	if (!success) {
		WRITE_ISCSIFILELOG(_T("Failed to get environment variable: ") << env_var.var_name);
		return false;
	}

	// 转换短路径为完整路径
	if (IsShortPath(sPath)) {
		sPath = GetFullPath(sPath).wstring();
	}

	// 读取旧的环境变量值以进行比较
	std::wstring sOldPath;
	hUserDataIni.GetValue(MAKELINK_INI_ENV_SECTION, env_var.var_name, sOldPath);
	if (sPath == sOldPath) {
		// 没有变化，不需要更新
		return false;
	}
	// 设置环境变量值
	return hUserDataIni.SetValue(MAKELINK_INI_ENV_SECTION, env_var.var_name, sPath);
}

bool CJunctionPoint::InitUserDataIni()
{
	auto pUserDataIni = FindUserDataIni(MAKELINK_INI_PATH);
	bool ret = pUserDataIni.compare(m_sUserDataIni);
	if (ret) {
		m_sUserDataIni = pUserDataIni;
		WRITE_ISCSIFILELOG(_T("Change UserDataIni to: ") << m_sUserDataIni.wstring());
		LoadUserDataIni();
	}
	return ret;
}

/*
[set]
;是否开启关闭转移
;enable the folder redirect
UsersEnable = 1
;超级用户下是否转移
;enable in super user too
SuperEnable = 0
;是否禁止默认的转移(C:\Users, C:\ProgramData)
;disable default folders redirect (C:\Users, C:\ProgramData)
DisableProfile = 1
;是否已经完成默认目录转移的初始化
#whether default folders redirect inited
UsersInit = 1
[folder]
;你想转移的目录
;you want redirect folder
C:\Program Files(x86)\game = [newname]
*/
bool CJunctionPoint::LoadUserDataIni()
{
	bool ret = (!m_sUserDataIni.empty())
		&& IsFile(m_sUserDataIni)
		&& m_hUserDataIni.Open(m_sUserDataIni);
	if (ret) {
		WRITE_ISCSIFILELOG(_T("Opened UserDataIni and load : ") << m_sUserDataIni.wstring());
		//有设置，
		ret = m_UserProfile.LoadUserProfile(m_hUserDataIni, m_UsersInit);
		m_ApplicationLayer.LoadApplicationLayer(m_hUserDataIni);
	}
	else {
		WRITE_ISCSIFILELOG(_T("Can not Open UserDataIni ") << m_sUserDataIni.wstring());
		m_UserProfile.reset();
		m_UsersInit = FALSE;
		m_ApplicationLayer.reset();
		m_hUserDataIni.Close();
	}
	ReloadUsersProfiles();
	return ret;
}

void CJunctionPoint::ReloadUsersProfiles()
{
	m_lUsersProfileOld = m_lUsersProfile;
	m_lUsersProfile.clear();
	if (!IsOEMICloud()) {
		m_sSystemUsers = GetProfilesPath();
		m_sSystemProgramData = GetAllUsersProfilePath();
		if (!m_UserProfile.m_bUserProfileDisableDefault) {
			AddUsersProfile(m_sSystemUsers, DEFAULT_PROFILE_NEW_NAME);
			AddUsersProfile(m_sSystemProgramData, DEFAULT_PROFILE_NEW_NAME);
		}
	}
	m_hUserDataIni.EnumKeyOfSection(MAKELINK_INI_FOLDER_SECTION, [this](const auto& sKey, const auto& sVal)->bool {
		this->AddUsersProfile(sKey, sVal);
		return true;//继续遍历
		});
	m_lRedirectFilesOld = m_lRedirectFiles;
	m_lRedirectFiles.clear();
	m_hUserDataIni.EnumKeyOfSection(MAKELINK_INI_FILE_SECTION, [this](const auto& sKey, const auto& sVal)->bool {
		AddRedirectFile(sKey, sVal);
		return true;//继续遍历
		});
	CIniFile hUserDataIniLocal;
	auto sLocalDataIni = boost::filesystem::absolute(MAKELINK_INI_FILENAME, GetModuleFolder());
	if (IsFile(sLocalDataIni) && hUserDataIniLocal.Open(sLocalDataIni)) {
		WRITE_ISCSIFILELOG(_T("Opened Local UserDataIni and load : ") << sLocalDataIni.c_str());
		hUserDataIniLocal.EnumKeyOfSection(MAKELINK_INI_FOLDER_SECTION, [this](const auto& sKey, const auto& sVal)->bool {
			AddUsersProfile(sKey, sVal);
			return true;//继续遍历
			});
		hUserDataIniLocal.EnumKeyOfSection(MAKELINK_INI_FILE_SECTION, [this](const auto& sKey, const auto& sVal)->bool {
			AddRedirectFile(sKey, sVal);
			return true;//继续遍历
			});
	}
	//windows7 转移后，会出现配置错误，桌面无法使用，故关掉之
	std::wstring sUserProfiles;
	if (m_hUserDataIni.GetValue(MAKELINK_INI_ENV_SECTION, USERPROFILE_VAR, sUserProfiles)
		&& !sUserProfiles.empty()) {
		AddUsersProfile(boost::filesystem::absolute(ICONCACHE_EXPLORER_FOLDERPATH, sUserProfiles).wstring(), DEFAULT_PROFILE_NEW_NAME);
		AddRedirectFile(boost::filesystem::absolute(ICONCACHE_DB_FILEPATH, sUserProfiles).wstring(), DEFAULT_PROFILE_NEW_NAME);
	}
	LogUserDataIni(true);

	auto removeOldEntries = [](auto& oldMap, const auto& newMap) {
		for (auto iter = oldMap.begin(); iter != oldMap.end();) {
			if (newMap.find(iter->first) != newMap.end()) {
				iter = oldMap.erase(iter);
			}
			else {
				++iter;
			}
		}
		};
	//删除 m_lUsersProfileOld 里，有和 m_lUsersProfile 相同 key 的项目
	removeOldEntries(m_lUsersProfileOld, m_lUsersProfile);
	//删除 m_lRedirectFilesOld 里，有和 m_lRedirectFiles 相同 key 的项目
	removeOldEntries(m_lRedirectFilesOld, m_lRedirectFiles);
}

bool CJunctionPoint::SaveUserDataIni()
{
	m_UserProfile.SaveUserProfile(m_hUserDataIni, m_UsersInit);
	m_ApplicationLayer.SaveApplicationLayer(m_hUserDataIni);

	auto ret = (!m_sUserDataIni.empty()) && m_hUserDataIni.Save(m_sUserDataIni);
	if (ret) {
		LogUserDataIni(false);
	}
	else {
		WRITE_ISCSIFILELOG(_T("Save UserDataIni error! UserDataIni path ") << m_sUserDataIni.wstring());
	}
	return ret;
}

void CJunctionPoint::LogUserDataIni(bool bLoad)
{
	WRITE_ISCSIFILELOG((bLoad ? _T("Load UserDataIni : ") : _T("Save UserDataIni : ")) << m_sUserDataIni << _T(", UsersEnable : ") << m_UserProfile.m_bUserProfile
		<< _T(", UsersInit : ") << m_UsersInit << _T(", SuperEnable : ") << m_UserProfile.m_bUserProfileSuper << _T(", DisableProfile : ") << m_UserProfile.m_bUserProfileDisableDefault);
	std::for_each(m_lUsersProfile.begin(), m_lUsersProfile.end(), [](auto iter) {
		WRITE_ISCSIFILELOG(_T("Users profile directory : ") << iter.first << _T(", name ") << iter.second);
		});
	std::for_each(m_lRedirectFiles.begin(), m_lRedirectFiles.end(), [](auto iter) {
		WRITE_ISCSIFILELOG(_T("Users profile file : ") << iter.first << _T(", name ") << iter.second);
		});
}

void CJunctionPoint::GetLocalPersonalDisks(std::list<std::wstring>& lPersonalDisk, std::list<std::wstring>& lLocalDisk)
{
	auto sVolumeList = GetAllVolumePath();
	for (auto iter = sVolumeList.begin(); iter != sVolumeList.end();) {
		int nBusType(0);
		auto sDiskVolumeID = GetVolumeDiskID((*iter)[0], &nBusType);
		//C盘有可能因为权限查不到，为空，
		//有些本地硬盘也没有名字
		if (BusTypeiScsi == nBusType
			&& (sDiskVolumeID.compare(GetCommonSCSIPersonalDiskProductIDA()) == 0
				|| sDiskVolumeID.compare(GetNASISCSIDiskProductIDA()) == 0)
			&& (*iter)[0] != _T('C')) {
			//个人磁盘
//			WRITE_ISCSIFILELOG(_T("Query personal volume disk add letter : ") << (*iter) << _T(", ID: ") << sDiskVolumeID.c_str());
			lPersonalDisk.push_back(*iter);
			iter++;
		}
		// microsoft vhd 虚拟盘总线是 BusTypeFileBackedVirtual
		else if (BusTypeiScsi != nBusType && BusTypeFileBackedVirtual != nBusType && BusTypeUsb != nBusType
			&& sDiskVolumeID.compare(GetMicrosoftVHDDiskProductIDA()) != 0
			&& sDiskVolumeID.compare(GetVBootXPVHDDiskProductIDA()) != 0
			&& (*iter)[0] != _T('C')) {
			//本地磁盘
//			WRITE_ISCSIFILELOG(_T("Query local volume disk add letter : ") << (*iter) << _T(", ID: ") << sDiskVolumeID.c_str());
			lLocalDisk.push_back(*iter);
			iter++;
		}
		else {
			//			WRITE_ISCSIFILELOG(_T("Query volume disk delete letter : ") << (*iter) << _T(", ID: ") << sDiskVolumeID.c_str());
			iter = sVolumeList.erase(iter);
		}
	}
}

//还原转移
void CJunctionPoint::RestoreUsersProfile_(const NO_CASE_WSTRING_MAP& lUsersProfile, const NO_CASE_WSTRING_MAP& lRedirectFiles)
{
	std::for_each(lUsersProfile.begin(), lUsersProfile.end(), [this](auto iter) {
		auto sOldUsersPath = this->FormatOldName(iter.first);
		if (IsJunctionPoint(iter.first.c_str(), TRUE) && IsDirectory(sOldUsersPath)) {
			CreateJunctionPoint(iter.first.c_str(), sOldUsersPath.c_str(), TRUE, TRUE);
			WRITE_ISCSIFILELOG(_T("Restore users profile directory to : ") << sOldUsersPath);
		}
		});
	std::for_each(lRedirectFiles.begin(), lRedirectFiles.end(), [this](auto iter) {
		auto sOldUsersPath = this->FormatOldName(iter.first);
		if (IsJunctionPoint(iter.first.c_str(), FALSE) && IsFile(sOldUsersPath)) {
			CreateJunctionPoint(iter.first.c_str(), sOldUsersPath.c_str(), TRUE, FALSE);
			WRITE_ISCSIFILELOG(_T("Restore redirect file : ") << sOldUsersPath);
		}
		});
}

//设置转移, 重命名转移不要删除旧文件，当 ntfs 系统损坏时 IsJunctionPoint 有可能判断错误
//导致 rename 删除旧文件，而旧文件又指向了实际转移目录！！！。。。。(不太可能)
void CJunctionPoint::SetUsersProfile(bool bInitService)
{
	InitUsersProfile(bInitService);
	std::for_each(m_lUsersProfile.begin(), m_lUsersProfile.end(), [this](auto iter) {
		auto sUserFolder = this->GetUserDataPath(iter.first, iter.second);
		if ((IsDirectory(iter.first) || MakeDirectory(iter.first))
			&& (IsDirectory(sUserFolder) || MakeDirectory(sUserFolder))) {
			if (IsJunctionPoint(iter.first.c_str(), TRUE)
				|| RenameFile(iter.first, this->FormatOldName(iter.first), false)) {
				//直接转移，或者重命名后转移
				CreateJunctionPoint(iter.first.c_str(), sUserFolder.c_str(), TRUE, TRUE);
				WRITE_ISCSIFILELOG(_T("Save Users folder Profile ") << iter.first << _T(", to : ") << sUserFolder);
			}
			else {
				WRITE_ISCSIFILELOG(_T("Users folder can not rename : ") << iter.first);
			}
		}
		else {
			WRITE_ISCSIFILELOG(_T("Users folder is not exist : ") << iter.first);
		}
		});
	std::for_each(m_lRedirectFiles.begin(), m_lRedirectFiles.end(), [this](auto iter) {
		auto sUserFolder = this->GetUserDataPath(iter.first, iter.second);
		if ((IsFile(iter.first) || MakeFile(iter.first))
			&& (IsFile(sUserFolder) || MakeFile(sUserFolder))) {
			if (IsJunctionPoint(iter.first.c_str(), FALSE)
				|| RenameFile(iter.first, this->FormatOldName(iter.first), false)) {
				//直接转移，或者重命名后转移
				CreateJunctionPoint(iter.first.c_str(), sUserFolder.c_str(), TRUE, FALSE);
				WRITE_ISCSIFILELOG(_T("Save File ") << iter.first << _T(", to : ") << sUserFolder);
			}
			else {
				WRITE_ISCSIFILELOG(_T("File can not rename : ") << iter.first);
			}
		}
		else {
			WRITE_ISCSIFILELOG(_T("File is not exist : ") << iter.first);
		}
		});
}
//初始化
void CJunctionPoint::InitUsersProfile(bool bInitService)
{
	bool bSyncDir(false);//是否同步了目录
	int nErrorCount(0);//错误次数
	std::list<boost::filesystem::path> sErrorSrcFiles;//错误拷贝的文件列表
	std::for_each(m_lUsersProfile.begin(), m_lUsersProfile.end(), [this, &bSyncDir, &nErrorCount, &sErrorSrcFiles](auto iter) {
		auto sUserFolder = this->GetUserDataPath(iter.first, iter.second);
		if (!IsDirectory(sUserFolder)
			|| (this->m_UsersInit == INIT_USERSPROFILE_NO) && (!m_UserProfile.m_bUserProfileDisableDefault) && IsDefaultName(iter.second)) {
			if (IsDefaultName(iter.second)) bSyncDir = true;
			//初始化目录
			auto sOldUsersPath = this->FormatOldName(iter.first);
			if (IsDirectory(iter.first) && !IsJunctionPoint(iter.first.c_str(), TRUE) && MakeDirectory(sUserFolder)) {
				nErrorCount += this->SyncDirectory(iter.first, sUserFolder, sErrorSrcFiles);
			}
			else if (IsDirectory(sOldUsersPath) && !IsJunctionPoint(sOldUsersPath.c_str(), TRUE) && MakeDirectory(sUserFolder)) {
				nErrorCount += this->SyncDirectory(sOldUsersPath, sUserFolder, sErrorSrcFiles);
			}
		}
		});
	if (bSyncDir && (m_UsersInit == INIT_USERSPROFILE_NO) && (!m_UserProfile.m_bUserProfileDisableDefault)) {
		//同步成功或者从服务初始化，只执行一次
		bool bErrorNTUSER(false);
		std::for_each(sErrorSrcFiles.begin(), sErrorSrcFiles.end(), [&bErrorNTUSER](auto sErrorSrc) {
			if (sErrorSrc.filename().wstring().compare(_T("NTUSER.DAT")) == 0) {
				bErrorNTUSER = true;
			}
			WRITE_ISCSIFILELOG(_T("Copy file error, src file : ") << sErrorSrc.c_str());
			});
		//对比成功，标记为下次不再对比
		if (!nErrorCount || bInitService && !bErrorNTUSER) {
			//无错误， 或者启动拷贝时无NTUSER.DAT拷贝错误
			m_UsersInit = INIT_USERSPROFILE_DONE;
			SaveUserDataIni();
		}
		WRITE_ISCSIFILELOG(_T("Init Users and Program Data folder, UsersInit : ") << m_UsersInit << _T(", ErrorCount : ") << nErrorCount);
	}
	std::for_each(m_lRedirectFiles.begin(), m_lRedirectFiles.end(), [this](auto iter) {
		auto sUserFolder = this->GetUserDataPath(iter.first, iter.second);
		if (!IsFile(sUserFolder) && IsFile(iter.first) && !IsJunctionPoint(iter.first.c_str(), FALSE)) {
			//未初始化
			Copy_File(iter.first, sUserFolder, false);
		}
		});
}

int CJunctionPoint::SyncDirectory(const boost::filesystem::path& src_path, const boost::filesystem::path& dst_path,
	std::list<boost::filesystem::path>& sErrorSrcFiles)
{
	auto nErrorCount = Copy_Directory(src_path, dst_path, [this, &src_path, &dst_path](auto src_iter, auto sub_dst_path, auto bsymlink)->bool {
		bool bRet(false);
		if (bsymlink) {
			ULONG Flags(0);
			std::wstring lpTargetDir; //链接的目标
			boost::system::error_code err;
			if (GetJunctionPoint(src_iter->path().c_str(), tmpstr(lpTargetDir, MAX_PATH), MAX_PATH,
				boost::filesystem::is_directory(src_iter->status(err)), &Flags)) {
				if (IsDirectory(sub_dst_path) && !IsJunctionPoint(sub_dst_path.c_str(), TRUE)) {
					RemoveDir(sub_dst_path);
				}
				else if (IsFile(sub_dst_path) && !IsJunctionPoint(sub_dst_path.c_str(), FALSE)) {
					RemoveFile(sub_dst_path);
				}
				//转换指向src内部自己的链接为指向目的地的链接
				//比如 C:\Users\<USER>\Users\Default] 的原链接
				//转换成 D:\UserData\Users\Default User  [D:\UserData\Users\Default] 的新链接
				if (!(Flags & SYMLINK_FLAG_RELATIVE)) {
					boost::system::error_code ec;
					if (string_starts_with(lpTargetDir, src_path.wstring())) {
						auto sRelativeSub = boost::filesystem::relative(lpTargetDir, src_path, ec);
						if (sRelativeSub.filename_is_dot()) {
							lpTargetDir = dst_path.wstring();
						}
						else {
							lpTargetDir = boost::filesystem::absolute(sRelativeSub, dst_path).wstring();
						}
					}
					else if (!m_UserProfile.m_bUserProfileDisableDefault) {
						//将目录里的所有 指向包含 c:\users 的链接 转换为  D:\UserData\Users 的新连接
						if (!m_sSystemUsers.empty()
							&& string_starts_with(lpTargetDir, m_sSystemUsers)) {
							auto sRelativeSub = boost::filesystem::relative(lpTargetDir, m_sSystemUsers, ec);
							if (sRelativeSub.filename_is_dot()) {
								lpTargetDir = GetUserDataPath(m_sSystemUsers, DEFAULT_PROFILE_NEW_NAME).wstring();
							}
							else {
								lpTargetDir = boost::filesystem::absolute(sRelativeSub,
									GetUserDataPath(m_sSystemUsers, DEFAULT_PROFILE_NEW_NAME)).wstring();
							}
						}
						else if (!m_sSystemProgramData.empty()
							&& string_starts_with(lpTargetDir, m_sSystemProgramData)) {
							auto sRelativeSub = boost::filesystem::relative(lpTargetDir, m_sSystemProgramData, ec);
							if (sRelativeSub.filename_is_dot()) {
								lpTargetDir = GetUserDataPath(m_sSystemProgramData, DEFAULT_PROFILE_NEW_NAME).wstring();
							}
							else {
								lpTargetDir = boost::filesystem::absolute(sRelativeSub,
									GetUserDataPath(m_sSystemProgramData, DEFAULT_PROFILE_NEW_NAME)).wstring();
							}
						}
					}
				}
				bRet = CreateJunctionPoint(sub_dst_path.c_str(),
					(Flags & SYMLINK_FLAG_RELATIVE) ? boost::filesystem::absolute(lpTargetDir, sub_dst_path).c_str() : lpTargetDir.c_str(),
					TRUE, boost::filesystem::is_directory(src_iter->status(err)));
			}
			else {
				bRet = false;
			}
		}
		else {
			if (IsFile(sub_dst_path)) {
				//文件都存在比较修改时间和大小
				if (LastWriteTime(src_iter->path()) > LastWriteTime(sub_dst_path)) {
					//只拷贝最新的文件
					bRet = true;
				}
			}
			else {
				//目标文件不存在，直接拷贝
				bRet = true;
			}
		}
		return bRet;
		}, sErrorSrcFiles);
	WRITE_ISCSIFILELOG(_T("SyncDirectory, src : ") << src_path.wstring() << _T(", to dst : ") << dst_path.wstring() << _T(", nErrorCount : ") << nErrorCount);
	return nErrorCount;
}
//添加转移目录
bool CJunctionPoint::AddUsersProfile(const std::wstring& sPathEnv, const std::wstring& sNewName, bool bCheckOnly /*= false*/)
{
	auto sUserPath = ReplaceEnvVarInPath(sPathEnv);
	if (!IsVolumeRoot(sUserPath)) {
		//检查是否已经包含该目录的上级目录
		auto sUserPathWithEnd = string_ends_with(sUserPath, _T("\\")) ? sUserPath : sUserPath + _T("\\");
		auto iter = std::find_if(m_lUsersProfile.begin(), m_lUsersProfile.end(), [&sUserPathWithEnd](auto iter)->bool {
			return string_starts_with(sUserPathWithEnd, string_ends_with(iter.first, _T("\\")) ? iter.first : iter.first + _T("\\"));
			});
		if (iter == m_lUsersProfile.end()) {
			if (!bCheckOnly) {
				m_lUsersProfile.insert(std::make_pair(sUserPath, sNewName));
				WRITE_ISCSIFILELOG(_T("UserDataIni load Users Profile folder: ") << sUserPath.c_str());
			}
			return true;
		}
	}
	return false;
}
//添加转移文件
bool CJunctionPoint::AddRedirectFile(const std::wstring& sPathEnv, const std::wstring& sNewName)
{
	boost::filesystem::path p(sPathEnv);
	if (p.has_filename()) {
		//先检查文件所在的目录是否已经转移, true 表示能够转移, 根目录不检查
		if (IsVolumeRoot(p.parent_path().wstring())
			|| AddUsersProfile(p.parent_path().wstring(), sNewName, true)) {
			auto sFilePath = ReplaceEnvVarInPath(sPathEnv);
			if (!IsVolumeRoot(sFilePath)) {
				auto iter = m_lRedirectFiles.find(sFilePath);
				if (iter == m_lRedirectFiles.end()) {
					m_lRedirectFiles.insert(std::make_pair(sFilePath, sNewName));
					WRITE_ISCSIFILELOG(_T("UserDataIni load Users redirect file: ") << sFilePath.c_str());
					return true;
				}
			}
		}
	}
	return false;
}

bool CJunctionPoint::FixDocumentsandSettings(bool bRestore)
{
	bool ret(false);
	if (!m_UserProfile.m_bUserProfileDisableDefault && !m_sSystemUsers.empty() && !IsXp2003Version()) {
		auto pDocumentsandSettings = boost::filesystem::absolute(_T("Documents and Settings"), GetOperatingSystemRootPath());
		std::wstring lpTargetDir; //链接的目标
		ULONG Flags(0);
		if (GetJunctionPoint(pDocumentsandSettings.c_str(), tmpstr(lpTargetDir, MAX_PATH), MAX_PATH, TRUE, &Flags)) {
			if (bRestore) {
				if (wstring2icmp(lpTargetDir, m_sSystemUsers)) {
					ret = CreateJunctionPoint(pDocumentsandSettings.c_str(), m_sSystemUsers.c_str(), TRUE, TRUE);
				}
			}
			else {
				if (m_UsersInit) {
					auto sUserFolder = GetUserDataPath(m_sSystemUsers, DEFAULT_PROFILE_NEW_NAME);
					if (wstring2icmp(lpTargetDir, sUserFolder.wstring())) {
						ret = CreateJunctionPoint(pDocumentsandSettings.c_str(), sUserFolder.c_str(), TRUE, TRUE);
					}
				}
			}
		}
	}
	return ret;
}

std::wstring CJunctionPoint::ReplaceEnvVarInPath(const std::wstring& sPath)
{
	std::wstring sRet(sPath);
	m_hUserDataIni.EnumKeyOfSection(MAKELINK_INI_ENV_SECTION, [&sRet](const std::wstring& sKey, const std::wstring& sValue)->bool {
		string_replace(sRet, sKey, sValue);
		return true;
		});
	return sRet;
}

bool CJunctionPoint::ClearSystemIconCache()
{
	std::wstring sUserProfiles;
	if (m_hUserDataIni.GetValue(MAKELINK_INI_ENV_SECTION, USERPROFILE_VAR, sUserProfiles)
		&& !sUserProfiles.empty()) {
		auto sIconCache = boost::filesystem::absolute(ICONCACHE_DB_FILEPATH, sUserProfiles);
		auto tIconCacheWriteTime = SymbolicLinkFile_LastWriteTime(sIconCache);
		auto current_time = std::time(nullptr);  // 获取当前时间
		std::time_t refresh_time(0);
		int month_in_seconds = 30 * 24 * 60 * 60;  // 一个月的秒数
		if (!m_hUserDataIni.GetValue(MAKELINK_INI_SET_SECTION, _T("IconCacheTime"), refresh_time)
			|| std::difftime(current_time, refresh_time) > month_in_seconds
			|| std::difftime(current_time, tIconCacheWriteTime) > month_in_seconds) {
			//删除缓存数据库
			auto ret = RemoveSymbolicLinkFile(sIconCache);
			//删除缓存目录
			auto sIconCacheFolder = boost::filesystem::absolute(ICONCACHE_EXPLORER_FOLDERPATH, sUserProfiles);
			Find_Directory(sIconCacheFolder, [](const boost::filesystem::path& p)->bool {
				if (p.extension().wstring() == _T(".db")) {
					auto ret = RemoveFile(p);
					//WRITE_ISCSIFILELOG(_T("ClearSystemIconCache remove IconCache folder file: ") << p.c_str() << _T(", result:") << ret);
				}
				return true;
				});
			m_hUserDataIni.SetValue(MAKELINK_INI_SET_SECTION, _T("IconCacheTime"), current_time);
			WRITE_ISCSIFILELOG(_T("ClearSystemIconCache remove IconCache.db: ") << sIconCache.c_str()
				<< _T(", result:") << ret << _T(", IconCacheTime:") << current_time);
			return SaveUserDataIni();
		}
	}
	return false;
}

//#define CURRENT_USER_SUBKEY_NAME _T("LOADUSER")
//bool CJunctionPoint::RestoreDesktopLayout()
//{
//	bool ret(false);
//	std::wstring sUserProfiles;
//	if (m_hUserDataIni.GetValue(MAKELINK_INI_ENV_SECTION, USERPROFILE_VAR, sUserProfiles)
//		&& !sUserProfiles.empty()) {
//		auto pUserHivePath = boost::filesystem::absolute(_T("NTUSER.DAT"), sUserProfiles);
//		//传统方式
//		//https://docs.microsoft.com/en-us/windows/win32/api/winreg/nf-winreg-regloadkeya
//		SetRegisterPrivilege();
//		if (LoadHiveFile(HKEY_USERS, CURRENT_USER_SUBKEY_NAME, pUserHivePath.c_str())) {
//			//读取当前设置
//			auto para_reg = wstring_format(_T("%s\\%s"), CURRENT_USER_SUBKEY_NAME, DESKTOP_LAYOUT_REG_PATH);
//			CIniFileReg hIniFileReg;
//			hIniFileReg.Set(HKEY_USERS, para_reg.c_str(), _T(""), MAKELINK_INI_DESKTOP_ICONLAYOUTS_KEY);
//			ret = hIniFileReg.RestoreFromIniFile(m_hUserDataIni);
//			UnLoadHiveFile(HKEY_USERS, CURRENT_USER_SUBKEY_NAME);
//		}
//		else {
//			WRITE_ISCSIFILELOG(_T("RestoreDesktopLayout LoadHiveFile failure: ") << pUserHivePath.c_str());
//		}
//	}
//	return ret;
//}