﻿#pragma once
#include "../iShareDll/LanguageFunction.hpp"
#include "../iShareDll/LoadString.hpp"
#define IDD_PAGE_RESET_INDEX (-1)
extern HINSTANCE g_hInstance;							// 当前实例
// std::wstring LoadStringFromID(int nResourceID);
//extern CLanguageIni* g_languages;
//extern BOOL g_Runinwinpe;
#define TRANSLATE(x) (g_languages?(g_languages->Translate(x)):(x))
#define LOADSTRING(x) TRANSLATE(LoadString_W(x, g_hInstance))
#define LoadStringFromID(x) LOADSTRING(x)
//翻译函数
void TranlateCWnd(HWND pWnd);
BOOL CALLBACK EnumChildProc(HWND hwndChild, LPARAM lParam);
void ParseCmdLine(LPTSTR    lpCmdLine);