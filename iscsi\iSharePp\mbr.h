#pragma once
// 基本大小和签名定义
#define LBA_SIZE_2TB ((DWORD64)0xFFFFFFFF)
#ifndef PAGE_SIZE
#define PAGE_SIZE 0x1000
#endif
#ifndef SECSIZE
#define SECSIZE (0x200) //默认扇区大小
#endif
#ifndef SECSIZE_4K
#define SECSIZE_4K (0x1000) // 默认扇区大小
#endif
#ifndef MBRSIGN
#define MBRSIGN (0xAA55)
#endif
#ifndef DBR_SIZE
#define DBR_SIZE (512)
#endif
#define BITMAP_BYTESIZE 8 // 位图里,每个字节的大小

// fat32相关
#define FAT32_STARTCLUTER_NO 2
#define FAT32_ENDCLUTER_VALUE 0x0FFFFFFF
#define FAT32_BADCLUTER_VALUE 0x0FFFFFF7
#define FAT32_DBR_NAME "FAT32"
//NTFS相关
#define NTFS_DBR_NAME "NTFS"
#define FAT32_DBR_NAME "FAT32"

//分区类型
#define PARTITION_TYPE_UNUSED 0x00
#define PARTITION_TYPE_EXTEND 0x05
#define PARTITION_TYPE_EXTENDWIN95 0x0F
#define PARTITION_TYPE_NTFS 0x07
#define PARTITION_TYPE_NTFSHIDDEN 0x17
#define PARTITION_TYPE_NTFS_LBA 0x27		   // NTFS文件系统（使用LBA寻址）
#define PARTITION_TYPE_MICROSOFT_RESERVED_HIDE 0x27
#define PARTITION_TYPE_FAT32 0x0B
#define PARTITION_TYPE_FAT32WIN95 0x0C
#define PARTITION_TYPE_FAT32HIDDEN 0x1B
#define PARTITION_TYPE_FAT32WIN95HIDDEN 0x1C
#define PARTITION_TYPE_PROTECT 0xEE

#define PARTITION_TYPE_EMPTY 0x00			   // 空分区
#define PARTITION_TYPE_FAT12_CHS 0x01		   // FAT12文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT16_CHS 0x04		   // FAT16文件系统（使用CHS寻址）
#define PARTITION_TYPE_EXTENDED_CHS 0x05	   // 扩展分区（使用CHS寻址）
#define PARTITION_TYPE_FAT16B_CHS 0x06		   // FAT16文件系统（使用CHS寻址）
#define PARTITION_TYPE_NTFS_CHS 0x07		   // NTFS文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT32_CHS 0x0B		   // FAT32文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT32_LBA 0x0C		   // FAT32文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT16_LBA 0x0E		   // FAT16文件系统（使用LBA寻址）
#define PARTITION_TYPE_EXTENDED_LBA 0x0F	   // 扩展分区（使用LBA寻址）
#define PARTITION_TYPE_NTFS_LBA 0x27		   // NTFS文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT32X_LBA 0x42		   // FAT32X文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT16X_LBA 0x42		   // FAT16X文件系统（使用LBA寻址）
#define PARTITION_TYPE_LINUX_SWAP 0x82		   // Linux交换分区
#define PARTITION_TYPE_LINUX_EXT2 0x83		   // Linux EXT2/EXT3文件系统
#define PARTITION_TYPE_LINUX_LVM 0x8E		   // Linux LVM分区
#define PARTITION_TYPE_BIOS_BOOT 0xEE		   // BIOS引导分区
#define PARTITION_TYPE_GPT 0xEE				   // GPT分区类型
#define PARTITION_TYPE_MICROSOFT_RESERVED 0xEE // Microsoft保留分区

enum
{
	SYSID_UNUSED = 0,	// 未使用的系统标识，取值: 0
	SYSID_EXTENDED = 5, // 扩展分区的系统标识，取值: 5
	SYSID_BIGFAT = 6,	// 大型FAT分区的系统标识，取值: 6
	SYSID_IFS = 7		// IFS分区的系统标识，取值: 7
};

// 隐藏磁盘字母相关定义
#ifdef OEM_HAPPYSHARE_UPDATE
//为了单独对游戏更新机设置盘符而确定的安全位置
#define CLIENTLETTER_OFFSET (DBR_SIZE-68)
#else
#define CLIENTLETTER_OFFSET (DBR_SIZE-3)
#endif
#ifndef HIDN_DISK_LETTER
#define HIDN_DISK_LETTER (0xFF)
#define HIDN_DISK_LETTER1 (-1)
#define HIDN_DISK_DISKLESS_LETTER (0xFE)
#define HIDN_DISK_LETTER2 (-2)
#define HIDN_DISK_VHDBOOT_LETTER (0xFD)
#define HIDN_DISK_LETTER3 (-3)
#endif
#ifndef SYSTEMLETTER_CHAR
#define SYSTEMLETTER_CHAR 'C'
#endif
#ifndef LASTLETTER_CHAR
#define LASTLETTER_CHAR 'Z'
#endif

inline bool IsGoodDiskletter(char dosletter) {
	return (dosletter > SYSTEMLETTER_CHAR && dosletter <= LASTLETTER_CHAR) ? true : false;
};
inline bool IsHidedDiskletter(char dosletter) {
	return (dosletter == HIDN_DISK_LETTER1 || dosletter == HIDN_DISK_LETTER2 || dosletter == HIDN_DISK_LETTER3) ? true : false;
};

inline bool IsGoodDiskletterSet(char dosletter) {
	return (IsGoodDiskletter(dosletter) || IsHidedDiskletter(dosletter)) ? true : false;
};

#pragma pack( 1 )

//
// Boot record disk partition table entry structure format.
//

#ifndef PARTITION_DESCRIPTOR_DEFINED
#define PARTITION_DESCRIPTOR_DEFINED
typedef struct _PARTITION_DESCRIPTOR
{
	UCHAR ActiveFlag;							// 0 引导标志，表示分区是否可引导
	UCHAR StartingTrack;						// 1 起始磁道号，未使用
	UCHAR StartingCylinderLsb;					// 2 起始柱面号（低字节），未使用
	UCHAR StartingCylinderMsb;					// 3 起始柱面号（高字节），未使用
	UCHAR PartitionType;						// 4 分区类型，12 位 FAT、16 位 FAT 等
	UCHAR EndingTrack;							// 5 结束磁道号，未使用
	UCHAR EndingCylinderLsb;					// 6 结束柱面号（低字节），未使用
	UCHAR EndingCylinderMsb;					// 7 结束柱面号（高字节），未使用
	UCHAR StartingSectorLsb0;					// 8 隐藏扇区数（低字节）
	UCHAR StartingSectorLsb1;					// 9 隐藏扇区数（中低字节）
	UCHAR StartingSectorMsb0;					// 10 隐藏扇区数（中高字节）
	UCHAR StartingSectorMsb1;					// 11 隐藏扇区数（高字节）
	UCHAR PartitionLengthLsb0;					// 12 分区长度（低字节），以扇区为单位
	UCHAR PartitionLengthLsb1;					// 13 分区长度（中低字节）
	UCHAR PartitionLengthMsb0;					// 14 分区长度（中高字节）
	UCHAR PartitionLengthMsb1;					// 15 分区长度（高字节）
} PARTITION_DESCRIPTOR, * PPARTITION_DESCRIPTOR; // 16 分区描述符结构体定义
#endif // PARTITION_DESCRIPTOR_DEFINED

#define PSTART(p) (                          \
	(ULONG)((p)->StartingSectorLsb0) +       \
	(ULONG)((p)->StartingSectorLsb1 << 8) +  \
	(ULONG)((p)->StartingSectorMsb0 << 16) + \
	(ULONG)((p)->StartingSectorMsb1 << 24))

#define PLENGTH(p) (                          \
	(ULONG)((p)->PartitionLengthLsb0) +       \
	(ULONG)((p)->PartitionLengthLsb1 << 8) +  \
	(ULONG)((p)->PartitionLengthMsb0 << 16) + \
	(ULONG)((p)->PartitionLengthMsb1 << 24))

//
// Number of partition table entries
//

#define NUM_PARTITION_TABLE_ENTRIES     4

//
// Partition table record and boot signature offsets in 16-bit words.
//

#define PARTITION_TABLE_OFFSET (0x1be / 2)
#define BOOT_SIGNATURE_OFFSET ((0x200 / 2) - 1)

//
// Boot record signature value.
//

#define BOOT_RECORD_SIGNATURE          MBRSIGN

//
// Initial size of the Partition list structure.
//

#define PARTITION_BUFFER_SIZE 2048

//
// Partition active flag - i.e., boot indicator
//

#define PARTITION_ACTIVE_FLAG          0x80

#ifndef PACKED_BOOT_SECTOR_MBR_DEFINED
#define PACKED_BOOT_SECTOR_MBR_DEFINED
typedef struct _PACKED_BOOT_SECTOR_MBR
{
	UCHAR BootStrap[0x1B8];										  // 0 启动代码
	ULONG dwDiskMark;											  // 440 磁盘签名
	UCHAR dwReserved[2];										  // 444 保留
	PARTITION_DESCRIPTOR bsPartEnts[NUM_PARTITION_TABLE_ENTRIES]; // 446 分区描述符数组
	USHORT bsSignature;											  // 510 boot block 签名 (0xAA55h)
} PACKED_BOOT_SECTOR_MBR, * PPACKED_BOOT_SECTOR_MBR;				  // 512 打包引导扇区 MBR 结构体定义
#endif // PACKED_BOOT_SECTOR_MBR_DEFINED

typedef struct tag_PtItem
{
	BYTE	bState;
	BYTE	bStartHead;
	BYTE	bStartSec;
	BYTE	bStartCyl;
	BYTE	bType;
	BYTE	bEndHead;
	BYTE	bEndSec;
	BYTE	bEndCyl;
	DWORD	dwStartSec;
	DWORD	dwTotalSecs;
}PTITEM, * LPPTITEM;

typedef struct _mbr
{
	BYTE	bootcode[0x1BE];
	PTITEM	bsPartEnts[4];
	WORD	bsSignature;			// boot block signature (0xAA55h)
}MBR, * PMBR;

typedef struct _PartEntry_
{
	BYTE	peBootable;				// 80h = bootable, 00h = not
	BYTE	peBeginHead;			// beginning head
	BYTE	peBeginSector;			// beginning sector
	BYTE	peBeginCylinder;		// beginning cylinder
	BYTE	peFileSystem;			// ID of filesystem
	BYTE	peEndHead;				// ending head
	BYTE	peEndSector;			// ending sector
	BYTE	peEndCylinder;			// ending cylinder
	DWORD	peStartSector;			// starting sector
	DWORD	peSectors;				// total sectors
}PartEntry;

typedef struct _BootSectorFat32_
{
	BYTE		bsJump[3];				// jmp to bootstrap instruction 0
	char		bsOemName[8];			// OEM name and version			3

	WORD		bsBytesPerSec;			// bytes per sector				11
	BYTE		bsSecPerClust;			// sectors per cluster			13
	WORD		bsResSectors;			// num reserved sectors			14
	BYTE		bsFats;					// num file allocation tables	16
	WORD		bsRootDirEnts;			// num root-directory entries	17
	WORD		bsSectors;				// total num secs (<= 32 megs)	19
	BYTE		bsMedia;				// media descriptor				21
	WORD		bsFatSecs;				// num sectors per FAT			22
	WORD		bsSecPerTrack;			// sectors per track (head)		24
	WORD		bsHeads;				// num heads					26
	DWORD		bsHiddenSecs;			// num hidden sectors			28
	DWORD		bsHugeSectors;			// total num sectors (> 32 megs)32
	DWORD		bsBigSectorsPerFat;		// total sectors per FAT		36
	WORD		bsExtFlags;				// extended flags				40
	WORD		bsFS_Version;			// filesystem version			42
	DWORD		bsRootDirStrtClus;		// first cluster of root dir	44
	WORD		bsFsInfoSec;			// sector of FSInfo sec			48
	WORD		bsBkUpBootSec;			// sector of backup boot sec	50
	WORD		bsReserved2[6];			// reserved						52

	BYTE		bsDriveNumber;			// drive number (80h)			64
	BYTE		bsReserved1;			// reserved						65
	BYTE		bsBootSignature;		// extended boot signature (29h)66
	DWORD		bsVolumeId;				// volume ID number				67
	char		bsVolumeLabel[11];		// volume label					71
	char		bsFileSysType[8];		// filesystem type				82
	BYTE		padding1[356];			// padding to fill out first sec90
	PartEntry	bsPartEnts[4];			// partition entry table		446
	WORD		bsSignature;			// boot block signature (0xAA55h)510
}BOOTSECTORFAT32, * PBOOTSECTORFAT32;

typedef struct	tag_NTFSBOOT
{
	BYTE			bJmp[3];            // 0
	BYTE			bNTFlags[4];		// 3  // like 'N','T','F','S'
	BYTE			bReserve1[4];       // 7
	WORD			wBytePerSector;     // b
	BYTE			bSectorPerCluster;  // d
	WORD			wReserveSectors;    // e
	BYTE			bFatNum;			// 0x10 // alway is 0
	WORD			wRootDirNum;		// 0x11 // alway is 0
	WORD			wSectorOfParti;     // 0x13 // alway is 0
	BYTE			bMedium;            // 0x15
	WORD			wSectorPerFat;		// 0x16 // alway is 0
	WORD			wSectorPerTrack;    // 0x18
	WORD			wHeadNum;           // 0x1a
	DWORD			dwHideSector;       // 0x1c
	DWORD			dwUnParti;     // 0x20 // alway is 0
	BYTE			bDeviceFlag;        //0x24
	BYTE			bReserve2;          //0x25
	WORD			wReserve3;          //0x26
	DWORD			ulSectorsOfParti; //0x28 all sectors - 1
	DWORD			ulSectorsOfPartiHigh; //0x2c all sectors - 1
	DWORD			ulMFTAddr;            //0x30
	DWORD			ulMFTAddrHigh;        //0x34
	DWORD			ulMFTMirrAddr;        //0x38
	DWORD			ulMFTMirrAddrHigh;    //0x3c
	char			bClusterPerFile;  //0x40
	BYTE			bReserve4[3];     //0x41
	char			bClusterPerINDX;  //0x44
	BYTE			bReserve5[3];     //0x45
	BYTE			bSerialID[8];     //0x48
	DWORD			bChecksum;		//0x50
	BYTE			cBootCode[426];	//0x54  boot code
	USHORT			bsSignature;	//0x1FE boot block signature (0xAA55h)
} NTFSBOOT, * PNTFSBOOT, * LPNTFSBOOT;

#pragma pack()
