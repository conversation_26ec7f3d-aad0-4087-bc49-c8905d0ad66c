﻿#include "stdafx.h"
#include "../iShareDll/UsbBootDef.h"
#include "../iShareDriverLib/bind.hpp"
#include "../iShareDriverLib/RegKey.h"
#include "iShareFilterDevice.h"
#include "iShareFilter.h"

static const PWCHAR sanbootconf_device_symlink[] = {
	L"\\DosDevices\\iShareFilter"
};
static const PWCHAR iSharePnp_DeviceName = L"\\Device\\iShareFilter";

/**
 * @brief 构造函数，初始化 CiShareFilter 对象
 * @param DriverObject 驱动对象指针
 * @param RegistryPath 注册表路径
 */
CiShareFilter::CiShareFilter(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
	: CminiFilter(DriverObject, RegistryPath)
{
}

/**
 * @brief 析构函数，释放 CiShareFilter 相关资源
 */
CiShareFilter::~CiShareFilter(void)
{
}

/**
 * @brief 过滤器入口函数，启动过滤功能
 * @param DriverObject 驱动对象指针
 * @param RegistryPath 注册表路径
 * @return NTSTATUS 状态码
 */
NTSTATUS CiShareFilter::DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(DriverObject);
	UNREFERENCED_PARAMETER(RegistryPath);
	NTSTATUS status = STATUS_SUCCESS;
	do
	{
		CWDMDevice_ptr pNewDevice(new iShareFilterDevice(this, iSharePnp_DeviceName));
		if (pNewDevice.get()) {
			//这个设备是NEITHER_IO，如果设置为DO_BUFFERED_IO会导致数据被重复释放
			status = CreateDevice(pNewDevice, 0);
			if (NT_SUCCESS(status)) {
				for (ULONG i = 0; i < _countof(sanbootconf_device_symlink); i++)
					pNewDevice->CreateSymbolicLink(sanbootconf_device_symlink[i]);
			}
		}
		// 启动底层 MiniFilter 过滤器, 当 OnMiniFilterInstanceSetup 初始化 userdata 盘时，开始读取参数，再开始转移文件和注册表
		status = StartFilter();
		if (!NT_SUCCESS(status)) {
			break;
		}
	} while (false);
	return status;
}

/**
 * @brief 实例上下文关闭回调，释放相关资源
 */
VOID CiShareFilter::OnMFInstanceContextClose(_In_ PFLT_CONTEXT Context,
	_In_ FLT_CONTEXT_TYPE ContextType)
{
	UNREFERENCED_PARAMETER(Context);
	UNREFERENCED_PARAMETER(ContextType);
	FLT_ASSERT(ContextType == FLT_INSTANCE_CONTEXT);

	auto InstanceContext = (CMFInstanceContext_Ptr*)Context;
	if (InstanceContext) {
		if (*InstanceContext) {
			PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
				"OnMFInstanceContextClose: InstanceContext Close, volume name = %wZ\n",
				(PCUNICODE_STRING)(*InstanceContext)->GetDosNtName());
			//(*InstanceContext)->Close();
			(*InstanceContext).reset();
		}
	}
}

//FltSetFileContext 也可能导致释放，因此这里不能关闭，也是简单的减少引用
VOID CiShareFilter::OnMFFileContextClose(_In_ PFLT_CONTEXT Context,
	_In_ FLT_CONTEXT_TYPE ContextType)
{
	UNREFERENCED_PARAMETER(Context);
	UNREFERENCED_PARAMETER(ContextType);
	FLT_ASSERT(ContextType == FLT_FILE_CONTEXT);

	auto FileContext = (CMFFileContext_Ptr*)Context;
	if (FileContext) {
		if (*FileContext) {
			//PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
			//	"OnMFFileContextClose: FileContext Close, full path = %wZ\n",
			//	(PCUNICODE_STRING)(*FileContext)->GetRelativePath());
			//(*FileContext)->Close();
			(*FileContext).reset();
		}
	}
}

VOID CiShareFilter::OnMFStreamHandleContextClose(_In_ PFLT_CONTEXT Context,
	_In_ FLT_CONTEXT_TYPE ContextType)
{
	UNREFERENCED_PARAMETER(Context);
	UNREFERENCED_PARAMETER(ContextType);
	FLT_ASSERT(ContextType == FLT_STREAMHANDLE_CONTEXT);

	auto StreamHandleContext = (CMFStreamHdlCtx_Ptr*)Context;
	if (StreamHandleContext) {
		if (*StreamHandleContext) {
			//PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
			//	"OnMFStreamHandleContextClose: StreamHandleContext Close\n");
			//(*StreamHandleContext)->Close();
			(*StreamHandleContext).reset();
		}
	}
}

/**
 * @brief MiniFilter 创建文件的预处理回调
 * @details 此函数在文件创建操作之前被调用，用于检查和处理文件创建请求。
 * @param Cbd 回调数据结构，包含文件操作的详细信息。
 * @param FltObjects 相关的过滤器对象。
 * @param CompletionContext 用于传递上下文信息到后续回调。
 * @return 返回预处理的状态。
 */
FLT_PREOP_CALLBACK_STATUS
CiShareFilter::OnMiniFilterPreCreateCallback(_Inout_ PFLT_CALLBACK_DATA Cbd,
	_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_Flt_CompletionContext_Outptr_ PVOID* CompletionContext)
{
	UNREFERENCED_PARAMETER(Cbd);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	PAGED_CODE();

	NTSTATUS status = STATUS_SUCCESS;
	FLT_PREOP_CALLBACK_STATUS callbackStatus = FLT_PREOP_SUCCESS_NO_CALLBACK;
	CMFFileContext_Ptr* pFileContext = nullptr;
	do
	{
		CMFContextRef InstanceContext;
		// 获取实例上下文，确保文件操作与正确的实例关联
		auto status1 = FltGetInstanceContext(FltObjects->Instance, &InstanceContext);
		if (NT_SUCCESS(status1) && InstanceContext) {
			auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
			if (*pInstanceContext) {
				if ((*pInstanceContext)->IsUserData() || !GetUserData() || !IsEnableFilter()) {
					// 如果是用户数据或过滤器未启用，则跳过监控
					break;
				}
				else {
					// 检查是否需要重定向路径
					if (GetUserData()) {
						pFileContext = (*pInstanceContext)->GetCacheFileContext(Cbd, FltObjects, GETFILENAMEINFO_TYPE_CREATE);
						if (pFileContext && (*pFileContext) && (*pInstanceContext)->CheckNeedRedirectPath(*pFileContext, true)) {
							status = GetUserData()->Redirect((*pInstanceContext), *pFileContext, Cbd, FltObjects);
						}
					}
				}
			}
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ERROR,
				"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
			break;
		}
	} while (false);

	if (status == STATUS_REPARSE) {
		callbackStatus = FLT_PREOP_COMPLETE;
	}
	else if (!NT_SUCCESS(status)) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR,
			"OnMiniFilterPreCreateCallback: Failed with status 0x%x\n", status);
		Cbd->IoStatus.Status = status;
		callbackStatus = FLT_PREOP_COMPLETE;
	}
	else if (pFileContext && (*pFileContext) && (*pFileContext)->IsDirectory()) {
		// 如果是目录操作，保存文件上下文以便后续处理, 需要合并目录
		*CompletionContext = pFileContext;
		callbackStatus = FLT_PREOP_SUCCESS_WITH_CALLBACK;
	}
	return callbackStatus;
}

/**
 * @brief MiniFilter 创建文件的后处理回调
 */
FLT_POSTOP_CALLBACK_STATUS CiShareFilter::OnMiniFilterPostCreateCallback(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _In_ PVOID CompletionContext, _In_ FLT_POST_OPERATION_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	UNREFERENCED_PARAMETER(Flags);

	do
	{
		// 检查文件打开操作是否成功
		if (NT_SUCCESS(Data->IoStatus.Status)) {
			SaveFileContext(Data, FltObjects, (CMFFileContext_Ptr*)CompletionContext);
		}
	} while (false);
	return FLT_POSTOP_FINISHED_PROCESSING;
}

/**
 * @brief MiniFilter 清理文件的后处理回调
 */
FLT_POSTOP_CALLBACK_STATUS CiShareFilter::OnMiniFilterPostCleanupCallback(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _In_opt_ PVOID CompletionContext, _In_ FLT_POST_OPERATION_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(CompletionContext);

	PAGED_CODE();

	NTSTATUS Status;

	if (!FlagOn(Flags, FLTFL_POST_OPERATION_DRAINING)) {
		CMFContextRef FileSteamContext;
		Status = FltGetStreamHandleContext(FltObjects->Instance,
			FltObjects->FileObject,
			&FileSteamContext);

		if (NT_SUCCESS(Status)) {
			auto streamHandleCtx = (CMFStreamHdlCtx_Ptr*)FileSteamContext.GetObject();
			if (streamHandleCtx && (*streamHandleCtx)) {
				auto pDirNotifyContext = (*streamHandleCtx)->GetDirNotifyContext();
				if (pDirNotifyContext) {
					pDirNotifyContext->NcStreamHandleContextNotCleanup(Data);
				}
			}
		}
	}

	return FLT_POSTOP_FINISHED_PROCESSING;
}

/**
 * @brief MiniFilter 卸载时的处理
 */
NTSTATUS CiShareFilter::OnMiniFilterUnload(_In_ FLT_FILTER_UNLOAD_FLAGS Flags)
{
	StopRegisterFilter();
	return CminiFilter::OnMiniFilterUnload(Flags);
}

/**
 * @brief 根据盘符查找实例上下文
 */
CMFInstanceContext_Ptr CiShareFilter::FindInstanceContext(WCHAR cDiskLetter)
{
	CMFInstanceContext_Ptr InstanceContext;
	for (auto iter = m_listInstanceContext.begin(); iter != m_listInstanceContext.end(); ++iter) {
		if ((*iter)->IsDiskLetter(cDiskLetter)) {
			InstanceContext = *iter;
			break;
		}
	}
	return InstanceContext;
}

/**
 * @brief 根据硬盘卷标查找实例上下文
 */
CMFInstanceContext_Ptr CiShareFilter::FindInstanceContext(const CPath& pHarddiskVolume)
{
	CMFInstanceContext_Ptr InstanceContext;
	for (auto iter = m_listInstanceContext.begin(); iter != m_listInstanceContext.end(); ++iter) {
		if ((*iter)->IsVolumeName(pHarddiskVolume)) {
			InstanceContext = *iter;
			break;
		}
	}
	return InstanceContext;
}

/**
 * @brief 将环境变量中的短路径转换为长路径
 */
VOID CiShareFilter::EnvPathShortNameToLongPath()
{
	m_EnvPathMultiString.clear();
	m_EnvPath = m_FilterSet.GetEnvPath();
	for (auto iter = m_EnvPath.begin(); iter != m_EnvPath.end(); ++iter) {
		auto& strValue = iter.second();
		if (strValue.IsEmpty()) {
			continue;
		}
		CPath path(strValue);
		if (path.HaveShortName() && path.HaveRootCZ()) {
			auto pInstanceContext = FindInstanceContext(path.GetRootCZ());
			if (pInstanceContext) {
				CPath pRootCZ;
				auto pathRelative = path.GetRelative(&pRootCZ);
				CPath pFullName;
				auto status1 = QueryFullPath(pInstanceContext->GetFilterRef(), pInstanceContext->GetInstanceRef(), pInstanceContext->GetVolumeName() + pathRelative, pFullName);
				if (NT_SUCCESS(status1)) {
					strValue = (pRootCZ + pFullName).GetPath();
					PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
						"EnvPathShortNameToLongPath: key = %wZ, value = %wZ\n",
						(PCUNICODE_STRING)iter.first(), (PCUNICODE_STRING)strValue);
				}
			}
		}
		m_EnvPathMultiString.MultiAppendBack(CStringW::Format_String(L"%wZ=%wZ", (PCUNICODE_STRING)iter.first(), (PCUNICODE_STRING)strValue));
	}
}

/**
 * @brief 清空黑白名单
 */
VOID CiShareFilter::ClearBlackWhiteList()
{
	for (auto iter = m_listInstanceContext.begin(); iter != m_listInstanceContext.end(); ++iter) {
		(*iter)->ClearBlackWhiteList();
	}
}

/**
 * @brief 设置文件路径黑名单或白名单
 */
VOID CiShareFilter::SetFilePathBlackWhiteList(const CStringW& FilePath, CMFInstanceContext::BLACKWHITE_TYPE bBlackType)
{
	CStringList FilePathList;
	if (FilePath.IsMultiFormat())
		MultiStrings2List(FilePath, FilePathList);
	else
		SplitStrings2List(FilePath, FilePathList, WSPLIT_STRING, FALSE);
	SetFilePathBlackWhiteList(FilePathList, bBlackType);
}

/**
 * @brief 设置文件路径黑名单或白名单
 */
VOID CiShareFilter::SetFilePathBlackWhiteList(const CStringList& FilePathList, CMFInstanceContext::BLACKWHITE_TYPE bBlackType)
{
	CMFInstanceContext_Ptr pInstanceContext;
	for (auto iter = FilePathList.begin(); iter != FilePathList.end(); ++iter) {
		auto& strValue = *iter;
		if (strValue.IsEmpty()) {
			continue;
		}

		//去掉环境变量
		for (const auto& sEnv : m_EnvPath) {
			if (IsPathMatched(strValue, sEnv.Key)) {
				strValue.Replace(sEnv.Key, sEnv.Value, TRUE);
				break;
			}
		}

		CPath path(strValue);
		if (path.HaveRootCZ()) {
			if (!pInstanceContext || !pInstanceContext->IsDiskLetter(path.GetRootCZ())) {
				pInstanceContext = FindInstanceContext(path.GetRootCZ());
			}
			if (pInstanceContext) {
				CPath pRootCZ;
				auto pathRelative = path.GetRelative(&pRootCZ);
				if (path.HaveShortName()) {
					CPath pFullName;
					auto status1 = QueryFullPath(pInstanceContext->GetFilterRef(), pInstanceContext->GetInstanceRef(), pInstanceContext->GetVolumeName() + pathRelative, pFullName);
					if (NT_SUCCESS(status1)) {
						strValue = (pRootCZ + pFullName).GetPath();
						PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
							"SetFilePathBlackWhiteList: full path %wZ\n", (PCUNICODE_STRING)strValue);
						pathRelative = pFullName;
					}
				}
				pInstanceContext->AppendBlackWhiteList(pathRelative, bBlackType);
			}
		}
	}
}

/**
 * @brief 设置注册表项文件黑名单 (优化版本)
 */
VOID CiShareFilter::SetRegisterHiveFileBlackList()
{
	m_FilePathBlackSystem.clear();

	// 获取Windows根路径信息
	const CPath sWindowsRoot = m_FilterSet.GetWindowsRoot();
	CPath pRootWindows;
	const auto pathWindows = sWindowsRoot.GetRelative(&pRootWindows);

	// 查找Windows实例上下文
	CMFInstanceContext_Ptr pInstanceContextWindows;
	if (pRootWindows.HaveRootHarddiskVolume())
		pInstanceContextWindows = FindInstanceContext(pRootWindows.GetRootHarddiskVolume());
	else if (pRootWindows.HaveRootCZ())
		pInstanceContextWindows = FindInstanceContext(pRootWindows.GetRootCZ());

	// 预计算Windows系统路径和配置路径
	CPath pathConfig;
	CPath windowsDosNtName;
	if (pInstanceContextWindows) {
		windowsDosNtName = pInstanceContextWindows->GetDosNtName();
		pathConfig = pathWindows + L"System32\\config";

		// 批量处理Windows系统目录黑名单 - 避免重复的路径计算
		static const LPCWSTR systemPaths[] = {
			L"System32\\config",
			L"System32\\drivers",
			L"System32\\DriverStore\\FileRepository"
		};

		for (const auto& systemPath : systemPaths) {
			const CPath fullSystemPath = pathWindows + systemPath;
			const CStringW fullPath = (windowsDosNtName + fullSystemPath).GetPath();

			m_FilePathBlackSystem.MultiAppendBack(fullPath);
			pInstanceContextWindows->AppendBlackWhiteList(fullSystemPath.GetPath(),
				CMFInstanceContext::BLACKWHITE_TYPE::BLACK_DEFAULT);
		}
	}

	// 预计算常用值以避免重复计算
	const CStringW configPath = pathConfig.GetPath();
	const CPath windowsVolumeName = pInstanceContextWindows ? pInstanceContextWindows->GetVolumeName() : CPath();

	// 简单的缓存结构 - 避免重复查找相同卷的实例上下文
	CMFInstanceContext_Ptr lastInstanceContext;
	CPath lastRootCZ;

	// 处理注册表文件列表
	const auto& lHiveFileList = m_FilterSet.GetHiveFileList();
	for (const auto& strHiveFile : lHiveFileList) {
		const CPath pathHiveFile(strHiveFile.Value);
		CPath pRootCZ;
		const auto pathHiveFolder = pathHiveFile.GetRelative(&pRootCZ);
		const CStringW hiveFolderPath = pathHiveFolder.GetPath();

		// 检查是否需要处理此注册表文件
		const bool isNotInConfigPath = configPath.IsEmpty() || !hiveFolderPath.StartWith(configPath);
		const bool isDifferentVolume = !pInstanceContextWindows || pRootCZ != windowsVolumeName;

		if (isNotInConfigPath || isDifferentVolume) {
			// 使用简单缓存避免重复查找相同卷
			CMFInstanceContext_Ptr pInstanceContextHiveFile;
			if (lastInstanceContext && pRootCZ == lastRootCZ) {
				pInstanceContextHiveFile = lastInstanceContext;
			}
			else {
				pInstanceContextHiveFile = FindInstanceContext(pRootCZ);
				lastInstanceContext = pInstanceContextHiveFile;
				lastRootCZ = pRootCZ;
			}

			if (pInstanceContextHiveFile) {
				const CStringW fullPath = (pInstanceContextHiveFile->GetDosNtName() + hiveFolderPath).GetPath();
				m_FilePathBlackSystem.MultiAppendBack(fullPath);
				pInstanceContextHiveFile->AppendBlackWhiteList(hiveFolderPath + ASTERISK_WCHAR,
					CMFInstanceContext::BLACKWHITE_TYPE::BLACK_DEFAULT);
			}
		}
	}
}

/**
 * @brief 设置临时文件夹白名单
 */
VOID CiShareFilter::SetTempFolderWhiteList()
{
	CStringList lTempFolderList;
	m_FilterSet.GetAllTempFolderList(lTempFolderList);
	m_FilePathWhiteSystem.clear();
	for (auto iter = lTempFolderList.begin(); iter != lTempFolderList.end(); ++iter) {
		auto& strValue = *iter;
		if (strValue.IsEmpty()) {
			continue;
		}
		m_FilePathWhiteSystem.MultiAppendBack(strValue);
	}
	SetFilePathBlackWhiteList(lTempFolderList, CMFInstanceContext::BLACKWHITE_TYPE::WHITE_DEFAULT);
}

/**
 * @brief 对黑白名单进行排序
 */
VOID CiShareFilter::SortBlackWhiteList()
{
	for (auto iter = m_listInstanceContext.begin(); iter != m_listInstanceContext.end(); ++iter) {
		(*iter)->SortBlackWhiteList();
	}
}

/**
 * @brief 保存文件上下文
 */
NTSTATUS CiShareFilter::SaveFileContext(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _In_ CMFFileContext_Ptr* pFileContext)
{
	NTSTATUS status = STATUS_SUCCESS;
	do
	{
		if (pFileContext && (*pFileContext)) {
			CMFContextRef OldFileContext;
			status = FltGetFileContext(Data->Iopb->TargetInstance, Data->Iopb->TargetFileObject, &OldFileContext);
			if (NT_SUCCESS(status)) {
				auto pOldFileContext = (CMFFileContext_Ptr*)OldFileContext.GetObject();
				if (pOldFileContext && (*pOldFileContext)
					&& (*pOldFileContext) == (*pFileContext)) {
					break;
				}
			}
			//保存文件上下文, FltAllocateContext, FltSetFileContext 会导致引用计数为2，必须释放一次
			CMFContextRef FileContext;
			status = FltAllocateContext(FltObjects->Filter,
				FLT_FILE_CONTEXT,
				sizeof(CMFFileContext_Ptr),
				NonPagedPool,
				&FileContext);
			if (NT_SUCCESS(status)) {
				//模拟初始化
				RtlZeroMemory(FileContext.GetObject(), sizeof(CMFFileContext_Ptr));
				status = FltSetFileContext(Data->Iopb->TargetInstance,
					Data->Iopb->TargetFileObject,
					FLT_SET_CONTEXT_REPLACE_IF_EXISTS,
					FileContext,
					NULL);
				if (!NT_SUCCESS(status)) {
					PT_DBG_PRINT(PTDBG_TRACE_ERROR,
						"Failed to set file context, file path: %wZ, status=%wZ\n", (PCUNICODE_STRING)(*pFileContext)->GetRelativePath(), (PCUNICODE_STRING)FormatNTSTATUSMessage(status));
				}
				else {
					// 附加成功
					*((CMFFileContext_Ptr*)FileContext.GetObject()) = *pFileContext;
				}
			}
			else {
				PT_DBG_PRINT(PTDBG_TRACE_ERROR,
					"Failed to allocate file context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status));
			}
		}
	} while (false);
	return status;
}

/**
 * @brief MiniFilter 目录控制的预处理回调
 */
FLT_PREOP_CALLBACK_STATUS CiShareFilter::OnMFPreDirectoryControlCallback(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _Flt_CompletionContext_Outptr_ PVOID* CompletionContext)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	PAGED_CODE();

	NTSTATUS status = STATUS_SUCCESS;
	FLT_PREOP_CALLBACK_STATUS callbackStatus = FLT_PREOP_SUCCESS_NO_CALLBACK;
	do
	{
		if (Data->Iopb->MinorFunction != IRP_MN_QUERY_DIRECTORY
			&& Data->Iopb->MinorFunction != IRP_MN_NOTIFY_CHANGE_DIRECTORY) {
			// 只处理查询和通知操作
			break;
		}

		if (Data->Iopb->MinorFunction == IRP_MN_QUERY_DIRECTORY
			&& !NcDetermineStructureOffsets(Data->Iopb->Parameters.DirectoryControl.QueryDirectory.FileInformationClass)) {
			// 不支持的文件信息类别
			break;
		}

		//  如果 FileObject 已经被清理过，现在就失败。
		//  这个检查是为了捕捉文件被清理后，第一次调用通知的情况。
		//  如果不必要，我们不想现在开始初始化我们的结构。
		if (Data->Iopb->MinorFunction == IRP_MN_NOTIFY_CHANGE_DIRECTORY
			&& FlagOn(FltObjects->FileObject->Flags, FO_CLEANUP_COMPLETE)) {
			Data->IoStatus.Status = status = STATUS_NOTIFY_CLEANUP;
			Data->IoStatus.Information = 0;
			callbackStatus = FLT_PREOP_COMPLETE;
			break;
		}

		CMFContextRef InstanceContext;
		// 只能获取实例上下文，其他上下文类型无法获取，因为文件未打开
		auto status1 = FltGetInstanceContext(FltObjects->Instance, &InstanceContext);
		if (NT_SUCCESS(status1) && InstanceContext) {
			auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
			if (*pInstanceContext) {
				if ((*pInstanceContext)->IsUserData() || !GetUserData() || !IsEnableFilter()) {
					//暂时不监控
					break;
				}
				else {
					// 重定向
					if (GetUserData()) {
						CMFFileContext_Ptr* pFileContext = nullptr;
						//保存文件上下文
						CMFContextRef FileContext;
						status1 = FltGetFileContext(Data->Iopb->TargetInstance, Data->Iopb->TargetFileObject, &FileContext);
						if (NT_SUCCESS(status1)) {
							pFileContext = (CMFFileContext_Ptr*)FileContext.GetObject();
						}
						else {
							//NTQueryDirectoryFile 会导致找不到文件上下文, 必须手工查找
							pFileContext = (*pInstanceContext)->GetCacheFileContext(Data, FltObjects, GETFILENAMEINFO_TYPE_DIR);
							SaveFileContext(Data, FltObjects, pFileContext);
						}
						if (pFileContext && (*pFileContext)
							&& (*pInstanceContext)->CheckNeedRedirectPath(*pFileContext, false)
							) {
							switch (Data->Iopb->MinorFunction) {
							case IRP_MN_QUERY_DIRECTORY:
								status = GetUserData()->EnumerateDirectory((*pInstanceContext), *pFileContext, Data, FltObjects, CompletionContext, callbackStatus);
								break;

							case IRP_MN_NOTIFY_CHANGE_DIRECTORY:
								status = GetUserData()->PreNotifyDirectory((*pInstanceContext), *pFileContext, Data, FltObjects, CompletionContext, callbackStatus);
								break;

							default:
								FLT_ASSERT(FALSE);
								callbackStatus = FLT_PREOP_SUCCESS_NO_CALLBACK;
							}
						}
					}
				}
			}
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ERROR,
				"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
			break;
		}
	} while (false);

	return callbackStatus;
}

/**
 * @brief MiniFilter 目录控制的后处理回调
 */
FLT_POSTOP_CALLBACK_STATUS CiShareFilter::OnMFPostDirectoryControlCallback(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _In_ PVOID CompletionContext, _In_ FLT_POST_OPERATION_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	UNREFERENCED_PARAMETER(Flags);
	return FLT_POSTOP_FINISHED_PROCESSING;
}
#if defined(FILTER_QUERY_INFORMATION_ENABLE)
/**
 * @brief MiniFilter 查询信息的预处理回调
 */
FLT_PREOP_CALLBACK_STATUS CiShareFilter::OnMFPreQueryInformationCallback(
	_Inout_ PFLT_CALLBACK_DATA Data,
	_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_Flt_CompletionContext_Outptr_ PVOID* CompletionContext)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	PAGED_CODE();

	FLT_PREOP_CALLBACK_STATUS result;
	switch (Data->Iopb->Parameters.QueryFileInformation.FileInformationClass) {
	case FileAllInformation:
	case FileNameInformation:
	case FileNormalizedNameInformation:
		//回调在同一线程上执行
		result = FLT_PREOP_SYNCHRONIZE;
		break;

	default:
		result = FLT_PREOP_SUCCESS_NO_CALLBACK;
		break;
	}

	return result;
}

/**
 * @brief MiniFilter 查询信息的后处理回调
 */
FLT_POSTOP_CALLBACK_STATUS CiShareFilter::OnMFPostQueryInformationCallback(
	_Inout_ PFLT_CALLBACK_DATA Data,
	_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_In_ PVOID CompletionContext,
	_In_ FLT_POST_OPERATION_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	UNREFERENCED_PARAMETER(Flags);

	FLT_POSTOP_CALLBACK_STATUS Status = FLT_POSTOP_FINISHED_PROCESSING;
	PFILE_NAME_INFORMATION UserDataNameInfo = NULL;
	FILE_INFORMATION_CLASS InfoClass = Data->Iopb->Parameters.QueryFileInformation.FileInformationClass;
	PVOID UserBuffer = Data->Iopb->Parameters.QueryFileInformation.InfoBuffer;
	if (!FlagOn(Flags, FLTFL_POST_OPERATION_DRAINING) && UserBuffer) {
		PFILE_NAME_INFORMATION NameInfo = NULL;
		switch (InfoClass) {
			//QueryFullProcessImageName 使用 FileNameInformation 查询，不包含盘符全路径，导致无法转移或者欺骗为其他盘符
		case FileNameInformation:
			//https://learn.microsoft.com/zh-cn/openspecs/windows_protocols/ms-fscc/20bcadba-808c-4880-b757-4af93e41edf6
			//这个包含盘符和长名称组件的规范化名称的绝对路径名
		case FileNormalizedNameInformation:
			NameInfo = (PFILE_NAME_INFORMATION)UserBuffer;
			break;
		case FileAllInformation:
			NameInfo = &((PFILE_ALL_INFORMATION)UserBuffer)->NameInformation;
			break;
		}
		if (NameInfo) {
			UNICODE_STRING FileName;
			// 初始化 UNICODE_STRING
			FileName.Length = (USHORT)NameInfo->FileNameLength;
			FileName.MaximumLength = (USHORT)(NameInfo->FileNameLength + sizeof(WCHAR)); // 包含终止符
			FileName.Buffer = NameInfo->FileName;
			//检查是否是 \UserData\C\ 形式
			if (CStringW::StartWith(&FileName, MAKELINK_INI_FOLDER_ROOT)
				&& UNICODE_STRING_LENGTH(FileName) > _countof_const_string(MAKELINK_INI_FOLDER_ROOT) + _countof_const_string(DRIVE_ROOT_WITH_SLASH)
				&& FileName.Buffer[_countof_const_string(MAKELINK_INI_FOLDER_ROOT)] == SLASH_WCHAR
				&& IsDiskLetter(FileName.Buffer[_countof_const_string(MAKELINK_INI_FOLDER_ROOT) + 1])
				&& FileName.Buffer[_countof_const_string(MAKELINK_INI_FOLDER_ROOT) + 2] == SLASH_WCHAR) {
				UserDataNameInfo = NameInfo;
				PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
					"OnMFPostQueryInformationCallback: QueryInformation, file name = %wZ, FileInformationClass = %d\n",
					&FileName, InfoClass);
			}
		}
	}

	if (UserDataNameInfo) {
		CMFContextRef InstanceContext;
		// 只能获取实例上下文，其他上下文类型无法获取，因为文件未打开
		auto status1 = FltGetInstanceContext(FltObjects->Instance, &InstanceContext);
		if (NT_SUCCESS(status1) && InstanceContext) {
			auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
			if (*pInstanceContext && (*pInstanceContext)->IsUserData()
				&& GetUserData() && GetUserData() == (*pInstanceContext)) {
				//从  "\\UserData\\C\\"  形式, 去掉UserData 并转换为 "C:\\"
				UserDataNameInfo->FileNameLength -= _countof_const_string(MAKELINK_INI_FOLDER_ROOT) * sizeof(WCHAR);
				RtlMoveMemory(UserDataNameInfo->FileName, UserDataNameInfo->FileName + _countof_const_string(MAKELINK_INI_FOLDER_ROOT), UserDataNameInfo->FileNameLength);
				UserDataNameInfo->FileName[0] = UserDataNameInfo->FileName[1];
				UserDataNameInfo->FileName[1] = COLON_WCHAR;
				UserDataNameInfo->FileName[UserDataNameInfo->FileNameLength / sizeof(WCHAR)] = UNICODE_NULL;
				PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
					"OnMFPostQueryInformationCallback: QueryInformation, Change name to %S\n",
					&UserDataNameInfo->FileName);
			}
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ERROR,
				"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
		}
	}
	return Status;
}
#endif
/**
 * @brief MiniFilter 设置信息的预处理回调
 */
FLT_PREOP_CALLBACK_STATUS CiShareFilter::OnMFPreSetInformationCallback(_Inout_ PFLT_CALLBACK_DATA Data, _In_ PCFLT_RELATED_OBJECTS FltObjects, _Flt_CompletionContext_Outptr_ PVOID* CompletionContext)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	PAGED_CODE();
	NTSTATUS status = STATUS_SUCCESS;
	FLT_PREOP_CALLBACK_STATUS callbackStatus = FLT_PREOP_SUCCESS_NO_CALLBACK;
	do
	{
		if (FileDispositionInformation == Data->Iopb->Parameters.SetFileInformation.FileInformationClass
			|| FileDispositionInformationEx == Data->Iopb->Parameters.SetFileInformation.FileInformationClass) {
			//在回调里处理删除操作
			callbackStatus = FLT_PREOP_SYNCHRONIZE;
			break;
		}

		if (FileRenameInformation != Data->Iopb->Parameters.SetFileInformation.FileInformationClass
			&& FileRenameInformationBypassAccessCheck != Data->Iopb->Parameters.SetFileInformation.FileInformationClass
			&& FileRenameInformationEx != Data->Iopb->Parameters.SetFileInformation.FileInformationClass
			&& FileRenameInformationExBypassAccessCheck != Data->Iopb->Parameters.SetFileInformation.FileInformationClass) {
			// 只处理重命名操作
			break;
		}

		CMFContextRef InstanceContext;
		// 只能获取实例上下文，其他上下文类型无法获取，因为文件未打开
		auto status1 = FltGetInstanceContext(FltObjects->Instance, &InstanceContext);
		if (NT_SUCCESS(status1) && InstanceContext) {
			auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
			if (*pInstanceContext && (*pInstanceContext)->IsUserData()
				&& GetUserData() && GetUserData() == (*pInstanceContext)) {
				//NTSetInformation 会导致找不到文件上下文, 必须手工查找
				auto pFileContext = (*pInstanceContext)->GetCacheFileContext(Data, FltObjects, GETFILENAMEINFO_TYPE_SET_RENAME);
				if (pFileContext && (*pFileContext)
					&& IsPathMatched((*pFileContext)->GetRelativePath().GetPath(), MAKELINK_INI_FOLDER, _countof_const_string(MAKELINK_INI_FOLDER))) {
					//主动完成重命名
					status = GetUserData()->Rename(*pFileContext, Data, FltObjects, CompletionContext, callbackStatus);
					if (!NT_SUCCESS(status)) {
						PT_DBG_PRINT(PTDBG_TRACE_ERROR,
							"OnMFPreSetInformationCallback: SetInformation, file path = %wZ, Target path = %wZ, FileInformationClass = %d, Failed with status 0x%x\n",
							(PCUNICODE_STRING)((*pInstanceContext)->GetDosNtName() + (*pFileContext)->GetRelativePath()),
							(PCUNICODE_STRING)(*pFileContext)->GetTargetPath(),
							Data->Iopb->Parameters.SetFileInformation.FileInformationClass, status);
					}
				}
			}
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ERROR,
				"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
			break;
		}
	} while (false);
	return callbackStatus;
}

/**
 * @brief MiniFilter 设置信息的后处理回调
 */
FLT_POSTOP_CALLBACK_STATUS CiShareFilter::OnMFPostSetInformationCallback(
	_Inout_ PFLT_CALLBACK_DATA Data,
	_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_In_ PVOID CompletionContext,
	_In_ FLT_POST_OPERATION_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(Data);
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(CompletionContext);
	UNREFERENCED_PARAMETER(Flags);
	// assert on FileDispositionInformation/FileDispositionInformationEx
	ASSERT((Data->Iopb->Parameters.SetFileInformation.FileInformationClass == FileDispositionInformation) ||
		(Data->Iopb->Parameters.SetFileInformation.FileInformationClass == FileDispositionInformationEx));

	//参考微软驱动示例 delete
	BOOLEAN DeleteOnClose = FALSE; //关闭时删除
	BOOLEAN SetDisp = FALSE;	//已经删除
	if (NT_SUCCESS(Data->IoStatus.Status)) {
		if (Data->Iopb->Parameters.SetFileInformation.FileInformationClass == FileDispositionInformationEx) {
			auto flags = ((PFILE_DISPOSITION_INFORMATION_EX)Data->Iopb->Parameters.SetFileInformation.InfoBuffer)->Flags;
			if (FlagOn(flags, FILE_DISPOSITION_ON_CLOSE)) {
				DeleteOnClose = BooleanFlagOn(flags, FILE_DISPOSITION_DELETE);
			}
			else {
				SetDisp = BooleanFlagOn(flags, FILE_DISPOSITION_DELETE);
			}
		}
		else {
			SetDisp = ((PFILE_DISPOSITION_INFORMATION)Data->Iopb->Parameters.SetFileInformation.InfoBuffer)->DeleteFile;
		}
	}

	while (DeleteOnClose || SetDisp) {
		CMFContextRef InstanceContext;
		// 只能获取实例上下文，其他上下文类型无法获取，因为文件未打开
		auto status1 = FltGetInstanceContext(FltObjects->Instance, &InstanceContext);
		if (NT_SUCCESS(status1) && InstanceContext) {
			auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
			if (*pInstanceContext) {
				if ((*pInstanceContext)->IsUserData() || !GetUserData() || !IsEnableFilter()) {
					//暂时不监控
					break;
				}
				else {
					// 重定向
					if (GetUserData()) {
						//NTSetInformation 会导致找不到文件上下文, 必须手工查找
						auto pFileContext = (*pInstanceContext)->GetCacheFileContext(Data, FltObjects, GETFILENAMEINFO_TYPE_SET_DELETE);
						if (pFileContext && (*pFileContext)) {
							(*pFileContext)->SetExist(FALSE);
							if ((*pInstanceContext)->CheckNeedRedirectPath(*pFileContext, false)) {
								//完成目录在userdata下的删除
								auto status2 = GetUserData()->Delete(*pFileContext, Data, FltObjects);
								if (!NT_SUCCESS(status2)) {
									PT_DBG_PRINT(PTDBG_TRACE_ERROR,
										"OnMFPostSetInformationCallback: SetInformation, file path = %wZ, DeleteOnClose = %d, SetDisp = %d, Failed with status 0x%x\n", (PCUNICODE_STRING)((*pInstanceContext)->GetDosNtName() + (*pFileContext)->GetRelativePath()), DeleteOnClose, SetDisp, status2);
								}
							}
						}
					}
				}
			}
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ERROR,
				"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
			break;
		}

		break;
	}
	return FLT_POSTOP_FINISHED_PROCESSING;
}

/// <summary>
/// 在卷被挂载时，设置和初始化微过滤器实例的上下文，并根据卷类型和文件系统类型决定是否附加过滤器。仅对本地 NTFS、FAT、exFAT 文件系统的卷进行监控，并初始化相关的上下文和配置。附加成功返回 STATUS_SUCCESS，否则返回错误码或 STATUS_FLT_DO_NOT_ATTACH。支持用户数据的加载和注册表监控的初始化。
/// </summary>
/// <param name="FltObjects">指向与微过滤器相关的对象结构体的指针，包含过滤器、实例、卷等信息。</param>
/// <param name="Flags">实例设置标志，指示实例初始化时的选项。</param>
/// <param name="VolumeDeviceType">卷的设备类型（如本地磁盘、网络文件系统、光驱等）。</param>
/// <param name="VolumeFilesystemType">卷的文件系统类型（如 NTFS、FAT、exFAT 等）。</param>
/// <returns>返回 NTSTATUS 状态码。若成功附加过滤器返回 STATUS_SUCCESS，否则返回错误码或 STATUS_FLT_DO_NOT_ATTACH。</returns>
NTSTATUS CiShareFilter::OnMiniFilterInstanceSetup(_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_In_ FLT_INSTANCE_SETUP_FLAGS Flags,
	_In_ DEVICE_TYPE VolumeDeviceType,
	_In_ FLT_FILESYSTEM_TYPE VolumeFilesystemType)
{
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(Flags);
	UNREFERENCED_PARAMETER(VolumeDeviceType);
	UNREFERENCED_PARAMETER(VolumeFilesystemType);
	PAGED_CODE();

	// 默认不附加
	// 检查是否需要附加， 仅对本地 NTFS、FAT、exFAT 文件系统的卷进行监控
	if (VolumeDeviceType == FILE_DEVICE_NETWORK_FILE_SYSTEM || VolumeDeviceType == FILE_DEVICE_CD_ROM ||
		(VolumeFilesystemType != FLT_FSTYPE_NTFS && VolumeFilesystemType != FLT_FSTYPE_FAT && VolumeFilesystemType != FLT_FSTYPE_EXFAT)) {
		return STATUS_FLT_DO_NOT_ATTACH;
	}

	auto pInstanceContext = make_shared<CMFInstanceContext>();
	if (!pInstanceContext || !NT_SUCCESS(pInstanceContext->Init(FltObjects, VolumeFilesystemType)) || !pInstanceContext->HaveDiskLetter()) {
		// 初始化实例上下文失败或获取盘符失败，暂时不监控
		PT_DBG_PRINT(PTDBG_TRACE_ERROR, "Failed to initialize instance context or get disk letter, do not monitor\n");
		return STATUS_FLT_DO_NOT_ATTACH;
	}

	// 分配和设置实例上下文，FltAllocateContext 和 FltSetInstanceContext 会导致引用计数为2，必须释放一次
	CMFContextRef InstanceContext;
	NTSTATUS status = FltAllocateContext(FltObjects->Filter, FLT_INSTANCE_CONTEXT, sizeof(CMFInstanceContext_Ptr), NonPagedPool, &InstanceContext);
	if (!NT_SUCCESS(status)) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR, "Failed to allocate instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status));
		return status;
	}

	RtlZeroMemory(InstanceContext.GetObject(), sizeof(CMFInstanceContext_Ptr));
	status = FltSetInstanceContext(FltObjects->Instance, FLT_SET_CONTEXT_REPLACE_IF_EXISTS, InstanceContext, NULL);
	if (!NT_SUCCESS(status)) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR, "Failed to set instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status));
		return status;
	}
	// 附加上下文
	*((CMFInstanceContext_Ptr*)InstanceContext.GetObject()) = pInstanceContext;

	// 用户盘，开始读取参数，并开始转移文件和注册表
	if (pInstanceContext->IsUserData() && !m_UserData && LoadConfigAndStart(pInstanceContext)) {
		m_UserData = pInstanceContext;
	}
	else {
		pInstanceContext->SetUserData(FALSE);
		m_listInstanceContext.push_back(pInstanceContext);
	}

	return STATUS_SUCCESS;
}

/// <summary>
/// 重新加载配置文件或设置。
/// </summary>
/// <returns>返回一个 NTSTATUS 值，指示重新加载配置的结果。</returns>
NTSTATUS CiShareFilter::ReloadConfig()
{
	if (m_UserData) {
		// 如果用户数据实例存在，尝试重新加载配置
		return LoadConfigAndStart(m_UserData) ? STATUS_SUCCESS : STATUS_UNSUCCESSFUL;
	}

	// 遍历实例上下文列表，查找用户数据实例并加载配置
	for (auto iter = m_listInstanceContext.begin(); iter != m_listInstanceContext.end(); ++iter) {
		if ((*iter)->HaveUserData() && LoadConfigAndStart(*iter)) {
			// 如果找到用户数据实例并成功加载配置，更新 m_UserData 并从列表中移除该实例
			(*iter)->SetUserData(TRUE);
			m_UserData = *iter;
			m_listInstanceContext.erase(iter);
			PT_DBG_PRINT(PTDBG_TRACE_ROUTINES, "Reload config success\n");
			return STATUS_SUCCESS;
		}
	}

	// 如果未找到用户数据实例或加载失败，返回失败状态
	return STATUS_UNSUCCESSFUL;
}

/// <summary>
/// 加载配置并启动实例上下文。
/// </summary>
/// <param name="pInstanceContext">指向实例上下文的智能指针。</param>
/// <returns>如果加载配置并启动成功，则返回 true；否则返回 false。</returns>
bool CiShareFilter::LoadConfigAndStart(CMFInstanceContext_Ptr pInstanceContext)
{
	// Early validation
	if (!pInstanceContext || !pInstanceContext->HaveUserData()) {
		return false;
	}

	// Load configuration
	CSimpleBuffer bufferUserData;
	CFilterSet tmpFilterSet;
	if (!NT_SUCCESS(pInstanceContext->ReadUserDataSet(bufferUserData)) ||
		!tmpFilterSet.LoadFilterSet(bufferUserData)) {
		return false;
	}

	// Check for configuration changes
	BOOLEAN bParameterFileSetChanged = FALSE;
	const BOOLEAN biSharePnpConfigChanged = ReadiShareFilterParameter(bParameterFileSetChanged);

	if (!biSharePnpConfigChanged && (m_FilterSet == tmpFilterSet)) {
		// No configuration changes detected
		return true;
	}

	const BOOLEAN bFilterSetChanged = m_FilterSet.IsChangedFileSet(tmpFilterSet);
	m_FilterSet.swap(tmpFilterSet);
	// Update filter set if changed
	if (bFilterSetChanged || bParameterFileSetChanged) {
		UpdateFilterConfiguration();
	}
	else if (!IsEnableFilter()) {
		// Clear blacklist if filter is disabled but config is not changed
		ClearBlackWhiteList();
	}

	const BOOLEAN shouldEnableRegistry = IsEnableFilter() && m_FilterSet.IsEnableFilterRegister();

	if (shouldEnableRegistry) {
		StartRegistryMonitoring(pInstanceContext);
	}
	else {
		StopRegisterFilter();
	}

	SaveiShareFilterParameter();
	return true;
}

void CiShareFilter::UpdateFilterConfiguration()
{
	// Clear existing configuration
	EnvPathShortNameToLongPath();
	ClearBlackWhiteList();

	if (!IsEnableFilter()) {
		return;
	}

	// Apply new filter settings
	SetFilePathBlackWhiteList(m_FilePathBlackDefault, CMFInstanceContext::BLACKWHITE_TYPE::BLACK_DEFAULT);
	m_FilePathBlackMultiString = IniString2MultiString(m_FilterSet.GetFilePathBlack());
	SetFilePathBlackWhiteList(m_FilePathBlackMultiString, CMFInstanceContext::BLACKWHITE_TYPE::BLACK);
	SetFilePathBlackWhiteList(m_FilePathWhiteDefault, CMFInstanceContext::BLACKWHITE_TYPE::WHITE_DEFAULT);
	m_FilePathWhiteMultiString = IniString2MultiString(m_FilterSet.GetFilePathWhite());
	SetFilePathBlackWhiteList(m_FilePathWhiteMultiString, CMFInstanceContext::BLACKWHITE_TYPE::WHITE);

	SetRegisterHiveFileBlackList();

	// Set temporary folder whitelist and sort blacklist/whitelist
	SetTempFolderWhiteList();
	SortBlackWhiteList();
}

/**
 * @brief 启动注册表监控
 * @param pInstanceContext 实例上下文指针
 * @return bool 启动成功返回true，否则返回false
 */
bool CiShareFilter::StartRegistryMonitoring(CMFInstanceContext_Ptr pInstanceContext)
{
	// 参数验证
	if (!pInstanceContext) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR, "StartRegistryMonitoring: Invalid instance context\n");
		return false;
	}

	// 更新现有过滤器的黑白名单
	if (m_RegFilterUserData.IsActive()) {
		UpdateRegistryFilterConfig();
		PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
			"StartRegistryMonitoring: Updated existing registry filter blacklist and whitelist\n");
		return true;
	}

	// 创建新的注册表过滤器
	const CPath hiveFilePath = pInstanceContext->GetVolumeName() + MAKELINK_HIVE_PATH;

	// 创建并加载注册表配置单元
	NTSTATUS status = CreateAndLoadRegistryHive(hiveFilePath.c_str(), USERDATA_REG_ROOT);
	if (!NT_SUCCESS(status)) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR,
			"StartRegistryMonitoring: Failed to load registry hive %wZ, status=%wZ\n",
			(PCUNICODE_STRING)hiveFilePath,
			(PCUNICODE_STRING)FormatNTSTATUSMessage(status));
		return false;
	}

	// 配置过滤器黑白名单
	UpdateRegistryFilterConfig();

	// 启动注册表过滤器
	status = m_RegFilterUserData.StartFilter();
	if (!NT_SUCCESS(status)) {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR,
			"StartRegistryMonitoring: Failed to start registry filter, status=%wZ\n",
			(PCUNICODE_STRING)FormatNTSTATUSMessage(status));

		// 启动失败时清理已加载的配置单元
		UnloadRegistryHive(USERDATA_REG_ROOT);
		return false;
	}

	PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
		"StartRegistryMonitoring: Successfully started registry monitoring for hive %wZ\n",
		(PCUNICODE_STRING)hiveFilePath);

	return true;
}

/**
 * @brief 更新现有注册表过滤器配置
 * @return bool 更新成功返回true，否则返回false
 */
bool CiShareFilter::UpdateRegistryFilterConfig()
{
	m_RegPathBlackMultiString = IniString2MultiString(m_FilterSet.GetRegPathBlack());
	m_RegPathWhiteMultiString = IniString2MultiString(m_FilterSet.GetRegPathWhite());
	m_RegFilterUserData.SetRegPathBlack(m_FilterSet.GetParsedRegPath(m_RegPathBlackMultiString));
	m_RegFilterUserData.SetRegPathWhite(m_FilterSet.GetParsedRegPath(m_RegPathWhiteMultiString));
	m_RegFilterUserData.SetRegPathBlackDefault(m_FilterSet.GetParsedRegPath(m_RegisterPathBlackDefault));
	m_RegFilterUserData.SetRegPathWhiteDefault(m_FilterSet.GetParsedRegPath(m_RegisterPathWhiteDefault));
	return true;
}

bool CiShareFilter::StopRegisterFilter()
{
	if (m_RegFilterUserData.IsActive()) {
		auto status1 = m_RegFilterUserData.StopFilter();
		PT_DBG_PRINT(PTDBG_TRACE_ROUTINES, "isharefilter stop registry filter status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status1));
		UnloadRegistryHive(USERDATA_REG_ROOT);
		return status1 == STATUS_SUCCESS;
	}
	return false;
}

/**
 * @brief MiniFilter 实例查询拆卸的处理
 */
NTSTATUS CiShareFilter::OnMiniFilterInstanceQueryTeardown(_In_ PCFLT_RELATED_OBJECTS FltObjects, _In_ FLT_INSTANCE_QUERY_TEARDOWN_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(Flags);
	PAGED_CODE();

	return STATUS_SUCCESS;
}

/**
 * @brief MiniFilter 实例拆卸开始的处理
 */
VOID CiShareFilter::OnMiniFilterInstanceTeardownStart(_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_In_ FLT_INSTANCE_TEARDOWN_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(Flags);
	PAGED_CODE();
}

/**
 * @brief MiniFilter 实例拆卸完成的处理
 */
VOID CiShareFilter::OnMiniFilterInstanceTeardownComplete(_In_ PCFLT_RELATED_OBJECTS FltObjects,
	_In_ FLT_INSTANCE_TEARDOWN_FLAGS Flags)
{
	UNREFERENCED_PARAMETER(FltObjects);
	UNREFERENCED_PARAMETER(Flags);
	PAGED_CODE();

	CMFContextRef InstanceContext;
	NTSTATUS status = FltGetInstanceContext(FltObjects->Instance,
		&InstanceContext);
	if (NT_SUCCESS(status) && InstanceContext) {
		auto pInstanceContext = (CMFInstanceContext_Ptr*)InstanceContext.GetObject();
		if (*pInstanceContext) {
			if ((*pInstanceContext) == m_UserData) {
				m_UserData.reset();
			}
			else {
				m_listInstanceContext.remove((*pInstanceContext));
			}
			(*pInstanceContext)->Close();
			(*pInstanceContext).reset();
		}
	}
	else {
		PT_DBG_PRINT(PTDBG_TRACE_ERROR,
			"Failed to get instance context, status=%wZ\n", (PCUNICODE_STRING)FormatNTSTATUSMessage(status));
	}
}

BOOLEAN CiShareFilter::ReadiShareFilterParameter(BOOLEAN& bChangeFileSet)
{
	bChangeFileSet = FALSE;

	CRegKey ParaRegKey;
	const CStringW sRegistryPath = GetRegistryPath() + ISHAREPNP_PARAMETERS;

	// 尝试打开或创建注册表键
	if (!NT_SUCCESS(ParaRegKey.Open(NULL, sRegistryPath)) &&
		!NT_SUCCESS(ParaRegKey.Create(NULL, sRegistryPath))) {
		return FALSE;
	}

	// 读取参数
	BOOLEAN bSuperUser = FALSE;
	BOOLEAN bLocalKeepMode = FALSE;
	CStringW sFilePathBlackDefault = L"";
	CStringW sFilePathWhiteDefault = L"";
	CStringW sRegisterPathBlackDefault = L"";
	CStringW sRegisterPathWhiteDefault = L"";

	ParaRegKey.QueryBOOLEANValue(ISHAREPNP_SUPERUSER, bSuperUser);
	ParaRegKey.QueryBOOLEANValue(ISHAREPNP_LOCALKEEPMODE, bLocalKeepMode);
	ParaRegKey.QueryMultiStringValue(MAKELINK_INI_FILTER_FILE_BLACK_DEFAULT, sFilePathBlackDefault);
	ParaRegKey.QueryMultiStringValue(MAKELINK_INI_FILTER_FILE_WHITE_DEFAULT, sFilePathWhiteDefault);
	ParaRegKey.QueryMultiStringValue(MAKELINK_INI_FILTER_REGISTER_BLACK_DEFAULT, sRegisterPathBlackDefault);
	ParaRegKey.QueryMultiStringValue(MAKELINK_INI_FILTER_REGISTER_WHITE_DEFAULT, sRegisterPathWhiteDefault);

	bChangeFileSet = (m_bSuperUser != bSuperUser ||
		m_bLocalKeepMode != bLocalKeepMode ||
		m_FilePathBlackDefault != sFilePathBlackDefault ||
		m_FilePathWhiteDefault != sFilePathWhiteDefault);

	// 检查是否有参数发生变化
	const BOOLEAN bChanged = (bChangeFileSet ||
		m_RegisterPathBlackDefault != sRegisterPathBlackDefault ||
		m_RegisterPathWhiteDefault != sRegisterPathWhiteDefault);

	if (bChanged) {
		// 更新成员变量
		m_bSuperUser = bSuperUser;
		m_bLocalKeepMode = bLocalKeepMode;
		m_FilePathBlackDefault = sFilePathBlackDefault;
		m_FilePathWhiteDefault = sFilePathWhiteDefault;
		m_RegisterPathBlackDefault = sRegisterPathBlackDefault;
		m_RegisterPathWhiteDefault = sRegisterPathWhiteDefault;
	}

	return bChanged;
}

BOOLEAN CiShareFilter::SaveiShareFilterParameter()
{
	CRegKey ParaRegKey;
	const CStringW sRegistryPath = GetRegistryPath() + ISHAREPNP_PARAMETERS;

	// 尝试打开或创建注册表键
	if (!NT_SUCCESS(ParaRegKey.Open(NULL, sRegistryPath)) &&
		!NT_SUCCESS(ParaRegKey.Create(NULL, sRegistryPath))) {
		return FALSE;
	}

	// 保存参数
	ParaRegKey.SetDWORDValue(MAKELINK_INI_FILTER_APPLICATIONLAYER, m_FilterSet.IsEnableFilterFile() ? 1 : 0);
	ParaRegKey.SetDWORDValue(MAKELINK_INI_FILTER_APPLICATIONLAYER_SUPER, m_FilterSet.IsEnableFilterSuperUser() ? 1 : 0);
	ParaRegKey.SetDWORDValue(MAKELINK_INI_FILTER_REGISTERLAYER_DISABLE, m_FilterSet.IsEnableFilterRegister() ? 0 : 1);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_ENV_SECTION, m_EnvPathMultiString);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_FILE_BLACK_SYSTEM, m_FilePathBlackSystem);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_FILE_WHITE_SYSTEM, m_FilePathWhiteSystem);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_FILE_BLACK, m_FilePathBlackMultiString);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_FILE_WHITE, m_FilePathWhiteMultiString);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_REGISTER_BLACK, m_RegPathBlackMultiString);
	ParaRegKey.SetMultiStringValue(MAKELINK_INI_FILTER_REGISTER_WHITE, m_RegPathWhiteMultiString);
	return TRUE;
}

// 解析INI字符串并转换为多字符串格式
// 这里需要根据实际的INI字符串格式进行解析
// 例如，可以使用分隔符将字符串分割成多个部分
// 然后将每个部分添加到strMultiString中
CStringW CiShareFilter::IniString2MultiString(const CStringW& strIniString)
{
	CStringW strMultiString;
	CStringList FilePathList;
	SplitStrings2List(strIniString, FilePathList, WSPLIT_STRING, FALSE);
	for (const auto& strPath : FilePathList) {
		strMultiString.MultiAppendBack(strPath);
	}
	return strMultiString;
}