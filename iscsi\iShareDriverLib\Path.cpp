﻿#include "stdafx.h"
#include "Path.h"

// 定义路径相关常量
#define VOLUME_STRING               L"Volume"
#define VOLUME_PREFIX               L"\\\\?\\" VOLUME_STRING
#define VOLUME_GUID_FORMAT          L"{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}"
#define VOLUME_GUID_ROOT            VOLUME_PREFIX VOLUME_GUID_FORMAT
#define VOLUME_GUID_ROOT_WITH_SLASH VOLUME_GUID_ROOT SLASH_STRING
#define UNC_DRIVE_ROOT_WITHOUT_SLASH UNC_PREFIX DRIVE_ROOT_WITHOUT_SLASH
#define UNC_DRIVE_ROOT_WITH_SLASH   UNC_PREFIX DRIVE_ROOT_WITH_SLASH
#define HARD_DISK_VOLUME_PREFIX     L"\\Device\\HarddiskVolume"
#define HARD_DISK_VOLUME_ROOT       HARD_DISK_VOLUME_PREFIX L"1"
#define HARD_DISK_VOLUME_ROOT_WITH_SLASH HARD_DISK_VOLUME_ROOT SLASH_STRING

/**
 * @brief 判断字符是否为磁盘盘符（A-Z或a-z）。
 *        用于路径解析时识别盘符。
 * @param c 待判断字符
 * @return true 是盘符，false 不是盘符
 */
bool IsDiskLetter(WCHAR c) {
	return (c >= L'A' && c <= L'Z') || (c >= L'a' && c <= L'z'); // 检查字符是否在 A-Z 或 a-z 范围内
}

/**
 * @brief 判断字符是否为数字字符。
 *        常用于路径分段时识别卷标等。
 * @param c 待判断字符
 * @return true 是数字，false 不是数字
 */
bool IsNumber(WCHAR c) {
	return c >= L'0' && c <= L'9'; // 检查字符是否在 0-9 范围内
}

/**
 * @brief 判断字符串是否为 GUID 格式。
 *        用于识别卷 GUID 路径。
 * @param str 待判断字符串
 * @return true 是 GUID，false 不是
 */
bool IsGuidString(const CStringW& str) {
	if (str.GetLength() != _countof_const_string(VOLUME_GUID_FORMAT)) {
		return false; // 长度不匹配，直接返回 false
	}
	// 检查 GUID 格式：{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}
	for (int i = 0; i < str.GetLength(); i++) {
		if (i == 0) {
			if (str[i] != L'{') {
				return false; // 第一个字符必须是 '{'
			}
		}
		else if (i == (_countof_const_string(VOLUME_GUID_FORMAT) - 1)) {
			if (str[i] != L'}') {
				return false; // 最后一个字符必须是 '}'
			}
		}
		else if (i == 9 || i == 14 || i == 19 || i == 24) {
			if (str[i] != L'-') {
				return false; // 指定位置必须是 '-'
			}
		}
		else {
			// 检查是否为十六进制字符
			if (!IsNumber(str[i]) &&
				(str[i] < L'a' || str[i] > L'f') &&
				(str[i] < L'A' || str[i] > L'F')) {
				return false; // 非十六进制字符返回 false
			}
		}
	}
	return true; // 符合 GUID 格式
}

/**
 * @brief 比较两个路径是否为同一目录。
 *        支持末尾反斜杠的容错，区分大小写。
 * @param path 另一个路径对象
 * @return true 目录相同，false 不同
 */
bool CPath::CompareDirectory(const CPath& path) const {
	CPath leftPath(m_strPath);
	CPath rightPath(path.m_strPath);
	if (leftPath.HaveBackslash())
		leftPath.RemoveBackslash();
	if (rightPath.HaveBackslash())
		rightPath.RemoveBackslash();

	auto leftLength = leftPath.GetLength();
	auto rightLength = rightPath.GetLength();
	// 处理空路径情况
	if (leftLength == 0 && rightLength == 0) {
		return true; // 两个空路径视为同一目录
	}
	else if (leftLength != rightLength) {
		return false;
	}
	else {
		// 路径完全一致才算同一目录
		return leftPath.m_strPath == rightPath.m_strPath;
	}
}

/**
 * @brief 判断当前路径是否为根路径。
 *        根路径如 C:\、\\Device\\HarddiskVolume1\ 等。
 * @return true 是根路径，false 不是
 */
bool CPath::IsRoot() const {
	USHORT nRootLen(0);
	auto ret = HaveRoot(&nRootLen);
	// 只有路径完全等于根路径时才返回 true
	return ret && (nRootLen == m_strPath.GetLength());
}

/**
 * @brief 判断路径是否有根（支持多种根路径格式）。
 *        根路径包括硬盘卷、盘符、NT名、GUID等。
 * @param pRootLen 返回根路径长度（可选）
 * @return true 有根路径，false 无
 */
bool CPath::HaveRoot(PUSHORT pRootLen /*= NULL*/) const {
	// 依次检测各种根路径格式
	if (HaveRootHarddiskVolume(pRootLen)) {
		return true;
	}

	if (HaveRootCZ(pRootLen)) {
		return true;
	}

	if (HaveRootDosNtName(pRootLen)) {
		return true;
	}

	if (HaveRootGUIDName(pRootLen)) {
		return true;
	}

	// 未识别的根路径格式
	return false;
}

/**
 * @brief 判断是否是硬盘卷根路径（如\Device\HarddiskVolume1\）
 * @param pRootLen [out] 可选，返回根目录部分长度
 * @return bool 是否是硬盘卷根路径
 */
bool CPath::HaveRootHarddiskVolume(PUSHORT pRootLen) const
{
	// 获取路径
	const CStringW& path = m_strPath;
	const int pathLength = path.GetLength();

	// 检查设备路径格式 (例如 "\\Device\\HarddiskVolume1" 或 "\\Device\\HarddiskVolume1\\")
	if (pathLength >= _countof_const_string(HARD_DISK_VOLUME_ROOT) &&
		path.Left(_countof_const_string(HARD_DISK_VOLUME_PREFIX)) == HARD_DISK_VOLUME_PREFIX &&
		IsNumber(path.Mid(_countof_const_string(HARD_DISK_VOLUME_PREFIX), 1)[0])) {
		// 查找 "HarddiskVolume" 后面的数字
		int volumeNumberStart = _countof_const_string(HARD_DISK_VOLUME_PREFIX);
		int volumeNumberEnd = volumeNumberStart;
		while (volumeNumberEnd < pathLength && IsNumber(path[volumeNumberEnd])) {
			volumeNumberEnd++;
		}

		// 检查后面的字符是否是 `SLASH_STRING` 或路径结束
		if (volumeNumberEnd < pathLength && path[volumeNumberEnd] == SLASH_WCHAR) {
			if (pRootLen) *pRootLen = (USHORT)(volumeNumberEnd + 1);
			return true;
		}

		if (volumeNumberEnd == pathLength) {
			if (pRootLen) *pRootLen = (USHORT)volumeNumberEnd;
			return true;
		}
	}
	return false; //非标准\\Device\\HarddiskVolume1
}

/**
 * @brief 获取硬盘卷根路径
 * @return CStringW 硬盘卷根路径
 */
CStringW CPath::GetRootHarddiskVolume() const
{
	CStringW strVolume;
	USHORT pRootLen = 0;
	if (HaveRootHarddiskVolume(&pRootLen)) {
		strVolume = m_strPath.Left(pRootLen);
	}
	return strVolume;
}

/**
 * @brief 判断是否是盘符根路径（如C:\）
 * @param pRootLen [out] 可选，返回根目录部分长度
 * @return bool 是否是盘符根路径
 */
bool CPath::HaveRootCZ(PUSHORT pRootLen) const
{
	// 获取路径
	const CStringW& path = m_strPath;
	const int pathLength = path.GetLength();

	// 检查标准驱动器根路径格式 (例如 "C:\" 或 "C:")
	if (pathLength >= _countof_const_string(DRIVE_ROOT_WITH_SLASH) &&
		IsDiskLetter(path[0]) &&
		path[1] == COLON_WCHAR &&
		path[2] == L'\\') {
		if (pRootLen) *pRootLen = _countof_const_string(DRIVE_ROOT_WITH_SLASH);
		return true;
	}
	if (pathLength == _countof_const_string(DRIVE_ROOT_WITHOUT_SLASH) &&
		IsDiskLetter(path[0]) &&
		path[1] == COLON_WCHAR) {
		if (pRootLen) *pRootLen = _countof_const_string(DRIVE_ROOT_WITHOUT_SLASH);
		return true;
	}

	return false;
}

/**
 * @brief 获取盘符（如C:）
 * @return WCHAR 盘符，0表示无效
 */
WCHAR CPath::GetRootCZ() const
{
	WCHAR c = L'\0';
	if (HaveRootCZ()) {
		c = m_strPath.GetAt(0);
	}
	return c;
}

/**
 * @brief 判断是否是DOS/NT格式路径（如\??\C:\）
 * @param pRootLen [out] 可选，返回根目录部分长度
 * @return bool 是否是DOS/NT格式路径
 */
bool CPath::HaveRootDosNtName(PUSHORT pRootLen) const
{
	// 获取路径
	const CStringW& path = m_strPath;
	const int pathLength = path.GetLength();

	// 检查路径是否以 "\\??\\" 开头，并处理标准驱动器根路径格式
	if (pathLength >= _countof_const_string(UNC_DRIVE_ROOT_WITHOUT_SLASH) &&
		path.Left(_countof_const_string(UNC_PREFIX)) == UNC_PREFIX &&
		IsDiskLetter(path.Mid(_countof_const_string(UNC_PREFIX), 1)[0]) &&
		path.Mid(_countof_const_string(UNC_PREFIX) + 1, 1) == L":") {
		if (pathLength >= _countof_const_string(UNC_DRIVE_ROOT_WITH_SLASH) &&
			path.Mid(_countof_const_string(UNC_DRIVE_ROOT_WITHOUT_SLASH), 1) == SLASH_STRING) {
			if (pRootLen) *pRootLen = _countof_const_string(UNC_DRIVE_ROOT_WITH_SLASH);
			return true;
		}
		if (pathLength == _countof_const_string(UNC_DRIVE_ROOT_WITHOUT_SLASH)) {
			if (pRootLen) *pRootLen = _countof_const_string(UNC_DRIVE_ROOT_WITHOUT_SLASH);
			return true;
		}
	}

	return false;
}

/**
 * @brief 判断是否是以 "\\?\" 开头的GUID格式路径（如\\?\{GUID}\）
 * @param pRootLen [out] 可选，返回根目录部分长度
 * @return bool 是否是GUID格式路径
 */
bool CPath::HaveRootGUIDName(PUSHORT pRootLen) const
{
	// 获取路径
	const CStringW& path = m_strPath;
	const int pathLength = path.GetLength();

	// 检查路径是否以 "\\?\" 开头，并处理 Volume GUID 路径格式
	if (pathLength >= _countof_const_string(VOLUME_GUID_ROOT) &&
		path.Left(_countof_const_string(VOLUME_PREFIX)) == VOLUME_PREFIX &&
		IsGuidString(path.Mid(_countof_const_string(VOLUME_PREFIX), _countof_const_string(VOLUME_GUID_FORMAT)))) {
		if (pathLength >= _countof_const_string(VOLUME_GUID_ROOT_WITH_SLASH) &&
			path.Mid(_countof_const_string(VOLUME_GUID_ROOT), 1) == SLASH_STRING) {
			if (pRootLen) *pRootLen = _countof_const_string(VOLUME_GUID_ROOT_WITH_SLASH);
			return true;
		}
		if (pathLength == _countof_const_string(VOLUME_GUID_ROOT)) {
			if (pRootLen) *pRootLen = _countof_const_string(VOLUME_GUID_ROOT);
			return true;
		}
	}
	// 未识别的根路径格式
	return false;
}

/**
 * @brief 检查是否包含 ~1 ~2 ... 等短文件名和短目录
 * @return bool 是否包含短文件名
 */
bool CPath::HaveShortName() const
{
	auto nPos = m_strPath.Find(L'~');
	while (nPos != CSTRINGW_NOPOS) {
		auto nLength = m_strPath.GetLength();
		if (nPos + 1 < nLength) {
			auto i = nPos + 1;
			// 检查 ~ 后面的字符是否为数字
			if (IsNumber(m_strPath.GetAt(i))) {
				while (i < nLength && IsNumber(m_strPath.GetAt(i))) {
					i++;
				}
				// 检查数字序列后的字符是否为路径结束、'\' 或 '.'
				if (i == nLength || m_strPath.GetAt(i) == L'\\' || m_strPath.GetAt(i) == L'.') {
					// 检查 ~ 前面的字符长度是否符合短文件名的限制
					auto start = m_strPath.ReverseFind(L'\\', nPos);
					if (start == CSTRINGW_NOPOS) {
						start = 0;
					}
					else {
						start += 1; // 跳过 '\'
					}
					// 计算短文件名的总长度，包括 ~ 和后面的数字部分
					if (i - start <= 8) {
						// 如果有扩展名，检查其长度是否为 3
						if (m_strPath.GetAt(i) == L'.') {
							auto extStart = i + 1;
							auto extEnd = extStart;
							while (extEnd < nLength && m_strPath.GetAt(extEnd) != L'\\') {
								extEnd++;
							}
							if (extEnd - extStart == 3) {
								return true;
							}
						}
						else {
							return true;
						}
					}
				}
			}
		}
		// 查找下一个 '~'
		nPos = m_strPath.Find(L'~', nPos + 1);
	}
	return false;
}

/**
 * @brief 获取父目录路径
 * @return CPath 父目录路径
 */
CPath CPath::GetParent() const
{
	CPath path = *this;
	path.RemoveBackslash();
	auto nPos = path.m_strPath.ReverseFind(SLASH_WCHAR);
	if (nPos != CSTRINGW_NOPOS && nPos > 0) {
		return path.m_strPath.Left(nPos);
	}
	return CPath();
}

CPath CPath::GetRootParent() const {
	CPath parent = GetParent();
	if (parent.IsEmpty()) {
		// 当前路径是否是根目录？
		if (IsRoot() || IsRelativeRoot()) {
			return *this;
		}
		else {
			return CPath(); // 不是根，无父目录
		}
	}
	else {
		return parent.GetRootParent(); // 递归继续
	}
}

/**
 * @brief 获取完整路径的父目录
 * @return CPath 父目录路径
 */
CPath CPath::GetFullPathParent() const
{
	CPath path = *this;
	path.RemoveBackslash();
	CPath pRoot;
	path = GetRelative(&pRoot);
	auto nPos = path.m_strPath.ReverseFind(SLASH_WCHAR);
	if (nPos != CSTRINGW_NOPOS && nPos > 0) {
		path.m_strPath = path.m_strPath.Left(nPos);
		return pRoot + path;
	}
	return CPath();
}

/**
 * @brief 获取文件名部分（不含路径）
 * @return CPath 文件名
 */
CPath CPath::GetFileName() const
{
	CPath path = *this;
	path.RemoveBackslash();
	auto nPos = path.m_strPath.ReverseFind(SLASH_WCHAR);
	if (nPos != CSTRINGW_NOPOS && nPos != path.m_strPath.GetLength() - 1) {
		path.m_strPath = path.m_strPath.Mid(nPos + 1);
	}
	return path;
}

/**
 * @brief 获取完整路径的文件名部分, 避免根目录误判为文件名
 * @return CPath 文件名
 */
CPath CPath::GetFullPathFileName() const
{
	CPath pRoot;
	CPath path = GetRelative(&pRoot);
	auto nPos = path.m_strPath.ReverseFind(SLASH_WCHAR);
	if (nPos != CSTRINGW_NOPOS && nPos != path.m_strPath.GetLength() - 1) {
		path.m_strPath = path.m_strPath.Mid(nPos + 1);
	}
	return path;
}

/**
 * @brief 获取文件名（不含扩展名）
 * @return CPath 文件名（不含扩展名）
 */
CPath CPath::GetFileNameWithoutExtension() const
{
	CPath path = GetFullPathFileName();
	auto nPos = path.m_strPath.ReverseFind(L'.');
	if (nPos != CSTRINGW_NOPOS) {
		path.m_strPath = path.m_strPath.Left(nPos);
	}
	return path;
}

/**
 * @brief 获取文件扩展名
 * @return CPath 文件扩展名
 */
CPath CPath::GetExtension() const
{
	CPath path = GetFullPathFileName();
	auto nPos = path.m_strPath.ReverseFind(L'.');
	if (nPos != CSTRINGW_NOPOS) {
		path.m_strPath = path.m_strPath.Mid(nPos);
	}
	else {
		path.m_strPath.Empty();
	}
	return path;
}

/**
 * @brief 追加路径字符串
 * @param strPath 要追加的路径字符串
 * @return CPath& 当前对象引用
 */
CPath& CPath::Append(LPCWSTR strPath)
{
	// 使用Append实现，避免丢失路径分隔符
	if (strPath && *strPath) {
		if (IsEmpty()) {
			m_strPath = strPath;
		}
		else {
			// 去掉了前面的反斜杠
			USHORT i = 0;
			while (strPath[i] == SLASH_WCHAR) {
				++i;
			}
			if (HaveBackslash()) {
				//自身有反斜杠，增加时，去掉后面的反斜杠
				m_strPath += (strPath + i);
			}
			else {
				if (i == 1) {
					// strPath 有一个反斜杠
					m_strPath += strPath;
				}
				else {
					AddBackslash();
					m_strPath += (strPath + i);
				}
			}
		}
	}
	return *this;
}

/**
 * @brief 移除末尾的反斜杠
 * @return CPath& 当前对象引用
 */
CPath& CPath::RemoveBackslash()
{
	auto length = m_strPath.GetLength();
	while (length > 1 && m_strPath.GetAt(length - 1) == SLASH_WCHAR) {
		--length;
	}
	if (length != m_strPath.GetLength()) {
		m_strPath = m_strPath.Left(length);
	}
	return *this;
}

/**
 * @brief 添加末尾的反斜杠（如果是目录）
 * @return CPath& 当前对象引用
 */
CPath& CPath::AddBackslash()
{
	if (!m_strPath.IsEmpty() && m_strPath.GetLast() != SLASH_WCHAR) {
		m_strPath += SLASH_STRING;
	}
	return *this;
}

/**
 * @brief 判断路径是否以反斜杠结尾
 * @return BOOLEAN 是否以反斜杠结尾
 */
BOOLEAN CPath::HaveBackslash() const
{
	return !m_strPath.IsEmpty() && m_strPath.GetLast() == SLASH_WCHAR;
}

/**
 * @brief 移除文件扩展名
 * @return CPath& 当前对象引用
 */
CPath& CPath::RemoveExtension()
{
	auto nPos = m_strPath.ReverseFind(L'.');
	if (nPos != CSTRINGW_NOPOS) {
		m_strPath = m_strPath.Left(nPos);
	}
	return *this;
}

CPath& CPath::AppendExtension(LPCWSTR szExt, bool bReplace)
{
	if (szExt && *szExt) {
		if (bReplace) {
			RemoveExtension();
		}
		if (*szExt != L'.') {
			// 如果扩展名没有以 '.' 开头，则添加 '.'
			m_strPath += L".";
		}
		m_strPath += szExt;
	}
	return *this;
}

/**
 * @brief 获取相对路径
 * @param pRoot 可选，返回根路径
 * @return CPath 相对路径
 */
CPath CPath::GetRelative(CPath* pRoot /*= NULL*/) const
{
	CPath pRelativePath;
	USHORT nRootLen(0);
	if (HaveRoot(&nRootLen)) {
		if (nRootLen < m_strPath.GetLength()) {
			pRelativePath = m_strPath.Mid(nRootLen);
			pRelativePath.RemoveBackslash();
			if (pRoot) {
				*pRoot = m_strPath.Left(nRootLen);
			}
		}
		else {
			pRelativePath = SLASH_STRING; //根目录
			if (pRoot) {
				*pRoot = m_strPath;
			}
		}
	}
	return pRelativePath;
}