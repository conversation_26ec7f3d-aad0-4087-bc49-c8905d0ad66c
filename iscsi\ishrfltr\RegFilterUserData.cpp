﻿#include "stdafx.h"
#include "../iShareDriverLib/WindowVer.hpp"
#include "../iShareDriverLib/RegKeyPropertiesDef.h"
#include "FilterSet.h"
#include "RegFilterUserData.h"

/**
 * @brief 构造函数，初始化注册表过滤器。
 */
CRegFilterUserData::CRegFilterUserData(void)
{
}

/**
 * @brief 析构函数，释放资源。
 */
CRegFilterUserData::~CRegFilterUserData(void)
{
}

/**
* @brief 检查是否需要过滤指定的注册表路径。
* @param sRegCompletePath 完整的注册表路径
* @param bEnumSubKeyOnly 输出参数，指示是否仅过滤子键枚举操作
* @return TRUE 需要过滤，FALSE 不需要过滤
*/
BOOLEAN CRegFilterUserData::CheckNeedFilterRegPath(const CStringW& sRegCompletePath, BOOLEAN* bEnumSubKeyOnly /*= NULL*/) {
	// 初始化输出参数
	if (bEnumSubKeyOnly) {
		*bEnumSubKeyOnly = FALSE;
	}
	// 内部默认不转移：跳过用户数据根路径
	if (REGPATH_STARTWITH_EXACT(sRegCompletePath, USERDATA_REG_ROOT)) {
		return FALSE;
	}

	//默认黑名单优先级高于白名单
	if (!m_RegPathBlackDefault.IsEmpty() &&
		CheckPathInList(m_RegPathBlackDefault, sRegCompletePath, nullptr)) {
		return FALSE;
	}

	// 默认白名单
	if (!m_RegPathWhiteDefault.IsEmpty() &&
		CheckPathInList(m_RegPathWhiteDefault, sRegCompletePath, bEnumSubKeyOnly)) {
		return TRUE;
	}

	// 如果黑白名单都为空，默认需要过滤
	if (m_RegPathBlack.IsEmpty() && m_RegPathWhite.IsEmpty()) {
		return TRUE;
	}

	// 黑名单优先级高于白名单：如果在黑名单中则不过滤
	if (!m_RegPathBlack.IsEmpty()) {
		return !CheckPathInList(m_RegPathBlack, sRegCompletePath, nullptr);
	}

	// 白名单检查：只有在白名单中的路径才需要过滤
	if (!m_RegPathWhite.IsEmpty()) {
		return CheckPathInList(m_RegPathWhite, sRegCompletePath, bEnumSubKeyOnly);
	}

	// 默认不需要过滤
	return FALSE;
}

/**
 * @brief 处理创建或打开注册表键的操作。
 * @param CompleteName 注册表键的完整路径
 * @param RootObject 注册表根对象
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CRegFilterUserData::OnCreateOrOpenKey(PUNICODE_STRING CompleteName, PVOID RootObject)
{
	CStringW sRegCompletePath;
	// 获取注册表对象的完整路径
	if (CRegKey::GetRegisterObjectCompletePath(CompleteName, RootObject, sRegCompletePath) && CheckNeedFilterRegPath(sRegCompletePath)) {
		CStringW sUserDataRegPath(USERDATA_REG_ROOT);
		sUserDataRegPath += CRegKey::RemoveCompletePathPrefix(sRegCompletePath);
		// 检查目标注册表键是否存在
		if (NT_SUCCESS(IsExistRegKey(NULL, sUserDataRegPath))) {
			// 复制注册表键
			auto status = CopyRegKey(NULL, sUserDataRegPath, NULL, sRegCompletePath, TRUE, TRUE);
			if (!NT_SUCCESS(status)) {
				// 打印操作日志
				PrintRegLog(status, RootObject, L"OnCreateOrOpenKey", NULL, NULL, CompleteName);
			}
		}
	}
	return STATUS_SUCCESS;
}

/**
 * @brief 处理创建或打开注册表键的辅助函数（Ex版本）
 * @param pKeyInfo 注册表键信息（CREATE 和 OPEN 使用相同结构）
 * @param bIsCreateKey 是否为创建操作
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CRegFilterUserData::OnCreateOrOpenKeyEx(PREG_CREATE_KEY_INFORMATION pKeyInfo, BOOLEAN bIsCreateKey)
{
	// 参数有效性检查
	if (!pKeyInfo) {
		return STATUS_SUCCESS;
	}

	CStringW sRegCompletePath;
	// 获取注册表对象的完整路径并检查是否需要过滤
	BOOLEAN bEnumSubKeyOnly = FALSE;
	if (!CRegKey::GetRegisterObjectCompletePath(pKeyInfo->CompleteName, pKeyInfo->RootObject, sRegCompletePath) ||
		!CheckNeedFilterRegPath(sRegCompletePath, &bEnumSubKeyOnly)) {
		return STATUS_SUCCESS;
	}

	// 检查原始注册表键是否存在
	if (pKeyInfo->RootObject) {
		CRegKey rootKey;
		rootKey.Attach(pKeyInfo->RootObject, (POBJECT_TYPE)pKeyInfo->ObjectType, pKeyInfo->DesiredAccess);
		if (NT_SUCCESS(IsExistRegKey(rootKey, pKeyInfo->CompleteName))) {
			return STATUS_SUCCESS; // 原始键已存在，无需处理, 稍后绑定
		}
	}
	else {
		if (NT_SUCCESS(IsExistRegKey(NULL, sRegCompletePath))) {
			return STATUS_SUCCESS; // 原始键已存在，无需处理, 稍后绑定
		}
	}

	// 处理打开注册表的情况
	if (!bIsCreateKey) {
		CRegKey RegUserData;
		ULONG dwDisposition = 0;
		NTSTATUS status = CreateOrOpenUserDataRegKey(sRegCompletePath, pKeyInfo, bIsCreateKey, RegUserData, &dwDisposition);

		if (!NT_SUCCESS(status)) {
			return STATUS_SUCCESS;
		}

		// 如果能打开，但是原始键不存在，则需要创建
		auto ClassName = RegUserData.GetClassName();
		status = CreateRegKey(NULL, sRegCompletePath, REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, ClassName, pKeyInfo->SecurityDescriptor);
		if (NT_SUCCESS(status)) {
			return STATUS_SUCCESS;
		}
	}

	//CRegKey RegUserData;
	//ULONG dwDisposition = 0;
	//NTSTATUS status = CreateOrOpenUserDataRegKey(sRegCompletePath, pKeyInfo, bIsCreateKey, RegUserData, &dwDisposition);

	//if (!NT_SUCCESS(status)) {
	//	return STATUS_SUCCESS;
	//}
	//if (pKeyInfo->ResultObject) {
	//	// 获取对象引用
	//	PVOID Object = RegUserData.Detach((POBJECT_TYPE)pKeyInfo->ObjectType, pKeyInfo->DesiredAccess);
	//	if (Object) {
	//		// 返回转移的新注册表句柄
	//		pKeyInfo->GrantedAccess = pKeyInfo->DesiredAccess;
	//		if (pKeyInfo->Disposition) {
	//			*pKeyInfo->Disposition = dwDisposition;
	//		}
	//		*pKeyInfo->ResultObject = Object;
	//		// 直接返回结果给客户
	//		return STATUS_CALLBACK_BYPASS;
	//	}
	//}
	return STATUS_SUCCESS;
}

/**
 * @brief 处理注册表键创建或打开的后置操作。
 * @param pPostInfo 后置操作信息
 * @param bIsCreateKey 是否为创建操作
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CRegFilterUserData::OnPostCreateOrOpenKeyEx(PREG_POST_OPERATION_INFORMATION pPostInfo, BOOLEAN bIsCreateKey)
{
	// 参数有效性检查
	if (!pPostInfo || !NT_SUCCESS(pPostInfo->Status)) {
		return STATUS_SUCCESS;
	}

	// 获取注册表完整路径并检查是否需要过滤
	CStringW sRegCompletePath;
	BOOLEAN bEnumSubKeyOnly = FALSE;
	if (!CRegKey::GetRegisterObjectCompletePath(nullptr, pPostInfo->Object, sRegCompletePath) ||
		!CheckNeedFilterRegPath(sRegCompletePath, &bEnumSubKeyOnly)) {
		return STATUS_SUCCESS;
	}

	// 获取预操作信息
	auto pKeyInfo = static_cast<PREG_CREATE_KEY_INFORMATION>(pPostInfo->PreInformation);
	if (!pKeyInfo) {
		return STATUS_SUCCESS;
	}

	// 创建或打开用户数据注册表键
	CRegKey RegUserData;
	NTSTATUS status = CreateOrOpenUserDataRegKey(sRegCompletePath, pKeyInfo, bIsCreateKey, RegUserData);
	if (!NT_SUCCESS(status)) {
		auto statusMessage = FormatNTSTATUSMessage(status);
		PT_DBG_PRINT(PTDBG_TRACE_ROUTINES, "CRegFilterUserData CreateOrOpenUserDataRegKey failed: %wZ\n", (PUNICODE_STRING)statusMessage);
		return STATUS_SUCCESS;
	}

	// 将用户数据键对象附加到回调上下文
	auto Object = RegUserData.Detach(static_cast<POBJECT_TYPE>(pKeyInfo->ObjectType), pKeyInfo->DesiredAccess);
	if (!Object) {
		return STATUS_SUCCESS;
	}

	// 创建过滤器上下文并设置到对象
	auto pRegFilterContext = make_shared<CRegFilterContext>(Object, static_cast<POBJECT_TYPE>(pKeyInfo->ObjectType), pKeyInfo->DesiredAccess, bEnumSubKeyOnly);
	if (!pRegFilterContext) {
		return STATUS_INSUFFICIENT_RESOURCES;
	}

	auto pRegFilterContext_ptr = new CRegFilterContext_Ptr(pRegFilterContext);
	if (!pRegFilterContext_ptr) {
		return STATUS_INSUFFICIENT_RESOURCES;
	}

	status = CmSetCallbackObjectContext(pPostInfo->Object, GetCookie(), pRegFilterContext_ptr, nullptr);
	if (!NT_SUCCESS(status)) {
		delete pRegFilterContext_ptr;
		PrintRegLog(status, pPostInfo->Object, L"OnPostCreateOrOpenKeyEx");
	}

	return status;
}

/**
 * @brief 创建或打开用户数据注册表键的辅助函数
 * @param sUserDataRegPath 用户数据注册表路径
 * @param pKeyInfo 注册表键信息
 * @param bIsCreateKey 是否为创建操作
 * @param RegUserData 输出的注册表键对象
 * @param pdwDisposition 创建结果（可选）
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CRegFilterUserData::CreateOrOpenUserDataRegKey(const CStringW& sRegCompletePath, PREG_CREATE_KEY_INFORMATION pKeyInfo, BOOLEAN bIsCreateKey, CRegKey& RegUserData, PULONG pdwDisposition)
{
	NTSTATUS status;
	// 构建用户数据注册表路径
	CStringW sUserDataRegPath(USERDATA_REG_ROOT);
	sUserDataRegPath += CRegKey::RemoveCompletePathPrefix(sRegCompletePath);

	if (bIsCreateKey) {
		// 创建注册表键
		// 处理Windows 8以下版本的兼容性问题
		if (!IsWin8OrGreater() && ExGetPreviousMode() == UserMode) {
			CStringW RegClass = pKeyInfo->Class;
			status = RegUserData.Create(NULL, sUserDataRegPath, pKeyInfo->CreateOptions,
				pKeyInfo->DesiredAccess, pdwDisposition, RegClass, pKeyInfo->SecurityDescriptor);
		}
		else {
			status = RegUserData.Create(NULL, sUserDataRegPath, pKeyInfo->CreateOptions,
				pKeyInfo->DesiredAccess, pdwDisposition, pKeyInfo->Class, pKeyInfo->SecurityDescriptor);
		}
	}
	else {
		// 打开注册表键
		status = RegUserData.Open(NULL, sUserDataRegPath, pKeyInfo->DesiredAccess, pKeyInfo->SecurityDescriptor);
	}

	return status;
}

//在 Windows Server 2003 和更高版本的 Windows 操作系统上使用 此回调
NTSTATUS CRegFilterUserData::OnPostRegistryNotify(REG_NOTIFY_CLASS Action, PREG_POST_OPERATION_INFORMATION pKeyInfo) {
	NTSTATUS status = STATUS_SUCCESS;
	switch (Action) {
	case RegNtPostDeleteKey:
	case RegNtPostSetValueKey:
	case RegNtPostDeleteValueKey:
	case RegNtPostRenameKey:
	case RegNtPostEnumerateKey:
	case RegNtPostEnumerateValueKey:
	case RegNtPostQueryKey:
	case RegNtPostQueryValueKey:
	case RegNtPostQueryMultipleValueKey:
	{
		status = HandlePostRegistryOperation(pKeyInfo, Action);
	}
	break;
	case RegNtPostCreateKeyEx:
	{
		status = OnPostCreateOrOpenKeyEx(pKeyInfo, TRUE);
	}
	break;
	case RegNtPostOpenKeyEx:
	{
		status = OnPostCreateOrOpenKeyEx(pKeyInfo, FALSE);
	}
	break;
	}
	return status;
}

/**
 * @brief 处理注册表POST操作回调的通用逻辑
 * @param pPostInfo POST操作信息
 * @param Action 注册表通知类型
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CRegFilterUserData::HandlePostRegistryOperation(PREG_POST_OPERATION_INFORMATION pPostInfo, REG_NOTIFY_CLASS Action)
{
	NTSTATUS status = STATUS_SUCCESS;

	if (pPostInfo && pPostInfo->ObjectContext) {
		auto pContext = (const CRegFilterContext_Ptr*)pPostInfo->ObjectContext;
		if (*pContext) {
			// 根据操作类型调用相应的上下文方法
			switch (Action) {
			case RegNtPostDeleteKey:
				status = (*pContext)->OnPostDeleteKey(pPostInfo);
				break;
			case RegNtPostSetValueKey:
				status = (*pContext)->OnPostSetValueKey(pPostInfo);
				break;
			case RegNtPostDeleteValueKey:
				status = (*pContext)->OnPostDeleteValueKey(pPostInfo);
				break;
			case RegNtPostRenameKey:
				status = (*pContext)->OnPostRenameKey(pPostInfo);
				break;
			case RegNtPostEnumerateKey:
				status = (*pContext)->OnPostEnumerateKey(pPostInfo);
				break;
			case RegNtPostEnumerateValueKey:
				status = (*pContext)->OnPostEnumerateValueKey(pPostInfo);
				break;
			case RegNtPostQueryKey:
				status = (*pContext)->OnPostQueryKey(pPostInfo);
				break;
			case RegNtPostQueryValueKey:
				status = (*pContext)->OnPostQueryValueKey(pPostInfo);
				break;
			case RegNtPostQueryMultipleValueKey:
				status = (*pContext)->OnPostQueryMultipleValueKey(pPostInfo);
				break;
			default:
				break;
			}
		}
	}

	return status;
}

/**
 * @brief 打印注册表操作日志。
 * @param status 操作状态码
 * @param Object 注册表对象
 * @param sOperateName 操作名称
 * @param sName 注册表项名称（可选）
 * @param sValue 注册表值（可选）
 * @param CompleteName 注册表完整路径（可选）
 */
VOID CRegFilterUserData::PrintRegLog(NTSTATUS status, PVOID Object, PCWCH sOperateName, PUNICODE_STRING sName, PUNICODE_STRING sValue, PUNICODE_STRING CompleteName)
{
	CStringW sRegCompletePath;
	// 获取注册表对象的完整路径
	if (CRegKey::GetRegisterObjectCompletePath(CompleteName, Object, sRegCompletePath)) {
		if (sName && sValue) {
			PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
				"RegFilterUserData %S, status: %wZ, name: %wZ, value: %wZ, path: %wZ\n",
				sOperateName, (PUNICODE_STRING)FormatNTSTATUSMessage(status), sName, sValue, (PUNICODE_STRING)AddPropertiesNames(sRegCompletePath));
		}
		else if (sName) {
			PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
				"RegFilterUserData %S, status: %wZ, name: %wZ, path: %wZ\n",
				sOperateName, (PUNICODE_STRING)FormatNTSTATUSMessage(status), sName, (PUNICODE_STRING)AddPropertiesNames(sRegCompletePath));
		}
		else {
			PT_DBG_PRINT(PTDBG_TRACE_ROUTINES,
				"RegFilterUserData %S, status: %wZ, path: %wZ\n",
				sOperateName, (PUNICODE_STRING)FormatNTSTATUSMessage(status), (PUNICODE_STRING)AddPropertiesNames(sRegCompletePath));
		}
	}
}

/**
 * @brief 检查注册表路径是否在指定的路径列表中
 * @param pathList 路径列表
 * @param targetPath 目标路径
 * @param bEnumSubKeyOnly 输出参数，指示是否仅过滤子键枚举操作
 * @return TRUE 路径在列表中，FALSE 不在列表中
 */
BOOLEAN CRegFilterUserData::CheckPathInList(const CStringList& pathList, const CStringW& targetPath, BOOLEAN* bEnumSubKeyOnly) const
{
	return pathList.for_each_if([&](const CStringW& path) -> bool {
		// 情况1：目标路径是配置路径的子路径或完全匹配
		if (IsPathMatched(targetPath, path)) {
			return true;
		}

		// 情况2：配置路径是目标路径的子路径（仅在需要检查枚举子键时）
		if (bEnumSubKeyOnly && IsPathMatched(path, targetPath)) {
			*bEnumSubKeyOnly = TRUE;
			return true;
		}

		return false;
		});
}